#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强后的时间处理功能
验证时间格式错误修复和数据库兼容性
"""

import sys
import os
import json

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_time_handling_integration():
    """集成测试：模拟完整的时间处理流程"""
    print("=" * 80)
    print("增强时间处理功能集成测试")
    print("=" * 80)
    
    # 模拟包含错误时间格式的测试数据
    test_items = [
        {
            'test_name': '血糖',
            'test_value': '5.6',
            'collect_time': '2024-06-278:00:15',  # 错误格式
            'report_time': '2024-06-27 10:30:45'  # 正确格式
        },
        {
            'test_name': '血压',
            'test_value': '120/80',
            'collect_time': '2024-06-279:15:30',  # 错误格式
            'report_time': '2024-06-2711:45:20'   # 错误格式
        },
        {
            'test_name': '心率',
            'test_value': '72',
            'collect_time': None,                  # 空值
            'report_time': 'invalid-time'          # 无效格式
        }
    ]
    
    try:
        # 导入时间处理工具
        from script.test_result_format_ae_ocr.nodes import TimeUtils
        
        print("🔧 测试 TimeUtils.validate_and_fix_datetime 方法:")
        print("-" * 60)
        
        processed_items = []
        for i, item in enumerate(test_items, 1):
            print(f"项目 {i}: {item['test_name']}")
            
            # 处理采集时间
            original_collect = item['collect_time']
            fixed_collect = TimeUtils.validate_and_fix_datetime(original_collect, f'项目{i}_collect_time')
            
            # 处理报告时间
            original_report = item['report_time']
            fixed_report = TimeUtils.validate_and_fix_datetime(original_report, f'项目{i}_report_time')
            
            processed_item = item.copy()
            processed_item['collect_time'] = fixed_collect
            processed_item['report_time'] = fixed_report
            processed_items.append(processed_item)
            
            print(f"  采集时间: '{original_collect}' -> '{fixed_collect}'")
            print(f"  报告时间: '{original_report}' -> '{fixed_report}'")
            print()
        
        print("✅ 时间处理完成，所有时间字段现在都是有效格式或None")
        print()
        
        # 验证处理结果
        print("📊 处理结果验证:")
        print("-" * 60)
        
        valid_count = 0
        total_time_fields = 0
        
        for i, item in enumerate(processed_items, 1):
            collect_time = item['collect_time']
            report_time = item['report_time']
            
            # 验证采集时间
            total_time_fields += 1
            if collect_time is None:
                collect_status = "✅ None（安全）"
                valid_count += 1
            elif TimeUtils.is_valid_django_datetime(collect_time):
                collect_status = "✅ 有效格式"
                valid_count += 1
            else:
                collect_status = "❌ 无效格式"
            
            # 验证报告时间
            total_time_fields += 1
            if report_time is None:
                report_status = "✅ None（安全）"
                valid_count += 1
            elif TimeUtils.is_valid_django_datetime(report_time):
                report_status = "✅ 有效格式"
                valid_count += 1
            else:
                report_status = "❌ 无效格式"
            
            print(f"项目 {i}: {item['test_name']}")
            print(f"  采集时间: {collect_time} - {collect_status}")
            print(f"  报告时间: {report_time} - {report_status}")
            print()
        
        success_rate = (valid_count / total_time_fields) * 100
        print(f"🎯 验证结果: {valid_count}/{total_time_fields} 个时间字段有效 ({success_rate:.1f}%)")
        
        if success_rate == 100:
            print("✅ 所有时间字段都是安全的，可以避免数据库插入错误！")
            return True
        else:
            print("❌ 仍有无效的时间字段，可能导致数据库插入失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_original_error_case():
    """测试原始错误案例的修复"""
    print("\n" + "=" * 80)
    print("原始错误案例专项测试")
    print("=" * 80)
    
    try:
        from script.test_result_format_ae_ocr.nodes import TimeUtils
        
        # 原始错误案例
        error_cases = [
            "2024-06-278:00:15",
            "2024-06-279:30:45", 
            "2024-06-2710:5:30",
            "2024-06-2710:30:5"
        ]
        
        print("🎯 测试原始错误时间格式修复:")
        print("-" * 60)
        
        all_fixed = True
        for case in error_cases:
            fixed = TimeUtils.validate_and_fix_datetime(case, "error_case")
            is_valid = fixed and TimeUtils.is_valid_django_datetime(fixed)
            
            status = "✅ 修复成功" if is_valid else "❌ 修复失败"
            if not is_valid:
                all_fixed = False
                
            print(f"'{case}' -> '{fixed}' - {status}")
        
        print()
        if all_fixed:
            print("✅ 所有原始错误案例都已成功修复！")
            return True
        else:
            print("❌ 部分原始错误案例修复失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始增强时间处理功能测试")
    
    # 运行集成测试
    integration_success = test_time_handling_integration()
    
    # 运行原始错误案例测试
    error_case_success = test_original_error_case()
    
    # 总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    if integration_success and error_case_success:
        print("🎉 所有测试通过！时间处理功能增强成功")
        print("💡 现在可以安全处理各种时间格式错误，避免数据库插入失败")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
