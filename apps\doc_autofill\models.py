
from django.db import models
from django.utils.translation import gettext_lazy as _

from common.models import BaseModel, BaseFileModel


class HospDocTmplCenter(BaseModel):

    class StatusCodeEnum(models.TextChoices):
        UNRELEASED = 'UNRELEASED', '未发布'
        RELEASED = 'RELEASED', '已发布'

        # 枚举值转义，找不到默认原code
        @staticmethod
        def get_status_name(status_code):
            try:
                return HospDocTmplCenter.StatusCodeEnum(status_code).label if status_code else status_code
            except ValueError:
                return status_code
    """中心模板"""
    # hosp_id = models.CharField(max_length=255, verbose_name="医院ID（h+hosp_code）", db_index=True, unique=True)
    # hosp_code = models.CharField(max_length=255, default="", verbose_name="中心编码", db_index=True)
    # hosp_name = models.CharField(max_length=100, default="", verbose_name="中心名称")

    status_code = models.CharField(max_length=20, default="", verbose_name="中心模板状态 UNRELEASED 未发布，RELEASED 已发布")
    total_amount = models.IntegerField(default=0, verbose_name="已上传文件清单个数")
    project_used_amount = models.IntegerField(default=0, verbose_name="已上传文件清单个数")

    class Meta:
        db_table = 'hosp_doc_tmpl_center'
        verbose_name = verbose_name_plural = "中心模板"


class HospDocTmplFile(BaseModel, BaseFileModel):
    """中心模板文件"""

    class Meta:
        db_table = 'hosp_doc_tmpl_file'
        verbose_name = verbose_name_plural = "中心模板文件"


class HospDocTmplFileProcessed(BaseModel, BaseFileModel):
    """中心模板文件（AI处理加工过的）"""

    class Meta:
        db_table = 'hosp_doc_tmpl_file_processed'
        verbose_name = verbose_name_plural = "中心模板文件（AI处理加工过的）"


class HospDocTmplInfo(BaseModel):

    class TmplTypeEnum(models.TextChoices):
        BLANK_TMPL = 'BLANK_TMPL', '空白模板'
        AI_TMPL = 'AI_TMPL', 'AI模板'
    # 系统模板状态
    class StatusCodeEnum(models.TextChoices):
        NOT_CREATED = 'NOT_CREATED', '未生成'
        CREATING = 'CREATING', '生成中'
        COMPLETED = 'COMPLETED', '已生成'

        # 枚举值转义，找不到默认原code
        @staticmethod
        def get_status_name(status_code):
            try:
                return HospDocTmplInfo.StatusCodeEnum(status_code).label if status_code else status_code
            except ValueError:
                return status_code

    # 中心项目文件审核状态
    class AuditStatusCodeEnum(models.TextChoices):
        UNAPPROVED = 'UNAPPROVED', '待审核'
        APPROVED = 'APPROVED', '审核通过'
        # REJECT = 'REJECT', '审核驳回'

        # 枚举值转义，找不到默认原code
        @staticmethod
        def get_audit_status_name(audit_status_code):
            try:
                return HospDocTmplInfo.AuditStatusCodeEnum(audit_status_code).label if audit_status_code else audit_status_code
            except ValueError:
                return audit_status_code
    """中心模板文件信息"""
    # hosp_id = models.CharField(max_length=255, verbose_name="医院ID（h+hosp_code）", db_index=True)
    # hosp_code = models.CharField(max_length=255, default="", verbose_name="中心编码", db_index=True)

    file_category = models.CharField(max_length=50, default="", verbose_name="文件类别,暂时未启用")
    file_type = models.CharField(max_length=50, default="", verbose_name="文件类型,暂时未启用")
    file_name = models.CharField(max_length=100, default="", verbose_name="文件名称")
    file_extension = models.CharField(max_length=10, default="", verbose_name="文件格式")
    # tmpl_file_id = models.BigIntegerField(verbose_name="模板文件ID")
    # sys_tmpl_file_id = models.BigIntegerField(null=True, verbose_name="系统模板文件ID")
    status_code = models.CharField(max_length=20, default="",
                                   verbose_name="系统模板状态 NOT_CREATED 未生成 CREATING 生成中 ,COMPLETED 已生成")
    audit_status_code = models.CharField(
        max_length=20, default="", verbose_name="中心项目文件审核状态 UNAPPROVED 待审核，APPROVED 审核通过，REJECT 审核驳回")

    hospital = models.ForeignKey(
        'hospital.Hospital',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_tmpl_infos',
        to_field='hosp_id',
        db_column='hosp_id',
        verbose_name="医院ID",
        db_index=True
    )

    tmpl_file = models.OneToOneField(
        HospDocTmplFile,
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_tmpl_info',
        to_field='id',
        db_column='tmpl_file_id',
        verbose_name="中心模板文件ID",
        db_index=True,
        null=True
    )

    tmpl_file_processed = models.OneToOneField(
        HospDocTmplFileProcessed,
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_tmpl_info',
        to_field='id',
        db_column='tmpl_file_processed_id',
        verbose_name="中心模板文件ID",
        db_index=True,
        null=True
    )

    class Meta:
        db_table = 'hosp_doc_tmpl_info'
        verbose_name = verbose_name_plural = "中心模板文件信息"


class ProjectSiteDocCenter(BaseModel):
    # 项目文件状态
    class StatusCodeEnum(models.TextChoices):
        PROCESSING = 'PROCESSING', '进行中'
        COMPLETED = 'COMPLETED', '已完成'

        # 枚举值转义，找不到默认原code
        @staticmethod
        def get_status_name(status_code):
            try:
                return ProjectSiteDocCenter.StatusCodeEnum(status_code).label if status_code else status_code
            except ValueError:
                return status_code

    """项目中心"""
    # project_id = models.CharField(max_length=255, verbose_name="项目ID", db_index=True)
    # project_no = models.CharField(max_length=255, default="", verbose_name="项目编号", db_index=True)
    # project_site_id = models.CharField(max_length=255, unique=True, verbose_name="项目研究中心ID", db_index=True)

    # hosp_id = models.CharField(max_length=255, verbose_name="医院ID（h+hosp_code）", db_index=True)
    # hosp_code = models.CharField(max_length=255, default="", verbose_name="中心编码", db_index=True)

    status_code = models.CharField(max_length=20, default="", verbose_name="项目文件状态 PROCESSING 进行中，COMPLETED 已完成")
    total_amount = models.IntegerField(default=0, verbose_name="项目文总数")
    complete_amount = models.IntegerField(default=0, verbose_name="项目文件完成数")

    project = models.ForeignKey(
        'project.Project',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_centers',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )

    project_site = models.ForeignKey(
        'project.ProjectSite',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_centers',
        to_field='project_site_id',
        db_column='project_site_id',
        verbose_name="项目中心ID",
        db_index=True
    )

    hospital = models.ForeignKey(
        'hospital.Hospital',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_centers',
        to_field='hosp_id',
        db_column='hosp_id',
        verbose_name="医院ID",
        db_index=True
    )

    class Meta:
        db_table = 'project_site_doc_center'
        verbose_name = verbose_name_plural = "项目中心文件"


class ProjectSiteDocFile(BaseModel, BaseFileModel):
    """项目中心文件"""

    class Meta:
        db_table = 'project_site_doc_file'
        verbose_name = verbose_name_plural = "项目中心文件"


class ProjectSiteDocInfo(BaseModel):
    """项目中心文件信息"""

    class AuditStatusCodeEnum(models.TextChoices):
        UNAPPROVED = 'UNAPPROVED', '待审核'
        APPROVED = 'APPROVED', '审核通过'
        REJECT = 'REJECT', '审核驳回'

        # 枚举值转义，找不到默认原code
        @staticmethod
        def get_audit_status_name(audit_status_code):
            try:
                return ProjectSiteDocInfo.AuditStatusCodeEnum(audit_status_code).label if audit_status_code else audit_status_code
            except ValueError:
                return audit_status_code

    class StatusCodeEnum(models.TextChoices):
        NOT_CREATED = 'NOT_CREATED', '未生成'
        CREATING = 'CREATING', '生成中'
        COMPLETED = 'COMPLETED', '已生成'

        # 枚举值转义，找不到默认原code
        @staticmethod
        def get_status_name(status_code):
            try:
                return ProjectSiteDocInfo.StatusCodeEnum(status_code).label if status_code else status_code
            except ValueError:
                return status_code

    # project_id = models.CharField(max_length=255, verbose_name="项目ID", db_index=True)
    # project_no = models.CharField(max_length=255, default="", verbose_name="项目编号", db_index=True)
    # project_site_id = models.CharField(max_length=255, unique=True, verbose_name="项目研究中心ID", db_index=True)

    # hosp_id = models.CharField(max_length=255, verbose_name="医院ID（h+hosp_code）", db_index=True)
    # hosp_code = models.CharField(max_length=255, default="", verbose_name="中心编码", db_index=True)

    file_category = models.CharField(max_length=50, default="", verbose_name="文件类别,暂时未启用")
    file_type = models.CharField(max_length=50, default="", verbose_name="文件类型,暂时未启用")
    file_name = models.CharField(max_length=100, default="", verbose_name="文件名称")
    file_extension = models.CharField(max_length=10, default="", verbose_name="文件格式")
    # sys_tmpl_file_id = models.BigIntegerField(verbose_name="中心模板文件ID")
    # doc_file_id = models.BigIntegerField(null=True, verbose_name="项目中心文件ID")
    status_code = models.CharField(max_length=20, choices=StatusCodeEnum.choices,
                                   default=StatusCodeEnum.NOT_CREATED, verbose_name="项目文件生成状态")
    audit_status_code = models.CharField(max_length=20, choices=AuditStatusCodeEnum.choices,
                                         default=AuditStatusCodeEnum.UNAPPROVED, verbose_name="中心项目文件审核状态")
    auditor_id = models.CharField(max_length=255, default="", verbose_name="审核人工号")
    auditor_name = models.CharField(max_length=255, default="", verbose_name="审核人名称")

    project = models.ForeignKey(
        'project.Project',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_infos',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )

    project_site = models.ForeignKey(
        'project.ProjectSite',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_infos',
        to_field='project_site_id',
        db_column='project_site_id',
        verbose_name="项目中心ID",
        db_index=True
    )

    hospital = models.ForeignKey(
        'hospital.Hospital',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_infos',
        to_field='hosp_id',
        db_column='hosp_id',
        verbose_name="医院ID",
        db_index=True
    )

    doc_file = models.OneToOneField(
        ProjectSiteDocFile,
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_info',
        to_field='id',
        db_column='doc_file_id',
        verbose_name="项目中心文件ID",
        db_index=True,
        null=True
    )

    class Meta:
        db_table = 'project_site_doc_info'
        verbose_name = verbose_name_plural = "项目中心文件信息"


class DocAutofillTask(BaseModel):
    """文件自动回填任务"""

    class Category(models.TextChoices):
        GEN_SYS_TMPL = 'GEN_SYS_TMPL', '生成系统模板'
        GEN_PROJECT_FILE = 'GEN_PROJECT_FILE', '生成项目文件'

    # 任务执行状态
    class StatusEnum(models.TextChoices):
        TODO = 'TODO', '待办'
        IN_PROGRESS = 'IN_PROGRESS', '进行中'
        COMPLETED = 'COMPLETED', '已完成'
        CANCELLED = 'CANCELLED', '已取消'
        ERROR = 'ERROR', '执行错误'
    # project_id = models.CharField(max_length=255, verbose_name="项目ID", db_index=True)
    # project_no = models.CharField(max_length=255, default="", verbose_name="项目编号", db_index=True)
    # project_site_id = models.CharField(max_length=255, unique=True, verbose_name="项目研究中心ID", db_index=True)

    # hosp_id = models.CharField(max_length=255, verbose_name="医院ID（h+hosp_code）", db_index=True)
    # hosp_code = models.CharField(max_length=255, default="", verbose_name="中心编码", db_index=True)

    project = models.ForeignKey(
        'project.Project',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_autofill_tasks',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True,
        null=True,
        blank=True
    )

    project_site = models.ForeignKey(
        'project.ProjectSite',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_autofill_tasks',
        to_field='project_site_id',
        db_column='project_site_id',
        verbose_name="项目中心ID",
        db_index=True,
        null=True,
        blank=True
    )

    hospital = models.ForeignKey(
        'hospital.Hospital',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_autofill_tasks',
        to_field='hosp_id',
        db_column='hosp_id',
        verbose_name="医院ID",
        db_index=True
    )

    hosp_doc_tmpl = models.ForeignKey(
        'HospDocTmplInfo',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_autofill_tasks',
        to_field='id',
        db_column='tmpl_info_id',
        verbose_name="中心模板文件信息ID",
        db_index=True,
        null=True
    )

    project_site_doc = models.ForeignKey(
        'ProjectSiteDocInfo',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='doc_autofill_tasks',
        to_field='id',
        db_column='doc_info_id',
        verbose_name="项目中心文件信息ID",
        db_index=True,
        null=True
    )

    name = models.CharField(max_length=255, default="", verbose_name="任务名称")
    category = models.CharField(max_length=20, choices=Category.choices, default="", verbose_name="任务分类")
    description = models.TextField(default="", verbose_name="任务描述")
    status = models.CharField(max_length=20, default="", verbose_name="任务状态")
    start_time = models.DateTimeField(null=True, blank=True,verbose_name="开始时间")
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="结束时间")

    class Meta:
        db_table = 'doc_autofill_task'
        verbose_name = verbose_name_plural = "文件自动回填任务"

class FileBizTypeEnum(models.TextChoices):
    SITE_SYS_TMPL = 'SITE_SYS_TMPL', '中心系统模板'
    SITE_PROJECT_FILE = 'SITE_PROJECT_FILE', '中心项目文件'