# -*- coding: utf-8 -*-
"""
大模型调用日志装饰器
支持异步/并发场景，自动记录模型调用日志到数据库
日志配置字段直接作为函数参数，提供更好的IDE提示
"""
import time
import logging
import threading
from functools import wraps
from typing import Optional, Dict, Any, Callable
from datetime import datetime

logger = logging.getLogger(__name__)

def log_llm_invocation(model_name: str = None):
    """
    大模型调用日志装饰器
    
    Args:
        model_name: 模型名称，如果不指定则从函数名自动推断
    
    Usage:
        @log_llm_invocation(model_name='DeepSeek-R1-Distill-Qwen-32B')
        def my_llm_function(prompt, **kwargs):
            return some_llm_call(prompt)
        
        response = my_llm_function(
            prompt="你好",
            task_id=123,
            create_user="user1", 
            create_name="用户1",
            business_id="biz_001",
            category="AE_TEST_REPORT"
        )
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 提取日志相关参数 - 直接从kwargs中获取
            task_id = kwargs.pop('task_id', None)
            create_user = kwargs.pop('create_user', None)
            create_name = kwargs.pop('create_name', None)
            business_id = kwargs.pop('business_id', None)
            category = kwargs.pop('category', None) 
            log_model_name = kwargs.pop('model_name', None)  # 可以覆盖装饰器的model_name
            enable_logging = kwargs.pop('enable_logging', True)  # 🎯 新增：控制是否记录日志，默认True
            
            # 🎯 如果明确禁用日志记录，直接调用原函数
            if not enable_logging:
                logger.debug("日志记录已禁用 (enable_logging=False)")
                return func(*args, **kwargs)
            
            # 如果没有提供必需的日志参数，跳过日志记录
            if not all([task_id, create_user, create_name, business_id, category]):
                missing = []
                if not task_id: missing.append('task_id')
                if not create_user: missing.append('create_user')
                if not create_name: missing.append('create_name')
                if not business_id: missing.append('business_id')
                if not category: missing.append('category')
                
                if missing:
                    logger.debug(f"跳过日志记录，缺少参数: {missing}")
                
                # 直接调用原函数，不记录日志
                return func(*args, **kwargs)[0]
            
            # 记录开始时间
            start_time = datetime.now()
            start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
            
            # 提取prompt参数
            prompt = args[0] if args else kwargs.get('content', '')
            
            try:
                # 调用原函数
                result = func(*args, **kwargs)
                
                # 记录结束时间
                end_time = datetime.now()
                end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
                
                # 保存成功日志
                _save_success_log(
                    func_name=func.__name__,
                    prompt=prompt,
                    response=result,
                    start_time=start_time_str,
                    end_time=end_time_str,
                    task_id=task_id,
                    create_user=create_user,
                    create_name=create_name,
                    business_id=business_id,
                    category=category,
                    log_model_name=log_model_name,
                    decorator_model_name=model_name
                )
                
                # 如果result是元组(content, full_response)，只返回content给调用者
                if isinstance(result, tuple) and len(result) == 2:
                    return result[0]  # 只返回content字符串
                
                return result
                
            except Exception as e:
                # # 记录结束时间
                # end_time = datetime.now()
                # end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
                
                # # 保存错误日志
                # _save_error_log(
                #     func_name=func.__name__,
                #     prompt=prompt,
                #     error=str(e),
                #     start_time=start_time_str,
                #     end_time=end_time_str,
                #     task_id=task_id,
                #     create_user=create_user,
                #     create_name=create_name,
                #     business_id=business_id,
                #     category=category,
                #     log_model_name=log_model_name,
                #     decorator_model_name=model_name
                # )
                
                # 重新抛出异常
                raise e
                
        return wrapper
    return decorator


def _save_success_log(func_name: str, prompt: str, response: str, 
                     start_time: str, end_time: str,
                     task_id: Any, create_user: str, create_name: str, 
                     business_id: Any, category: str,
                     log_model_name: str = None, decorator_model_name: str = None):
    """保存成功调用的日志"""
    try:
        # 延迟导入避免循环依赖
        from apps.system.models import ModelInvocationLog
        from common.tools import activate_conn
        from django.db import connection
        
        # 激活数据库连接
        activate_conn(connection)
        
        # 解析响应获取token信息和文本内容
        generated_tokens, output_text, think = _parse_llm_response_for_logging(response)
        
        # 确定最终的model_name - 优先级：参数传入 > 装饰器参数 > 函数名推断
        final_model_name = (
            log_model_name or 
            decorator_model_name or 
            _extract_model_name_from_function(func_name)
        )
        
        # 构建完整的日志记录，确保所有必需字段都存在
        log_data = {
            # 必需字段 - 从参数获取
            'task_id': task_id,
            'create_user': create_user,
            'create_name': create_name,
            'business_id': business_id,
            'category': category,
            'model_name': final_model_name,
            'start_time': start_time,
            'end_time': end_time,
            
            # 必需字段 - 从响应解析获取
            'input_text': prompt,  # 使用原始prompt
            'think_text': think or '',  # 思考过程，可能为空
            'output_text': output_text or response,  # 如果解析失败，使用原始response
            'prompt_tokens': generated_tokens.get('prompt_tokens', 0),
            'completion_tokens': generated_tokens.get('completion_tokens', 0)
        }
        
        # 验证所有必需字段都不为None
        none_fields = [k for k, v in log_data.items() if v is None]
        if none_fields:
            logger.warning(f"日志数据中存在None值字段: {none_fields}")
            # 为None字段设置默认值
            for field in none_fields:
                if field in ['task_id', 'business_id']:
                    log_data[field] = 0
                elif field in ['create_user', 'create_name', 'category', 'model_name', 'input_text', 'think_text', 'output_text']:
                    log_data[field] = ''
                elif field in ['prompt_tokens', 'completion_tokens']:
                    log_data[field] = 0
        
        # 保存到数据库
        ModelInvocationLog.objects.create(**log_data)
        logger.debug(f"成功保存模型调用日志: {func_name} -> {final_model_name}")
        
    except Exception as e:
        logger.error(f"保存成功日志失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")


# def _save_error_log(func_name: str, prompt: str, error: str,
    #                start_time: str, end_time: str,
    #                task_id: Any, create_user: str, create_name: str,
    #                business_id: Any, category: str,
    #                log_model_name: str = None, decorator_model_name: str = None):
    # """保存失败调用的日志"""
    # try:
    #     # 延迟导入避免循环依赖
    #     from apps.system.models import ModelInvocationLog
    #     from common.tools import activate_conn
    #     from django.db import connection
        
    #     # 激活数据库连接
    #     activate_conn(connection)
        
    #     # 确定最终的model_name - 优先级：参数传入 > 装饰器参数 > 函数名推断
    #     final_model_name = (
    #         log_model_name or 
    #         decorator_model_name or 
    #         _extract_model_name_from_function(func_name)
    #     )
        
    #     # 构建完整的错误日志记录，确保所有必需字段都存在
    #     log_data = {
    #         # 必需字段 - 从参数获取
    #         'task_id': task_id,
    #         'create_user': create_user,
    #         'create_name': create_name,
    #         'business_id': business_id,
    #         'category': category,
    #         'model_name': final_model_name,
    #         'start_time': start_time,
    #         'end_time': end_time,
            
    #         # 必需字段 - 错误相关
    #         'input_text': prompt or '',
    #         'think_text': '',  # 错误情况下没有思考过程
    #         'output_text': f'ERROR: {error}',
    #         'prompt_tokens': 0,  # 错误情况下无法统计token
    #         'completion_tokens': 0
    #     }
        
    #     # 验证所有必需字段都不为None
    #     none_fields = [k for k, v in log_data.items() if v is None]
    #     if none_fields:
    #         logger.warning(f"错误日志数据中存在None值字段: {none_fields}")
    #         # 为None字段设置默认值
    #         for field in none_fields:
    #             if field in ['task_id', 'business_id']:
    #                 log_data[field] = 0
    #             elif field in ['create_user', 'create_name', 'category', 'model_name', 'input_text', 'think_text', 'output_text']:
    #                 log_data[field] = ''
    #             elif field in ['prompt_tokens', 'completion_tokens']:
    #                 log_data[field] = 0
        
    #     # 保存到数据库
    #     ModelInvocationLog.objects.create(**log_data)
    #     logger.debug(f"成功保存模型错误日志: {func_name} -> {final_model_name}")
        
    # except Exception as e:
    #     logger.error(f"保存错误日志失败: {e}")
    #     import traceback
    #     logger.error(f"详细错误信息: {traceback.format_exc()}")


def _parse_llm_response_for_logging(response) -> tuple:
    """
    LLM响应解析函数
    
    Args:
        response: LLM的响应，可能是字符串或元组(content, full_response)
        
    Returns:
        tuple: (generated_tokens, output_text, think_text)
    """
    import json
    import re
    
    # 默认返回值
    tokens = {'prompt_tokens': 0, 'completion_tokens': 0}
    output_text = ''
    think_text = ''
    
    try:
        # 检查是否是元组(content, full_response)
        if isinstance(response, tuple) and len(response) == 2:
            content_text, full_response = response
            
            # 使用完整响应提取token信息
            if isinstance(full_response, dict):
                # 提取文本内容
                output_text = content_text
                
                # 提取思考过程
                think_pattern = re.search(r'<think>\s*(.*?)\s*</think>', content_text, re.DOTALL)
                if think_pattern:
                    think_text = think_pattern.group(1).strip()
                    # 移除思考过程，只保留最终输出
                    output_text = re.sub(r'<think>.*?</think>', '', content_text, flags=re.DOTALL).strip()
                else:
                    think_text = ''
                
                # 提取token使用信息
                if 'usage' in full_response:
                    tokens = full_response['usage']
            else:
                output_text = str(content_text)
                
        # 如果是普通字符串，尝试解析为JSON
        elif isinstance(response, str):
            try:
                result = json.loads(response)
                
                # 提取文本内容
                content = result["choices"][0]["message"]["content"]
                output_text = content
                
                # 提取思考过程
                think_pattern = re.search(r'<think>\s*(.*?)\s*</think>', content, re.DOTALL)
                if think_pattern:
                    think_text = think_pattern.group(1).strip()
                    output_text = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL).strip()
                else:
                    think_text = ''
                
                # 提取token使用信息
                if 'usage' in result:
                    tokens = result['usage']
                    
            except (json.JSONDecodeError, KeyError, IndexError):
                # 如果不是JSON，直接作为文本处理
                # 尝试提取思考过程
                think_pattern = re.search(r'<think>\s*(.*?)\s*</think>', response, re.DOTALL)
                if think_pattern:
                    think_text = think_pattern.group(1).strip()
                    output_text = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL).strip()
                else:
                    output_text = response
        else:
            # 其他类型，转换为字符串
            output_text = str(response)
                    
    except Exception as e:
        # 解析失败时使用默认值
        output_text = str(response) if response else ''
        logger.debug(f"解析LLM响应失败: {e}")
    
    return tokens, output_text, think_text


def _extract_model_name_from_function(func_name: str) -> str:
    """从函数名推断模型名称"""
    model_name_mapping = {
        'deepseek_r1_distill_qwen_32b': 'DeepSeek-R1-Distill-Qwen-32B',
        'qwen3-32b': 'qwen3-32b',
        'call_llm': 'Unknown'
    }
    
    return model_name_mapping.get(func_name, func_name)