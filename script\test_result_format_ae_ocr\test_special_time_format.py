#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特殊时间格式修复
专门测试 2024-07-0213:42 这种格式
"""

import re
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class TimeUtils:
    """时间格式处理工具类"""

    @staticmethod
    def attempt_time_fix(time_str):
        """修复无空格的时间格式"""
        import re

        if not time_str or not time_str.strip():
            return None

        time_str = time_str.strip()

        # 首先检查是否已经是有效的时间格式
        if TimeUtils.is_valid_django_datetime(time_str):
            return time_str

        # 尝试修复各种常见的时间格式问题
        fixed_time = None

        # 修复1: 无空格格式 YYYY-MM-DD[H]H:MM:SS -> YYYY-MM-DD HH:MM:SS (支持单位数时间组件)
        no_space_pattern_with_seconds = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2}:\d{1,2})$'
        match = re.match(no_space_pattern_with_seconds, time_str)
        if match:
            date_part, time_part = match.groups()
            # 标准化时间部分，确保都是两位数
            time_components = time_part.split(':')
            normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
            fixed_time = f"{date_part} {normalized_time}"

        # 修复2: 无空格格式 YYYY-MM-DD[H]H:MM -> YYYY-MM-DD HH:MM (支持单位数时间组件)
        if not fixed_time:
            no_space_pattern = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2})$'
            match = re.match(no_space_pattern, time_str)
            if match:
                date_part, time_part = match.groups()
                # 标准化时间部分，确保都是两位数
                time_components = time_part.split(':')
                normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
                fixed_time = f"{date_part} {normalized_time}"

        # 修复3: 特殊无空格格式 YYYY-MM-DDHH:MM -> YYYY-MM-DD HH:MM (处理日期和时间直接连接的情况)
        if not fixed_time:
            # 匹配 YYYY-MM-DD 后面直接跟 HH:MM 或 H:MM 的格式
            special_pattern = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{2})$'
            match = re.match(special_pattern, time_str)
            if match:
                date_part, time_part = match.groups()
                # 标准化时间部分，确保小时是两位数
                time_components = time_part.split(':')
                normalized_time = ':'.join([time_components[0].zfill(2), time_components[1]])
                fixed_time = f"{date_part} {normalized_time}"
                logger.debug(f"特殊格式修复: '{time_str}' -> '{fixed_time}'")

        # 修复4: 特殊无空格格式 YYYY-MM-DDHH:MM:SS -> YYYY-MM-DD HH:MM:SS
        if not fixed_time:
            special_pattern_with_seconds = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{2}:\d{2})$'
            match = re.match(special_pattern_with_seconds, time_str)
            if match:
                date_part, time_part = match.groups()
                # 标准化时间部分，确保小时是两位数
                time_components = time_part.split(':')
                normalized_time = ':'.join([time_components[0].zfill(2), time_components[1], time_components[2]])
                fixed_time = f"{date_part} {normalized_time}"
                logger.debug(f"特殊格式修复: '{time_str}' -> '{fixed_time}'")

        # 验证修复结果
        if fixed_time and TimeUtils.is_valid_django_datetime(fixed_time):
            logger.debug(f"时间修复成功: '{time_str}' -> '{fixed_time}'")
            return fixed_time
        
        # 无法修复，记录警告并返回None
        logger.warning(f"无法修复时间格式: '{time_str}'，将设置为None")
        return None

    @staticmethod
    def is_valid_django_datetime(time_str):
        """验证时间字符串是否符合Django DateTimeField要求"""
        if not time_str or not isinstance(time_str, str):
            return False
        
        try:
            from datetime import datetime
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M',
                '%Y-%m-%d',
            ]
            
            for fmt in formats:
                try:
                    datetime.strptime(time_str, fmt)
                    return True
                except ValueError:
                    continue
            return False
        except Exception:
            return False

def test_special_time_formats():
    """测试特殊时间格式修复"""
    print("=" * 80)
    print("特殊时间格式修复测试")
    print("=" * 80)
    
    # 重点测试用例
    test_cases = [
        # 原始问题案例
        ("2024-07-0213:42", "2024-07-02 13:42", "原始问题：日期和时间直接连接"),
        
        # 类似格式
        ("2024-07-029:30", "2024-07-02 09:30", "单位数小时"),
        ("2024-07-0213:42:15", "2024-07-02 13:42:15", "带秒的格式"),
        ("2024-07-029:30:45", "2024-07-02 09:30:45", "单位数小时带秒"),
        
        # 原有支持的格式（确保不被破坏）
        ("2024-06-278:00:15", "2024-06-27 08:00:15", "原有格式1"),
        ("2024-06-279:30:45", "2024-06-27 09:30:45", "原有格式2"),
        ("2024-06-2710:5:30", "2024-06-27 10:05:30", "原有格式3"),
        
        # 已经正确的格式
        ("2024-07-02 13:42", "2024-07-02 13:42", "已正确格式"),
        
        # 无效格式
        ("invalid-time", None, "无效格式"),
        ("2024-13-45 25:70:80", None, "无效日期时间"),
    ]
    
    print("🔧 测试特殊时间格式修复:")
    print("-" * 60)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (input_time, expected_output, description) in enumerate(test_cases, 1):
        print(f"\n{i:2d}. {description}")
        print(f"    输入: {input_time}")
        print(f"    期望: {expected_output}")
        
        result = TimeUtils.attempt_time_fix(input_time)
        print(f"    输出: {result}")
        
        # 判断是否成功
        if result == expected_output:
            success_count += 1
            print(f"    状态: ✅ 成功")
        else:
            print(f"    状态: ❌ 失败")
    
    print("\n" + "=" * 80)
    print(f"测试结果: {success_count}/{total_count} 成功 ({success_count/total_count*100:.1f}%)")
    
    if success_count >= total_count * 0.9:  # 90%成功率
        print("🎉 特殊时间格式修复成功！")
        print("💡 现在可以正确处理 2024-07-0213:42 这种格式")
        return True
    else:
        print("⚠️  特殊时间格式修复仍有问题")
        return False

def test_regex_patterns():
    """测试正则表达式模式"""
    print("\n" + "=" * 80)
    print("正则表达式模式测试")
    print("=" * 80)
    
    test_string = "2024-07-0213:42"
    
    patterns = [
        (r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2}:\d{1,2})$', "原有模式1（带秒）"),
        (r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2})$', "原有模式2（无秒）"),
        (r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{2})$', "新增模式3（特殊格式）"),
        (r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{2}:\d{2})$', "新增模式4（特殊格式带秒）"),
    ]
    
    print(f"测试字符串: {test_string}")
    print("-" * 60)
    
    for i, (pattern, description) in enumerate(patterns, 1):
        match = re.match(pattern, test_string)
        if match:
            date_part, time_part = match.groups()
            print(f"{i}. {description}")
            print(f"   模式: {pattern}")
            print(f"   匹配: ✅ 成功")
            print(f"   日期部分: {date_part}")
            print(f"   时间部分: {time_part}")
            print()
        else:
            print(f"{i}. {description}")
            print(f"   模式: {pattern}")
            print(f"   匹配: ❌ 失败")
            print()

def main():
    """主测试函数"""
    print("🚀 开始特殊时间格式修复测试\n")
    
    # 运行正则表达式测试
    test_regex_patterns()
    
    # 运行格式修复测试
    format_success = test_special_time_formats()
    
    # 总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    if format_success:
        print("🎉 特殊时间格式修复测试通过！")
        print("✅ 现在可以正确处理 2024-07-0213:42 格式")
        print("✅ 原有格式支持保持不变")
        print("\n💡 修复的关键:")
        print("  - 添加了新的正则表达式模式")
        print("  - 支持日期和时间直接连接的格式")
        print("  - 保持向后兼容性")
        return True
    else:
        print("⚠️  特殊时间格式修复测试失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
