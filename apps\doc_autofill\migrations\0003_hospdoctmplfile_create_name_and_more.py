# Generated by Django 4.1.5 on 2025-04-23 10:14

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("doc_autofill", "0002_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="hospdoctmplfile",
            name="create_name",
            field=models.CharField(
                max_length=255, null=True, verbose_name="创建人姓名"
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplfile",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_index=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="hospdoctmplfile",
            name="create_user",
            field=models.CharField(
                max_length=255, null=True, verbose_name="创建人工号"
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplfile",
            name="data_version",
            field=models.PositiveIntegerField(default=0, verbose_name="数据版本"),
        ),
        migrations.AddField(
            model_name="hospdoctmplfile",
            name="delete_flag",
            field=models.SmallIntegerField(
                choices=[(0, "未删除"), (1, "已删除")],
                db_index=True,
                default=0,
                verbose_name="删除标志（0：未删除；1：已删除）",
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplfile",
            name="update_name",
            field=models.CharField(
                max_length=255, null=True, verbose_name="更新人姓名"
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplfile",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_index=True, verbose_name="更新时间"
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplfile",
            name="update_user",
            field=models.CharField(
                max_length=255, null=True, verbose_name="更新人工号"
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplfileprocessed",
            name="create_name",
            field=models.CharField(
                max_length=255, null=True, verbose_name="创建人姓名"
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplfileprocessed",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_index=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="hospdoctmplfileprocessed",
            name="create_user",
            field=models.CharField(
                max_length=255, null=True, verbose_name="创建人工号"
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplfileprocessed",
            name="data_version",
            field=models.PositiveIntegerField(default=0, verbose_name="数据版本"),
        ),
        migrations.AddField(
            model_name="hospdoctmplfileprocessed",
            name="delete_flag",
            field=models.SmallIntegerField(
                choices=[(0, "未删除"), (1, "已删除")],
                db_index=True,
                default=0,
                verbose_name="删除标志（0：未删除；1：已删除）",
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplfileprocessed",
            name="update_name",
            field=models.CharField(
                max_length=255, null=True, verbose_name="更新人姓名"
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplfileprocessed",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_index=True, verbose_name="更新时间"
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplfileprocessed",
            name="update_user",
            field=models.CharField(
                max_length=255, null=True, verbose_name="更新人工号"
            ),
        ),
        migrations.AddField(
            model_name="projectsitedocfile",
            name="create_name",
            field=models.CharField(
                max_length=255, null=True, verbose_name="创建人姓名"
            ),
        ),
        migrations.AddField(
            model_name="projectsitedocfile",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_index=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="projectsitedocfile",
            name="create_user",
            field=models.CharField(
                max_length=255, null=True, verbose_name="创建人工号"
            ),
        ),
        migrations.AddField(
            model_name="projectsitedocfile",
            name="data_version",
            field=models.PositiveIntegerField(default=0, verbose_name="数据版本"),
        ),
        migrations.AddField(
            model_name="projectsitedocfile",
            name="delete_flag",
            field=models.SmallIntegerField(
                choices=[(0, "未删除"), (1, "已删除")],
                db_index=True,
                default=0,
                verbose_name="删除标志（0：未删除；1：已删除）",
            ),
        ),
        migrations.AddField(
            model_name="projectsitedocfile",
            name="update_name",
            field=models.CharField(
                max_length=255, null=True, verbose_name="更新人姓名"
            ),
        ),
        migrations.AddField(
            model_name="projectsitedocfile",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_index=True, verbose_name="更新时间"
            ),
        ),
        migrations.AddField(
            model_name="projectsitedocfile",
            name="update_user",
            field=models.CharField(
                max_length=255, null=True, verbose_name="更新人工号"
            ),
        ),
    ]
