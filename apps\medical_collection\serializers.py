from datetime import timedelta
from django.conf import settings
from rest_framework import serializers
from common.minio_client import get_minio_client
from common.serializers import FileUrlMixin
from . import models


class MedicalCollectionTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.MedicalCollectionTask
        fields = '__all__'
        # exclude = ['patient']  # , 'subject'


class MedicalCollectionTaskCreatRequestSerializer(serializers.ModelSerializer):
    project_id = serializers.CharField(source='project.project_id', label="项目ID")
    project_site_id = serializers.CharField(source='project_site.project_site_id', label="项目中心ID")
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID")

    class Meta:
        model = models.MedicalCollectionTask
        fields = ['subject_visit_id', 'project_id', 'project_site_id', 'subject_id', 'category']
        # exclude = ['patient']  # , 'subject'


class MedicalCollectionFileSerializer(serializers.ModelSerializer):
    from apps.project.serializers import ProjectMaterialFileSerializer
    # file = serializers.FileField(required=True)  # 文件上传字段
    class Meta:
        model = models.MedicalCollectionFile
        fields = '__all__'
        # exclude = ['patient']  # , 'subject'


class MedicalCollectionGetFileRequestSerializer(serializers.ModelSerializer):

    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID")

    class Meta:
        model = models.MedicalCollectionTask
        fields = ['subject_visit_id','subject_id', 'category']
        # exclude = ['patient']  # , 's
        # ubject'


# class ProjectMaterialFileSerializer(FileUrlMixin, serializers.ModelSerializer):
#     url = serializers.SerializerMethodField(label='文件下载url')
#
#     class Meta:
#         model = models.ProjectMaterialFile
#         exclude = ['create_user', 'create_name', 'update_user', 'update_name']
class MedicalCollectionTasksLatestResponseSerializer(FileUrlMixin, serializers.ModelSerializer):
    url = serializers.SerializerMethodField(label='文件下载url')

    class Meta:
        model = models.MedicalCollectionFile
        exclude = ['create_user', 'create_name', 'update_user', 'update_name']


class MedicalRecordChangeJsonRequestMiniSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID")
    json_data = serializers.Serializer(label="JSON 数据")

    class Meta:
        model = models.MedicalCollectionTask

        fields = ['subject_visit_id','subject_id', 'category', 'json_data']
        # exclude = ['patient']  # , 's
        # ubject'


class MedicalRecordChangeJsonRequestSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID")
    json_data = serializers.JSONField(label="JSON 数据")

    class Meta:
        model = models.MedicalCollectionTask
        fields = ['subject_visit_id','subject_id', 'category', 'json_data']
        # exclude = ['patient']  # , 's
        # ubject'


# class MedicalCollectionFileHistoryRequestSerializer(serializers.ModelSerializer):
#
#     subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
#
#     class Meta:
#         model = models.MedicalCollectionTask
#         fields = ['subject_id']
#         # exclude = ['patient']  # , 's
#         # ubject'


from datetime import datetime
from django.db.models import Sum
from apps.medical_collection.models import MedicalCollectionTask
from apps.system.models import ModelInvocationLog

# 修改 MedicalCollectionFileHistoryResponseSerializer 类
# 在 serializers.py 文件中找到这个类并修改 get_time_statistics 方法



def calculate_task_time_statistics(task_id):
    """
    计算任务执行时间和模型交互时间统计
    
    Args:
        task_id: 任务ID
        
    Returns:
        dict: 包含任务时间统计信息的字典，结果已扁平化
    """
    # 获取任务信息
    try:
        task = MedicalCollectionTask.objects.get(id=task_id, delete_flag=0)
    except MedicalCollectionTask.DoesNotExist:
        return {
            'start_time': None,
            'end_time': None,
            'task_duration': 0,
        }
    
    # 获取任务开始和结束时间
    start_time = task.start_time
    end_time = task.end_time
    
    # 格式化时间
    formatted_start_time = None
    formatted_end_time = None
    
    if start_time:
        formatted_start_time = start_time.strftime('%Y-%m-%d %H:%M:%S')
    
    if end_time:
        formatted_end_time = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    # 计算任务总执行时间(秒) - 使用格式化后的时间来保证一致性
    task_duration = 0
    if start_time and end_time:
        # 使用格式化后的时间字符串重新解析为datetime对象进行计算
        # 这样可以确保计算结果与显示的时间一致
        from datetime import datetime
        formatted_start_dt = datetime.strptime(formatted_start_time, '%Y-%m-%d %H:%M:%S') if formatted_start_time else None
        formatted_end_dt = datetime.strptime(formatted_end_time, '%Y-%m-%d %H:%M:%S') if formatted_end_time else None
        
        if formatted_start_dt and formatted_end_dt:
            task_duration = (formatted_end_dt - formatted_start_dt).total_seconds()
    
    # 格式化任务总执行时间
    if task_duration >= 60:
        minutes = int(task_duration // 60)
        seconds = int(task_duration % 60)
        if seconds == 0:
            formatted_task_duration = f"{minutes}min"
        else:
            formatted_task_duration = f"{minutes}min{seconds}s"
    else:
        formatted_task_duration = f"{int(task_duration)}s"
    
    # 获取该任务相关的所有模型调用记录，只拿category为这五个的
    model_logs = ModelInvocationLog.objects.filter(
        task_id=task_id,
        delete_flag=0,
        category__in=[
            'CRF_GENERATION',
            'CRF_GENERATION_COMPARE',
            'MEDICAL_RECORD_COMPARISON',
            'MEDICAL_RECORD',
            'MEDICAL_RECORD_SUMMARY'
        ]
    ).order_by('create_time')
    
    # 初始化返回结果
    result = {
        'start_time': formatted_start_time,
        'end_time': formatted_end_time,
        'task_duration': formatted_task_duration,  # 任务总执行时间，根据要求格式化
    }
    
    # 按 category 和 model_name 分组统计
    category_model_stats = {}
    
    for log in model_logs:
        if log.start_time and log.end_time:
            duration = (log.end_time - log.start_time).total_seconds()
            
            # 按 category 和 model_name 分组统计
            key = (log.category, log.model_name)
            if key not in category_model_stats:
                category_model_stats[key] = {
                    'model_name': log.model_name,
                    'category': log.category,
                    'duration': 0,
                    'count': 0
                }
            
            # 对相同 category 和 model_name 的数据进行累加
            category_model_stats[key]['duration'] += duration
            category_model_stats[key]['count'] += 1
    
    # 将统计信息扁平化添加到结果中
    for (category, model_name), stats in category_model_stats.items():
        # 创建扁平化的键名，替换特殊字符
        clean_category = category.replace('-', '_')
        clean_model_name = model_name.replace('-', '_').replace('.', '_')
        prefix = f"{clean_category}_{clean_model_name}"
        
        # 格式化模型执行时间
        if stats['duration'] >= 60:
            minutes = int(stats['duration'] // 60)
            seconds = int(stats['duration'] % 60)
            if seconds == 0:
                formatted_duration = f"{minutes}min"
            else:
                formatted_duration = f"{minutes}min{seconds}s"
        else:
            formatted_duration = f"{int(stats['duration'])}s"
            
        result[f"{prefix}_duration"] = formatted_duration
        result[f"{prefix}_count"] = stats['count']
    
    return result


class MedicalCollectionFileHistoryResponseSerializer(FileUrlMixin, serializers.ModelSerializer):
    subject_visit_id = serializers.CharField(source='task.subject_visit_id', label="受试者访视ID")
    subject_id = serializers.CharField(source='task.subject_id', label="受试者ID")
    category = serializers.CharField(source='task.category', label="素材分类")
    create_user = serializers.CharField(source='task.create_user', label="创建人")
    create_name = serializers.CharField(source='task.create_name', label="创建人")
    url = serializers.SerializerMethodField(label='文件下载url')
    time_statistics = serializers.SerializerMethodField(label='时间统计信息')  # 添加时间统计字段

    class Meta:
        model = models.MedicalCollectionFile
        fields = '__all__'
        # exclude = ['update_user', 'update_name']

    def get_time_statistics(self, obj):
        """
        获取任务的时间统计信息，并添加文件来源信息
        """
        # time_stats = {
        #     'task_duration': 0,
        #     'model_interaction_time': 0,
        #     'overhead_time': 0,
        #     'model_count': 0,
        #     'time_details': []
        # }
        
        # 添加文件来源信息
        file_source = "模型结果"  # 默认值
        if obj.model_name =='DeepSeek':
            file_source = 'DS模型结果'
        if obj.model_name =='qwen3':
            file_source = 'Qwen模型结果'
        if obj.version:
            if "Person" in obj.version:
                file_source = "导入"
            elif "manual_edit" in obj.version:  # 注意这里匹配的是 manual_edit 而不是 manual_editing
                file_source = "在线编辑"
        
        
        
        # if hasattr(obj, 'task') and obj.task:
        task_stats = calculate_task_time_statistics(obj.task.id)
        task_stats['file_source'] = file_source
        # if task_stats['file_source'] != "模型结果":
        #     pass
        #     # time_stats.update(task_stats)
            
        return task_stats

class MedicalCrfResultTextSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(source='task.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='task.subject_visit_id', label="受试者访视ID")
    # medical_collection_file_id = serializers.CharField(source='medical_collection_file_id', label="文件ID")
    project_id = serializers.CharField(source='task.project_id', label="项目ID")
    project_site_id = serializers.CharField(source='task.project_site_id', label="项目中心ID")
    class Meta:
        model = models.MedicalCollectionCrfResult
        fields = ['subject_id', 'subject_visit_id', 'project_id', 'project_site_id']

class MedicalCollectionCrfLogCreateSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(source='task.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='task.subject_visit_id', label="受试者访视ID")
    # medical_collection_file_id = serializers.CharField(source='medical_collection_file_id', label="文件ID")
    project_id = serializers.CharField(source='task.project_id', label="项目ID")
    project_site_id = serializers.CharField(source='task.project_site_id', label="项目中心ID")
    modified_history = serializers.CharField(required=True)
    create_user = serializers.CharField(source='create_name', label="创建人")


    class Meta:
        model = models.MedicalCollectionCrfLog
        fields = [
            'subject_id',
            'subject_visit_id',
            'project_id',
            'project_site_id',
            'modified_history',
            'create_user'
        ]

class MedicalCollectionCrfLogQuerySerializer(serializers.Serializer):
    project_id = serializers.CharField(required=True, label="项目ID")
    project_site_id = serializers.CharField(required=True, label="项目中心ID")
    subject_id = serializers.CharField(required=True, label="受试者ID")
    subject_visit_id = serializers.CharField(required=True, label="受试者访视ID")
    title = serializers.CharField(required=True, label="一级标题")
    key = serializers.CharField(required=True, label="二级键名")

    class Meta:
        model = models.MedicalCollectionCrfLog
        fields = [
            'subject_id',
            'subject_visit_id',
            'project_id',
            'project_site_id',
            'title',
            'key'
        ]

class MedicalCollectionCrfLogResponseSerializer(serializers.ModelSerializer):
    content_text = serializers.SerializerMethodField()
    class Meta:
        model = models.MedicalCollectionCrfLog
        fields = [
            'id',
            'title',
            'item_key',
            'content_text',
            'create_time',
            'create_user',
            'create_name',
        ]

    def get_content_text(self, obj):
        # create_name = obj.create_name or ""
        # create_user = obj.create_user or ""
        # title = obj.title or ""
        # item_key = obj.item_key or ""
        content_text = obj.content_text or ""
        return content_text  # 或者替换为真实姓名字段，如果有