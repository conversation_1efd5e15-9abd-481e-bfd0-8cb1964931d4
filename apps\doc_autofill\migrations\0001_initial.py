# Generated by Django 4.1.5 on 2025-04-22 17:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="DocAutofillTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.Char<PERSON>ield(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "name",
                    models.CharField(default="", max_length=255, verbose_name="任务名称"),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("GEN_SYS_TMPL", "生成系统模板"),
                            ("GEN_PROJECT_FILE", "生成项目文件"),
                        ],
                        default="",
                        max_length=20,
                        verbose_name="任务分类",
                    ),
                ),
                ("description", models.TextField(default="", verbose_name="任务描述")),
                (
                    "status",
                    models.CharField(default="", max_length=20, verbose_name="任务状态"),
                ),
                ("start_time", models.DateTimeField(verbose_name="开始时间")),
                (
                    "end_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="结束时间"),
                ),
            ],
            options={
                "verbose_name": "文件自动回填任务",
                "verbose_name_plural": "文件自动回填任务",
                "db_table": "doc_autofill_task",
            },
        ),
        migrations.CreateModel(
            name="HospDocTmplCenter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "status_code",
                    models.CharField(
                        default="",
                        max_length=20,
                        verbose_name="中心模板状态 UNRELEASED 未发布，RELEASED 已发布",
                    ),
                ),
                (
                    "total_amount",
                    models.IntegerField(default=0, verbose_name="已上传文件清单个数"),
                ),
                (
                    "project_used_amount",
                    models.IntegerField(default=0, verbose_name="已上传文件清单个数"),
                ),
            ],
            options={
                "verbose_name": "中心模板",
                "verbose_name_plural": "中心模板",
                "db_table": "hosp_doc_tmpl_center",
            },
        ),
        migrations.CreateModel(
            name="HospDocTmplFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "original_filename",
                    models.CharField(max_length=255, verbose_name="原始文件名"),
                ),
                ("bucket_name", models.CharField(max_length=100, verbose_name="存储桶名称")),
                ("object_name", models.CharField(max_length=500, verbose_name="对象名称")),
                ("content_type", models.CharField(max_length=100, verbose_name="文件类型")),
                ("size", models.BigIntegerField(verbose_name="文件大小（字节）")),
                (
                    "hash",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="文件哈希（sha256）"
                    ),
                ),
            ],
            options={
                "verbose_name": "中心模板文件",
                "verbose_name_plural": "中心模板文件",
                "db_table": "hosp_doc_tmpl_file",
            },
        ),
        migrations.CreateModel(
            name="HospDocTmplFileProcessed",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "original_filename",
                    models.CharField(max_length=255, verbose_name="原始文件名"),
                ),
                ("bucket_name", models.CharField(max_length=100, verbose_name="存储桶名称")),
                ("object_name", models.CharField(max_length=500, verbose_name="对象名称")),
                ("content_type", models.CharField(max_length=100, verbose_name="文件类型")),
                ("size", models.BigIntegerField(verbose_name="文件大小（字节）")),
                (
                    "hash",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="文件哈希（sha256）"
                    ),
                ),
            ],
            options={
                "verbose_name": "中心模板文件（AI处理加工过的）",
                "verbose_name_plural": "中心模板文件（AI处理加工过的）",
                "db_table": "hosp_doc_tmpl_file_processed",
            },
        ),
        migrations.CreateModel(
            name="HospDocTmplInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "file_category",
                    models.CharField(
                        default="", max_length=50, verbose_name="文件类别,暂时未启用"
                    ),
                ),
                (
                    "file_type",
                    models.CharField(
                        default="", max_length=50, verbose_name="文件类型,暂时未启用"
                    ),
                ),
                (
                    "file_name",
                    models.CharField(default="", max_length=100, verbose_name="文件名称"),
                ),
                (
                    "file_extension",
                    models.CharField(default="", max_length=10, verbose_name="文件格式"),
                ),
                (
                    "status_code",
                    models.CharField(
                        default="",
                        max_length=20,
                        verbose_name="系统模板状态 NOT_CREATED 未生成 CREATING 生成中 ,COMPLETED 已生成",
                    ),
                ),
                (
                    "audit_status_code",
                    models.CharField(
                        default="",
                        max_length=20,
                        verbose_name="中心项目文件审核状态 UNAPPROVED 待审核，APPROVED 审核通过，REJECT 审核驳回",
                    ),
                ),
            ],
            options={
                "verbose_name": "中心模板文件信息",
                "verbose_name_plural": "中心模板文件信息",
                "db_table": "hosp_doc_tmpl_info",
            },
        ),
        migrations.CreateModel(
            name="ProjectSiteDocCenter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "status_code",
                    models.CharField(
                        default="",
                        max_length=20,
                        verbose_name="PROCESSING 进行中，COMPLETED 已完成",
                    ),
                ),
                ("total_amount", models.IntegerField(default=0, verbose_name="项目文总数")),
                (
                    "complete_amount",
                    models.IntegerField(default=0, verbose_name="项目文件完成数"),
                ),
            ],
            options={
                "verbose_name": "项目中心文件",
                "verbose_name_plural": "项目中心文件",
                "db_table": "project_site_doc_center",
            },
        ),
        migrations.CreateModel(
            name="ProjectSiteDocFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "original_filename",
                    models.CharField(max_length=255, verbose_name="原始文件名"),
                ),
                ("bucket_name", models.CharField(max_length=100, verbose_name="存储桶名称")),
                ("object_name", models.CharField(max_length=500, verbose_name="对象名称")),
                ("content_type", models.CharField(max_length=100, verbose_name="文件类型")),
                ("size", models.BigIntegerField(verbose_name="文件大小（字节）")),
                (
                    "hash",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="文件哈希（sha256）"
                    ),
                ),
            ],
            options={
                "verbose_name": "项目中心文件",
                "verbose_name_plural": "项目中心文件",
                "db_table": "project_site_doc_file",
            },
        ),
        migrations.CreateModel(
            name="ProjectSiteDocInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "file_category",
                    models.CharField(
                        default="", max_length=50, verbose_name="文件类别,暂时未启用"
                    ),
                ),
                (
                    "file_type",
                    models.CharField(
                        default="", max_length=50, verbose_name="文件类型,暂时未启用"
                    ),
                ),
                (
                    "file_name",
                    models.CharField(default="", max_length=100, verbose_name="文件名称"),
                ),
                (
                    "file_extension",
                    models.CharField(default="", max_length=10, verbose_name="文件格式"),
                ),
                (
                    "status_code",
                    models.CharField(
                        choices=[
                            ("NOT_CREATED", "未生成"),
                            ("CREATING", "生成中"),
                            ("COMPLETED", "已生成"),
                        ],
                        default="NOT_CREATED",
                        max_length=20,
                        verbose_name="项目文件生成状态",
                    ),
                ),
                (
                    "audit_status_code",
                    models.CharField(
                        choices=[
                            ("UNAPPROVED", "待审核"),
                            ("APPROVED", "审核通过"),
                            ("REJECT", "审核驳回"),
                        ],
                        default="UNAPPROVED",
                        max_length=20,
                        verbose_name="中心项目文件审核状态",
                    ),
                ),
                (
                    "auditor_id",
                    models.CharField(default="", max_length=255, verbose_name="审核人工号"),
                ),
                (
                    "auditor_name",
                    models.CharField(default="", max_length=255, verbose_name="审核人名称"),
                ),
                (
                    "doc_file",
                    models.OneToOneField(
                        db_column="doc_file_id",
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="doc_info",
                        to="doc_autofill.projectsitedocfile",
                        verbose_name="项目中心文件ID",
                    ),
                ),
            ],
            options={
                "verbose_name": "项目中心文件信息",
                "verbose_name_plural": "项目中心文件信息",
                "db_table": "project_site_doc_info",
            },
        ),
    ]
