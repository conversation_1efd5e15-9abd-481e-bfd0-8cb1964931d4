import os
import time
import json
import uuid
import logging
import threading
from django.http import Http404
from django.db import connections
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.mixins import RetrieveModelMixin
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import MultiPartParser, FormParser
from django.http import FileResponse
from django.conf import settings
from django.shortcuts import render
from django.db import transaction
from django.db.models import Subquery, OuterRef
from django.db.models import Count
from rest_framework.viewsets import GenericViewSet
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.fields import <PERSON><PERSON><PERSON><PERSON>
from rest_framework import filters, viewsets
from rest_framework.mixins import ListModelMixin, CreateModelMixin, UpdateModelMixin, DestroyModelMixin
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.filters import OrderingFilter
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.renderers import BaseRenderer
from rest_framework.exceptions import ValidationError, NotFound, APIException
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import APIException

from django_filters.rest_framework import DjangoFilterBackend

from rest_pandas.views import PandasViewBase
from rest_pandas import PandasExcelRenderer
from openpyxl.styles import Font, Border
from openpyxl.styles import Alignment

from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema, OpenApiParameter

from .models import MaskingTask, MaskingMaskedFile, MaskingOriginalFile
from .serializers import CreateMaskingTaskSerializer, MaskingTaskSerializer
from apps.subject.models import SubjectItem, SubjectVisit, SubjectEpoch, Subject
from apps.subject_medical.models import SubjectMedicalInfo, SubjectMedicalFile
from apps.patient.models import Patient
from common.tools import sql_to_list
from common.auth import ERPSysJWTAuthentication
from common.utils import calculate_file_hash
from common.minio_client import get_minio_client
from common.pagination import StandardResultsSetPagination
from common.ocr_tools import heic_to_jpg_stream, heic_to_jpg_file, doc_to_docx_file, bytes_word_to_pdf
from common.tools import trigger_dag

logger = logging.getLogger('app')


class APIUSer(AnonymousUser):

    @property
    def is_authenticated(self):
        return True


class BearerTokenAuthentication(BaseAuthentication):
    keyword = 'Bearer'

    def authenticate(self, request):
        auth = request.META.get('HTTP_AUTHORIZATION', '')
        if not auth or not auth.startswith(f'{self.keyword} '):
            return None

        token = auth.split(' ')[1]
        # 使用固定的API密钥进行验证
        if token == settings.FILE_MASKING_API_SECRET_KEY:
            return (APIUSer(), token)
        else:
            raise AuthenticationFailed('无效的认证凭证')


class BaseAPIView(APIView):
    authentication_classes = [BearerTokenAuthentication]
    permission_classes = [IsAuthenticated]


class MaskingTaskView(BaseAPIView):

    parser_classes = (MultiPartParser, )  # 支持文件上传

    @extend_schema(
        summary='创建文件脱敏任务',
        tags=['文件脱敏'],
        request=CreateMaskingTaskSerializer,
        responses=MaskingTaskSerializer
    )
    def post(self, request):
        serializer = CreateMaskingTaskSerializer(data=request.data)
        if not serializer.is_valid():
            raise ValidationError(serializer.errors)

        params = serializer.validated_data

        file = params['file']
        meta = params['meta']
        callback_url = params.get('callback_url')

        try:
            meta = json.loads(meta)
            task_type = meta.get('type')
        except:
            meta = {}
            task_type = None

        task_id = f"{uuid.uuid4().hex}"

        _, ext = os.path.splitext(file.name)

        ALLOWED_EXTENSIONS = [
            '.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp', '.pdf',
            '.doc', '.docx', '.heic'
        ]

        if ext.lower() not in ALLOWED_EXTENSIONS:
            raise ValidationError(f"不支持的文件格式，仅支持：{ALLOWED_EXTENSIONS}")

        # 转换特殊的文件格式
        if ext.lower() == '.heic':
            file = heic_to_jpg_file(file)
            _, ext = os.path.splitext(file.name)

        if ext.lower() == '.doc' or ext.lower() == '.docx':
            file = bytes_word_to_pdf(file, ext.lower())
            _, ext = os.path.splitext(file.name)

        hash = calculate_file_hash(file)
        object_name = f"{uuid.uuid4().hex}{ext}"
        bucket_name = settings.MINIO_BUCKET_NAME

        # 保存文件
        try:
            minio_client = get_minio_client()
            # Ensure bucket exists
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)
            # Upload file to MinIO
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file,
                length=file.size,
                part_size=1024 * 1024 * 5,
                content_type=file.content_type
            )
        except Exception as e:
            logger.error(e)
            raise APIException(f"文件上传失败：{e}")

        # 数据入库
        with transaction.atomic():
            original_file_data = {
                'original_filename': file.name,
                'bucket_name': bucket_name,
                'object_name': object_name,
                'content_type': file.content_type,
                'size': file.size,
                'hash': hash,
                # 'create_user': request.sys_user.username,
                # 'create_name': request.sys_user.realname,
            }
            original_file = MaskingOriginalFile.objects.create(**original_file_data)

            # Create the task
            task = MaskingTask(
                task_id=task_id,
                original_file=original_file,
                meta=meta,
                callback_url=callback_url,
                status='TODO'
            )
            task.save()

        if task_type == '102':  # 访视操作项类型文件
            
            # 只同步了OT普通的操作项数据，samples，drugs两种类型item没同步
            try:
                subject_id = meta.get('subjectId') or ''
                subject_item_id = meta.get('subjectItemId') or ''

                subject_item_id = subject_item_id + '-' + subject_id

                if subject_id and subject_item_id:
                    print(subject_item_id, 'subject_item_idsubject_item_idsubject_item_id')
                    with transaction.atomic():
                        try:
                            subject = Subject.objects.get(subject_id=subject_id)
                        except Subject.DoesNotExist:
                            # 执行原生 SQL 查询
                            with connections['OTDB'].cursor() as cursor:
                                cursor.execute("""
                                    SELECT
                                        t1.id AS subject_id,
                                        t1.project_id,
                                        t1.project_site_id,
                                        t1.real_name,
                                        t1.short_name,
                                        t1.code,
                                        t1.status,
                                        dictconstantname('e888888', t1.status) AS status_text
                                    FROM subject AS t1
                                    WHERE t1.id = %s
                                """, [subject_id])
                                row = cursor.fetchone()

                            if not row:
                                raise ValidationError(f"参数 subject_id={subject_id} 不存在")

                            columns = [column[0] for column in cursor.description]
                            data = dict(zip(columns, row))

                            # 创建 Subject 记录
                            subject = Subject.objects.create(**data)

                        patient_id = f"{subject.project_id}_{subject.project_site_id}_{subject.subject_id}"
                        # Update or create related patient record
                        patient, created = Patient.objects.update_or_create(
                            subject_id=subject_id,
                            defaults={'patient_id': patient_id}
                        )

                        # try:
                        #     subject_item = SubjectItem.objects.get(subject_item_id=subject_item_id)
                        # except SubjectItem.DoesNotExist:
                        #     # 执行原生 SQL 查询
                        #     with connections['OTDB'].cursor() as cursor:
                        #         cursor.execute("""
                        #             SELECT
                        #                 concat(t1.id, '-', t1.sub_id) as subject_item_id,
                        #                 concat(t1.visit->>'subVstId', '-', t1.sub_id) as subject_visit_id,
                        #                 t1.id as source_id,
                        #                 t1.item->>'itemId' as item_id,
                        #                 t1.status,
                        #                 dictconstantname('e888888', t1.status) as status_text,
                        #                 t1.item->>'itemLabel' as label,
                        #                 t1.seq,
                        #                 t1.project_id,
                        #                 t1.project_site_id,
                        #                 t1.sub_id as subject_id
                        #             FROM subject_item AS t1
                        #             WHERE t1.id = %s and t1.sub_id=%s
                        #         """, subject_item_id.split('-'))
                        #         row = cursor.fetchone()
                        #
                        #     if not row:
                        #         raise ValidationError(f"参数 subject_item_id={subject_item_id} 不存在")
                        #
                        #     columns = [column[0] for column in cursor.description]
                        #     data = dict(zip(columns, row))
                        #
                        #     # 创建 SubjectItem 记录
                        #     subject_item = SubjectItem.objects.create(**data)
                        with connections['OTDB'].cursor() as cursor:
                            cursor.execute("""
                                        SELECT
                                            concat(t1.id, '-', t1.sub_id) as subject_item_id,
                                            concat(t1.visit->>'subVstId', '-', t1.sub_id) as subject_visit_id,
                                            t1.id as source_id,
                                            t1.item->>'itemId' as item_id,
                                            t1.status,
                                            dictconstantname('e888888', t1.status) as status_text,
                                            t1.item->>'itemLabel' as label,
                                            t1.seq,
                                            t1.project_id,
                                            t1.project_site_id,
                                            t1.sub_id as subject_id
                                        FROM subject_item AS t1
                                        WHERE t1.id = %s and t1.sub_id=%s
                                    """, subject_item_id.split('-'))
                            row = cursor.fetchone()
                            columns = [column[0] for column in cursor.description]
                            data = dict(zip(columns, row))
                            subject_visit_id = data.get('subject_visit_id')
                            item_label = str(data.get('subject_item_id')) + '-' + str(data.get('label'))

                        try:
                            subject_visit = SubjectVisit.objects.get(subject_visit_id=subject_visit_id)
                        except SubjectVisit.DoesNotExist:
                            # 执行原生 SQL 查询
                            with connections['OTDB'].cursor() as cursor:
                                cursor.execute("""
                                    SELECT
                                        concat(t1.id, '-', t1.sub_id) as subject_visit_id,
                                        concat(t1.visit->>'epochId', '-', t1.sub_id) as subject_epoch_id,
                                        t1.id as source_id,
                                        t1.sub_id as subject_id,
                                        t1.visit->>'visitId' as visit_id,
                                        t1.visit_date,
                                        t1.status,
                                        dictconstantname('e888888', t1.status) as status_text,
                                        t1.visit->>'visitType' as type,
                                        dictconstantname('e888888', t1.visit->>'visitType') as type_text,
                                        t1.label,
                                        t1.skip_reason,
                                        t1.prepare_date,
                                        t1.project_id,
                                        t1.project_site_id,
                                        t1.create_time as ot_create_time,
                                        t1.update_time as ot_update_time
                                    FROM subject_visit AS t1
                                    WHERE t1.id = %s and t1.sub_id=%s
                                """, subject_visit_id.split('-'))
                                row = cursor.fetchone()

                            if not row:
                                raise ValidationError(f"参数 subject_visit_id={subject_visit_id} 不存在")

                            columns = [column[0] for column in cursor.description]
                            data = dict(zip(columns, row))

                            # 创建 SubjectVisit 记录
                            subject_visit = SubjectVisit.objects.create(**data)

                        try:
                            subject_epoch = subject_visit.subject_epoch
                        except SubjectEpoch.DoesNotExist:
                            # 执行原生 SQL 查询
                            with connections['OTDB'].cursor() as cursor:
                                cursor.execute("""
                                    SELECT
                                        concat(t1.id, '-', t1.sub_id) as subject_epoch_id,
                                        t1.id as source_id,
                                        t1.ext->>'epochId' as epoch_id,
                                        t1.status,
                                        dictconstantname('e888888', t1.status) as status_text,
                                        t1.label,
                                        t1.ext->>'epochSeq' as seq,
                                        t1.project_id,
                                        t1.project_site_id,
                                        t1.sub_id as subject_id
                                    FROM subject_epoch AS t1
                                    WHERE t1.id = %s and t1.sub_id=%s
                                """, subject_visit.subject_epoch_id.split('-'))
                                row = cursor.fetchone()

                            if not row:
                                raise ValidationError(f"参数 subject_epoch_id={subject_visit.subject_epoch_id} 不存在")

                            columns = [column[0] for column in cursor.description]
                            data = dict(zip(columns, row))
                            data['seq'] = int(data['seq']) if data['seq'] else data['seq']

                            # 创建 SubjectEpoch 记录
                            subject_epoch = SubjectEpoch.objects.create(**data)

                        extra = {}

                        subject_medical_file = SubjectMedicalFile.objects.create(**original_file_data)

                        medical_info = SubjectMedicalInfo.objects.create(
                            project_id=subject.project_id,
                            project_site_id=subject.project_site_id,
                            subject=subject,
                            patient=patient,
                            subject_item_id=item_label,
                            subject_visit=subject_visit,
                            subject_epoch=subject_epoch,
                            file=subject_medical_file,
                            # create_user=request.sys_user.username,
                            # create_name=request.sys_user.realname,
                            data_source='OT'
                        )
                        extra['subject_medical_info_id'] = medical_info.id
                        task.extra = extra
                        task.save()
            except Exception as e:
                logger.error(e)
                import traceback
                traceback.print_exc()

        try:
            trigger_dag('external_masking_task', conf={'task_id': task.id})
        except Exception as e:
            logger.error(e)
            task.status = 'ERROR'
            task.save()

        serializer = MaskingTaskSerializer(task)
        return Response(serializer.data)


class MaskingTaskDetailView(BaseAPIView):

    def get_object(self, task_id):
        try:
            return MaskingTask.objects.get(task_id=task_id)
        except MaskingTask.DoesNotExist:
            raise Http404

    @extend_schema(
        summary='查询文件脱敏任务详情',
        tags=['文件脱敏'],
        responses=MaskingTaskSerializer
    )
    def get(self, request, task_id):
        task = self.get_object(task_id)
        serializer = MaskingTaskSerializer(task)
        return Response(serializer.data)


class StructuringTaskView(BaseAPIView):
    parser_classes = (MultiPartParser,)  # 支持文件上传

    @extend_schema(
        summary='创建结构化提取任务',
        tags=['结构化提取'],
        request=CreateMaskingTaskSerializer,
        responses=MaskingTaskSerializer
    )
    def post(self, request):
        serializer = CreateMaskingTaskSerializer(data=request.data)
        if not serializer.is_valid():
            raise ValidationError(serializer.errors)

        params = serializer.validated_data

        file = params['file']
        meta = params['meta']
        callback_url = params.get('callback_url')

        try:
            meta = json.loads(meta)
            task_type = meta.get('type')
        except:
            meta = {}
            task_type = None

        task_id = f"{uuid.uuid4().hex}"

        _, ext = os.path.splitext(file.name)

        ALLOWED_EXTENSIONS = [
            '.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp', '.pdf',
            '.doc', '.docx', '.heic'
        ]

        if ext.lower() not in ALLOWED_EXTENSIONS:
            raise ValidationError(f"不支持的文件格式，仅支持：{ALLOWED_EXTENSIONS}")

        # 转换特殊的文件格式
        if ext.lower() == '.heic':
            file = heic_to_jpg_file(file)
            _, ext = os.path.splitext(file.name)

        if ext.lower() == '.doc' or ext.lower() == '.docx':
            file = bytes_word_to_pdf(file, ext.lower())
            _, ext = os.path.splitext(file.name)

        hash = calculate_file_hash(file)
        object_name = f"{uuid.uuid4().hex}{ext}"
        bucket_name = settings.MINIO_BUCKET_NAME

        # 保存文件
        try:
            minio_client = get_minio_client()
            # Ensure bucket exists
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)
            # Upload file to MinIO
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file,
                length=file.size,
                part_size=1024 * 1024 * 5,
                content_type=file.content_type
            )
        except Exception as e:
            logger.error(e)
            raise APIException(f"文件上传失败：{e}")

        # 数据入库
        with transaction.atomic():
            original_file_data = {
                'original_filename': file.name,
                'bucket_name': bucket_name,
                'object_name': object_name,
                'content_type': file.content_type,
                'size': file.size,
                'hash': hash,
                # 'create_user': request.sys_user.username,
                # 'create_name': request.sys_user.realname,
            }
            original_file = MaskingOriginalFile.objects.create(**original_file_data)

            # Create the task
            task = MaskingTask(
                task_id=task_id,
                original_file=original_file,
                meta=meta,
                callback_url=callback_url,
                status='TODO'
            )
            task.save()

        if task_type == '102':  # 访视操作项类型文件

            # 只同步了OT普通的操作项数据，samples，drugs两种类型item没同步
            try:
                subject_id = meta.get('subjectId') or ''
                subject_item_id = meta.get('subjectItemId') or ''

                subject_item_id = subject_item_id + '-' + subject_id

                if subject_id and subject_item_id:
                    print(subject_item_id, 'subject_item_idsubject_item_idsubject_item_id')
                    with transaction.atomic():
                        try:
                            subject = Subject.objects.get(subject_id=subject_id)
                        except Subject.DoesNotExist:
                            # 执行原生 SQL 查询
                            with connections['OTDB'].cursor() as cursor:
                                cursor.execute("""
                                    SELECT
                                        t1.id AS subject_id,
                                        t1.project_id,
                                        t1.project_site_id,
                                        t1.real_name,
                                        t1.short_name,
                                        t1.code,
                                        t1.status,
                                        dictconstantname('e888888', t1.status) AS status_text
                                    FROM subject AS t1
                                    WHERE t1.id = %s
                                """, [subject_id])
                                row = cursor.fetchone()

                            if not row:
                                raise ValidationError(f"参数 subject_id={subject_id} 不存在")

                            columns = [column[0] for column in cursor.description]
                            data = dict(zip(columns, row))

                            # 创建 Subject 记录
                            subject = Subject.objects.create(**data)

                        patient_id = f"{subject.project_id}_{subject.project_site_id}_{subject.subject_id}"
                        # Update or create related patient record
                        patient, created = Patient.objects.update_or_create(
                            subject_id=subject_id,
                            defaults={'patient_id': patient_id}
                        )

                        # try:
                        #     subject_item = SubjectItem.objects.get(subject_item_id=subject_item_id)
                        # except SubjectItem.DoesNotExist:
                        #     # 执行原生 SQL 查询
                        #     with connections['OTDB'].cursor() as cursor:
                        #         cursor.execute("""
                        #             SELECT
                        #                 concat(t1.id, '-', t1.sub_id) as subject_item_id,
                        #                 concat(t1.visit->>'subVstId', '-', t1.sub_id) as subject_visit_id,
                        #                 t1.id as source_id,
                        #                 t1.item->>'itemId' as item_id,
                        #                 t1.status,
                        #                 dictconstantname('e888888', t1.status) as status_text,
                        #                 t1.item->>'itemLabel' as label,
                        #                 t1.seq,
                        #                 t1.project_id,
                        #                 t1.project_site_id,
                        #                 t1.sub_id as subject_id
                        #             FROM subject_item AS t1
                        #             WHERE t1.id = %s and t1.sub_id=%s
                        #         """, subject_item_id.split('-'))
                        #         row = cursor.fetchone()
                        #
                        #     if not row:
                        #         raise ValidationError(f"参数 subject_item_id={subject_item_id} 不存在")
                        #
                        #     columns = [column[0] for column in cursor.description]
                        #     data = dict(zip(columns, row))
                        #
                        #     # 创建 SubjectItem 记录
                        #     subject_item = SubjectItem.objects.create(**data)
                        with connections['OTDB'].cursor() as cursor:
                            cursor.execute("""
                                        SELECT
                                            concat(t1.id, '-', t1.sub_id) as subject_item_id,
                                            concat(t1.visit->>'subVstId', '-', t1.sub_id) as subject_visit_id,
                                            t1.id as source_id,
                                            t1.item->>'itemId' as item_id,
                                            t1.status,
                                            dictconstantname('e888888', t1.status) as status_text,
                                            t1.item->>'itemLabel' as label,
                                            t1.seq,
                                            t1.project_id,
                                            t1.project_site_id,
                                            t1.sub_id as subject_id
                                        FROM subject_item AS t1
                                        WHERE t1.id = %s and t1.sub_id=%s
                                    """, subject_item_id.split('-'))
                            row = cursor.fetchone()
                            columns = [column[0] for column in cursor.description]
                            data = dict(zip(columns, row))
                            subject_visit_id = data.get('subject_visit_id')
                            item_label = str(data.get('subject_item_id')) + '-' + str(data.get('label'))

                        try:
                            subject_visit = SubjectVisit.objects.get(subject_visit_id=subject_visit_id)
                        except SubjectVisit.DoesNotExist:
                            # 执行原生 SQL 查询
                            with connections['OTDB'].cursor() as cursor:
                                cursor.execute("""
                                    SELECT
                                        concat(t1.id, '-', t1.sub_id) as subject_visit_id,
                                        concat(t1.visit->>'epochId', '-', t1.sub_id) as subject_epoch_id,
                                        t1.id as source_id,
                                        t1.sub_id as subject_id,
                                        t1.visit->>'visitId' as visit_id,
                                        t1.visit_date,
                                        t1.status,
                                        dictconstantname('e888888', t1.status) as status_text,
                                        t1.visit->>'visitType' as type,
                                        dictconstantname('e888888', t1.visit->>'visitType') as type_text,
                                        t1.label,
                                        t1.skip_reason,
                                        t1.prepare_date,
                                        t1.project_id,
                                        t1.project_site_id,
                                        t1.create_time as ot_create_time,
                                        t1.update_time as ot_update_time
                                    FROM subject_visit AS t1
                                    WHERE t1.id = %s and t1.sub_id=%s
                                """, subject_visit_id.split('-'))
                                row = cursor.fetchone()

                            if not row:
                                raise ValidationError(f"参数 subject_visit_id={subject_visit_id} 不存在")

                            columns = [column[0] for column in cursor.description]
                            data = dict(zip(columns, row))

                            # 创建 SubjectVisit 记录
                            subject_visit = SubjectVisit.objects.create(**data)

                        try:
                            subject_epoch = subject_visit.subject_epoch
                        except SubjectEpoch.DoesNotExist:
                            # 执行原生 SQL 查询
                            with connections['OTDB'].cursor() as cursor:
                                cursor.execute("""
                                    SELECT
                                        concat(t1.id, '-', t1.sub_id) as subject_epoch_id,
                                        t1.id as source_id,
                                        t1.ext->>'epochId' as epoch_id,
                                        t1.status,
                                        dictconstantname('e888888', t1.status) as status_text,
                                        t1.label,
                                        t1.ext->>'epochSeq' as seq,
                                        t1.project_id,
                                        t1.project_site_id,
                                        t1.sub_id as subject_id
                                    FROM subject_epoch AS t1
                                    WHERE t1.id = %s and t1.sub_id=%s
                                """, subject_visit.subject_epoch_id.split('-'))
                                row = cursor.fetchone()

                            if not row:
                                raise ValidationError(f"参数 subject_epoch_id={subject_visit.subject_epoch_id} 不存在")

                            columns = [column[0] for column in cursor.description]
                            data = dict(zip(columns, row))
                            data['seq'] = int(data['seq']) if data['seq'] else data['seq']

                            # 创建 SubjectEpoch 记录
                            subject_epoch = SubjectEpoch.objects.create(**data)

                        extra = {}

                        subject_medical_file = SubjectMedicalFile.objects.create(**original_file_data)

                        medical_info = SubjectMedicalInfo.objects.create(
                            project_id=subject.project_id,
                            project_site_id=subject.project_site_id,
                            subject=subject,
                            patient=patient,
                            subject_item_id=item_label,
                            subject_visit=subject_visit,
                            subject_epoch=subject_epoch,
                            file=subject_medical_file,
                            # create_user=request.sys_user.username,
                            # create_name=request.sys_user.realname,
                            data_source='OT'
                        )
                        extra['subject_medical_info_id'] = medical_info.id
                        task.extra = extra
                        task.save()
            except Exception as e:
                logger.error(e)
                import traceback
                traceback.print_exc()

        try:
            trigger_dag('external_structuring_task', conf={'task_id': task.id})
        except Exception as e:
            logger.error(e)
            task.status = 'ERROR'
            task.save()

        serializer = MaskingTaskSerializer(task)
        return Response(serializer.data)