# Generated by Django 4.1.5 on 2025-07-25 13:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('medical_collection', '0004_medicalcollectiontask_subject_visit'),
    ]

    operations = [
        migrations.CreateModel(
            name='MedicalCollectionCrfLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('medical_collection_crf_result_id', models.BigIntegerField(default=0, verbose_name='病历归集CRF结果id')),
                ('title', models.CharField(default='', max_length=20, verbose_name='一级标题')),
                ('item_key', models.CharField(default='', max_length=20, verbose_name='二级标题')),
                ('content_text', models.CharField(default='', max_length=20, verbose_name='变更记录内容')),
                ('delete_flag', models.SmallIntegerField(default=0, verbose_name='删除标志（0：未删除；1：已删除）')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('create_user', models.CharField(blank=True, max_length=50, null=True, verbose_name='创建人工号')),
                ('create_name', models.CharField(blank=True, max_length=50, null=True, verbose_name='创建人姓名')),
            ],
            options={
                'verbose_name': '病历归集',
                'verbose_name_plural': '病历归集CRF变更日志',
                'db_table': 'medical_collection_crf_log',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='MedicalCollectionCrfResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('delete_flag', models.SmallIntegerField(choices=[(0, '未删除'), (1, '已删除')], db_index=True, default=0, verbose_name='删除标志（0：未删除；1：已删除）')),
                ('data_version', models.PositiveIntegerField(default=0, verbose_name='数据版本')),
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, db_index=True, verbose_name='更新时间')),
                ('create_user', models.CharField(max_length=255, null=True, verbose_name='创建人工号')),
                ('create_name', models.CharField(max_length=255, null=True, verbose_name='创建人姓名')),
                ('update_user', models.CharField(max_length=255, null=True, verbose_name='更新人工号')),
                ('update_name', models.CharField(max_length=255, null=True, verbose_name='更新人姓名')),
                ('medical_collection_task_id', models.BigIntegerField(default=0, verbose_name='病历归集文件id')),
                ('result_text', models.TextField(blank=True, null=True, verbose_name='result文本')),
                ('patient_id', models.CharField(max_length=255, verbose_name='患者ID')),
                ('project_id', models.CharField(max_length=255, verbose_name='项目ID')),
                ('project_site_id', models.CharField(max_length=255, verbose_name='项目中心ID')),
                ('subject_id', models.CharField(max_length=50, verbose_name='受试者ID')),
                ('subject_visit_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='受试者访视ID')),
            ],
            options={
                'db_table': 'medical_collection_crf_result',
                'managed': False,
            },
        ),
    ]
