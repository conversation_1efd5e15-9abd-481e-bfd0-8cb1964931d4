import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

import re
import json
import requests
import datetime
import argparse
import asyncio
import time
from string import Template
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from django.db import connection
from apps.ae_tracker.models import TestResult, AeTrackerTask
from apps.subject.models import SubjectItem
from django.db import transaction, DatabaseError

from common.ocr_tools import hw_ocr_general_text, format_ocr_text, extract_text_from_folder
from common.llm_tools import deepseek_r1_llama_70b, deepseek_r1_qwen_32b, get_json_from_ai_reply, get_generated_tokens_think_from_ai_reply, pangu_reasoner, pangu_v35_128k, qwen3_32b
from common.tools import activate_conn, get_ctcae_results_json
from apps.system.models import ModelInvocationLog

from common.utils import get_general_prompt_sql
from common.constants import *
from script.ae_grade.prompt_config import PROMPT_TEMPLATE_20250429, PROMPT_TEMPLATE3
# 导入新的PocketFlow优化架构
from script.ae_grade.main import process_ae_analysis

def add_timestamp(func):
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        return result, end_time
    return wrapper


async def async_get_responses(prompt):
    wrapped_deepseek = add_timestamp(deepseek_r1_qwen_32b)
    wrapped_pangu = add_timestamp(qwen3_32b)

    task1 = asyncio.to_thread(wrapped_deepseek, prompt)
    task2 = asyncio.to_thread(wrapped_pangu, prompt)
    return await asyncio.gather(task1, task2)


def get_responses_sync(prompt):
    return asyncio.run(async_get_responses(prompt))


def process_task(task_id):
    try:
        print(f"Task: {task_id}")
        # task = AeTrackerTask.objects.filter(
        #     status__in=['TODO', 'IN_PROGRESS'],
        #     status='TODO',
        #     category=AeTrackerTask.AE_RECOGNITION,
        #     delete_flag=0
        # ).first()  # 每次只处理一个任务

        task = AeTrackerTask.objects.filter(id=task_id, category=AeTrackerTask.AE_RECOGNITION, delete_flag=0).first()
        if not task:
            print(f"没有发现任务")
            return

        # 使用乐观锁更新任务状态，防止任务重复处理
        with transaction.atomic():
            rows_updated = AeTrackerTask.objects.filter(
                id=task.id,
                data_version=task.data_version,
                delete_flag=0,
            ).update(
                status='IN_PROGRESS',
                data_version=task.data_version + 1,  # 增加 data_version
                start_time=datetime.datetime.now(),
                update_time=datetime.datetime.now(),
            )

        if rows_updated != 1:
            return

        subject = task.subject
        subject_item = task.subject_item

        print(f"Processing: {task.id}")
        print(f"Subject: {subject.subject_id}")
        print(f"SubjectItem: {subject_item.subject_item_id}")

        # 查询化验结果
        test_results = TestResult.objects.filter(
            subject_id=subject.subject_id, subject_item_id=subject_item.subject_item_id, delete_flag=0).all()
        if subject_item.item_type == '1':
            test_results_list = list(test_results.values(
                'id', 'test_type', 'test_code', 'test_name', 'test_unit',
                'test_value', 'test_flag', 'reference_value',
                'reference_range_min', 'reference_range_max',
                'abnormal_symbol'
            ))
            print(f"test_results: {test_results}")
        else:
            test_results_list = list(test_results.values(
                'id', 'test_name'
            ))
            print(f"test_results: {test_results}")
        # print(f"test_results---------------=======------: \n\n")
        # print(test_results_list)
        # print(f"test_results---------------=======------: \n\n")


# Type '1' - 检验报告数据：
        if subject_item.item_type == '1':
            # ========== 尝试使用PocketFlow优化处理，失败则降级到原始逻辑 ==========
            pocketflow_success = False
            llm1_data = None
            llm2_data = None

            try:
                logger.info("使用PocketFlow优化架构处理AE识别")
                start_time_pf = time.time()

                # 预加载AE识别提示词模板（在同步上下文中）
                ae_prompt_templates = None
                try:
                    ae_prompt_content = get_general_prompt_sql(AE_RECOGNITION)
                    # print("-"*40)
                    # print(ae_prompt_content)
                    # print("-"*40)
                    if ae_prompt_content:
                        ae_prompt_templates = json.loads(ae_prompt_content)
                        logger.info("成功预加载AE识别提示词模板")
                    else:
                        logger.warning("未找到AE识别提示词模板，将使用SmartPromptBuilder的默认模板")
                        ae_prompt_templates = None  # 传入None，让SmartPromptBuilder使用默认模板
                except Exception as e:
                    logger.error(f"预加载AE识别提示词模板失败: {e}，将使用SmartPromptBuilder的默认模板")
                    ae_prompt_templates = None  # 传入None，让SmartPromptBuilder使用默认模板

                # 直接传递原始数据库数据给process_ae_analysis，包含任务信息用于日志记录
                result = process_ae_analysis(
                    test_results=test_results_list,  # 直接使用原始数据库查询结果
                    prompt_templates=ae_prompt_templates,  # 传递预加载的提示词模板
                    task_info={  # 传递任务信息用于日志记录
                        'task_id': task_id,
                        'create_user': task.create_user,
                        'create_name': task.create_name,
                        'business_id': task.patient_id
                    }
                )

                # 检查PocketFlow结果是否有效
                if result and 'results' in result and result['results']:
                    # 从PocketFlow结果中提取双模型数据
                    dual_model_data = result.get('dual_model_data', {})

                    # 使用双模型的原始解析结果
                    llm1_results = dual_model_data.get('llm1_results', [])
                    llm2_results = dual_model_data.get('llm2_results', [])

                    # 如果没有双模型数据，使用主结果作为备用
                    if not llm1_results and not llm2_results:
                        pocketflow_results = result['results']
                        llm1_results = pocketflow_results
                        llm2_results = pocketflow_results

                    llm1_data = {"results": llm1_results}
                    llm2_data = {"results": llm2_results}

                    pocketflow_success = True
                    processing_time = time.time() - start_time_pf
                    logger.info(f"PocketFlow处理成功，耗时: {processing_time:.2f}秒")
                else:
                    logger.warning("PocketFlow返回空结果，降级到原始逻辑")
                    raise Exception("PocketFlow returned empty or invalid result")

            except Exception as e:
                logger.error(f"PocketFlow处理出错: {e}")
                logger.info("降级到原始双模型LLM处理逻辑")
                pocketflow_success = False

            # ========== 如果PocketFlow失败，使用原始双模型LLM逻辑 ==========
            if not pocketflow_success:
                # 将数据转换为JSON格式字符串
                # 英文 key 到中文 key 的映射
                key_mapping = {
                    'id': '序号',
                    'test_name': '检查项目',
                    'test_value': '结果',
                    'test_flag': '状态',
                    'test_unit': '单位',
                    'reference_value': '生物参考区间',
                    'reference_range_min': '参考范围最小值',
                    'reference_range_max': '参考范围最大值',
                    'test_code': '检查代码'
                }
                # 替换 key
                test_results_list = [{key_mapping.get(k, k): v for k, v in data.items()} for data in test_results_list]

                # test_flag 转 状态
                flag_to_status = {
                    0: '-',
                    1: '↑',
                    2: '↓'
                }
                # 补充 "状态" 字段
                for data in test_results_list:
                    data['状态'] = flag_to_status.get(data.get('状态'), '-')

                temp = PROMPT_TEMPLATE_20250429
                if len(test_results_list) > 20:
                    batch_size = 20
                    total_batches = (len(test_results_list) + batch_size - 1) // batch_size
                    all_dicts_llm1_response = []
                    all_dicts_llm2_response = []
                    model_invocation_log_instances = []
                    for batch_idx in range(total_batches):
                        start_idx = batch_idx * batch_size
                        end_idx = min(start_idx + batch_size, len(test_results_list))
                        batch = test_results_list[start_idx:end_idx]

                        # 处理当前批次数据
                        test_results_json = json.dumps(batch, ensure_ascii=False, indent=2)
                        print(f'Batch {batch_idx + 1}/{total_batches} JSON: {test_results_json}')

                        template = Template(temp)
                        prompt = template.safe_substitute(test_results=test_results_json)
                        print(f"Prompt: {prompt}")
                        start_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        (llm1_response_batch, deepseek_end_time), (llm2_response_batch,
                                                                   pangu_end_time) = get_responses_sync(prompt)
                        print(f"DeepSeek-R1-Distill-Qwen-32B Response: {llm1_response_batch}")
                        print(f"qwen3-32b Response: {llm2_response_batch}")
                        print('*' * 100)
                        llm1_response_batch_first = llm1_response_batch
                        llm1_response_batch = get_json_from_ai_reply(llm1_response_batch)
                        generated_tokens, inputText, outputText, think = get_generated_tokens_think_from_ai_reply(
                            llm1_response_batch_first, prompt)
                        activate_conn(connection)
                        result = {}
                        result['task_id'] = task_id
                        result['create_user'] = task.create_user
                        result['create_name'] = task.create_name
                        result['category'] = 'AE_TEST_REPORT'
                        result['model_name'] = 'DeepSeek-R1-Distill-Qwen-32B'
                        result['start_time'] = start_time
                        result['end_time'] = deepseek_end_time
                        result['input_text'] = inputText
                        result['think_text'] = think
                        result['output_text'] = outputText
                        result['prompt_tokens'] = generated_tokens['prompt_tokens']
                        result['completion_tokens'] = generated_tokens['completion_tokens']
                        result['business_id'] = task.patient_id
                        model_invocation_log_instances.append(ModelInvocationLog(**result))
                        llm2_response_batch_first = llm2_response_batch
                        llm2_response_batch = get_json_from_ai_reply(llm2_response_batch)
                        generated_tokens, inputText, outputText, think = get_generated_tokens_think_from_ai_reply(
                            llm2_response_batch_first, prompt)
                        result['model_name'] = 'qwen3-32b'
                        result['input_text'] = inputText
                        result['think_text'] = think
                        result['end_time'] = pangu_end_time
                        result['output_text'] = outputText
                        result['prompt_tokens'] = generated_tokens['prompt_tokens']
                        result['completion_tokens'] = generated_tokens['completion_tokens']
                        model_invocation_log_instances.append(ModelInvocationLog(**result))
                        all_dicts_llm1_response.append(llm1_response_batch)
                        all_dicts_llm2_response.append(llm2_response_batch)
                    activate_conn(connection)
                    ModelInvocationLog.objects.bulk_create(model_invocation_log_instances)
                    llm1_data = {"results": []}
                    for d in all_dicts_llm1_response:
                        if "results" in d:
                            llm1_data["results"].extend(d["results"])
                    llm2_data = {"results": []}
                    for d in all_dicts_llm2_response:
                        if "results" in d:
                            llm2_data["results"].extend(d["results"])
                else:
                    test_results_json = json.dumps(test_results_list, ensure_ascii=False, indent=2)
                    print(f'test_results_json: {test_results_json}')
                    template = Template(temp)
                    prompt = template.safe_substitute(test_results=test_results_json)
                    start_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    (llm1_response, deepseek_end_time), (llm2_response, pangu_end_time) = get_responses_sync(prompt)
                    generated_tokens, inputText, outputText, think = get_generated_tokens_think_from_ai_reply(llm1_response,
                                                                                                              prompt)
                    print(f"Prompt: {prompt}")
                    activate_conn(connection)
                    result = {}
                    result['task_id'] = task_id
                    result['create_user'] = task.create_user
                    result['create_name'] = task.create_name
                    result['category'] = 'AE_TEST_REPORT'
                    result['model_name'] = 'DeepSeek-R1-Distill-Qwen-32B'
                    result['start_time'] = start_time
                    result['end_time'] = deepseek_end_time
                    result['input_text'] = inputText
                    result['think_text'] = think
                    result['output_text'] = outputText
                    result['prompt_tokens'] = generated_tokens['prompt_tokens']
                    result['completion_tokens'] = generated_tokens['completion_tokens']
                    result['business_id'] = task.patient_id
                    ModelInvocationLog.objects.create(**result)
                    generated_tokens, inputText, outputText, think = get_generated_tokens_think_from_ai_reply(llm2_response,
                                                                                                              prompt)

                    result['model_name'] = 'qwen3-32b'
                    result['input_text'] = inputText
                    result['think_text'] = think
                    result['end_time'] = pangu_end_time
                    result['output_text'] = outputText
                    result['prompt_tokens'] = generated_tokens['prompt_tokens']
                    result['completion_tokens'] = generated_tokens['completion_tokens']
                    ModelInvocationLog.objects.create(**result)
                    print(f"DeepSeek-R1-Distill-Qwen-32B Response: {llm1_response}")
                    llm1_data = get_json_from_ai_reply(llm1_response)
                    print(f"qwen3-32b Response: {llm2_response}")
                    llm2_data = get_json_from_ai_reply(llm2_response)

            # ========== 统一数据处理逻辑（适用于PocketFlow和原始LLM） ==========
            # 中文 key 到目标英文 key 的映射
            # key_mapping = {
            #     '序号': 'id',
            #     '不良事件名称': 'ae_name',
            #     'CTCAE分级': 'ae_grade',
            #     '不良事件定义': 'ae_desc',
            #     '状态': 'abnormal_flag'
            # }

            # 状态 转 abnormal_flag
            # status_to_abnormal_flag = {
            #     '-': 0,
            #     '↑': 1,
            #     '↓': 1
            # }

            print(llm1_data)
            llm1_data = llm1_data['results']
            # 转换数据
            # llm1_data = [{key_mapping[k]: v for k, v in data.items() if k in key_mapping} for data in llm1_data]
            # for data in llm1_data:
            #     data['abnormal_flag'] = status_to_abnormal_flag.get(data['abnormal_flag'], '')

            llm1_data_map = {i['id']: i for i in llm1_data if 'id' in i and i['id'] is not None}

            # print(llm2_data)
            llm2_data = llm2_data['results']
            #
            # llm2_data = [{key_mapping[k]: v for k, v in data.items() if k in key_mapping} for data in llm2_data]
            # for data in llm2_data:
            #     data['abnormal_flag'] = status_to_abnormal_flag.get(data['abnormal_flag'], '')

            llm2_data_map = {i['id']: i for i in llm2_data if 'id' in i and i['id'] is not None}
        # {
        #     1: {'id': 1, 'name': 'Alice'},
        #     2: {'id': 2, 'name': 'Bob'},
        #     3: {'id': 3, 'name': 'Charlie'}
        # }
        else:
            # 非Type '1' - 病史/文本数据
            # temp = PROMPT_TEMPLATE3
            temp = get_general_prompt_sql(AE_RECOGNITION_TEXT)
            key_mapping = {
                'id': '序号',
                'test_name': '检查项目'
            }
            # 替换 key
            test_results_list = [{key_mapping.get(k, k): v for k, v in data.items()} for data in test_results_list]
            ctcae_results_json = get_ctcae_results_json(test_results_list)
            ctcae_results_json = json.dumps(ctcae_results_json, ensure_ascii=False, indent=2)
            print(f'ctcae_results_json: {ctcae_results_json}')

            # 将数据转换为JSON格式字符串
            test_results_json = json.dumps(test_results_list, ensure_ascii=False, indent=2)
            print(f'test_results_json: {test_results_json}')

            template = Template(temp)  # PROMPT_TEMPLATE3
            prompt = template.safe_substitute(test_results=test_results_json, ctcae_results=ctcae_results_json)
            print(f"Prompt: {prompt}")
            start_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            (llm1_response, deepseek_end_time), (llm2_response, pangu_end_time) = get_responses_sync(prompt)
            generated_tokens, inputText, outputText, think = get_generated_tokens_think_from_ai_reply(llm1_response,
                                                                                                      prompt)
            activate_conn(connection)
            result = {}
            result['task_id'] = task_id
            result['create_user'] = task.create_user
            result['create_name'] = task.create_name
            result['category'] = 'AE_MEDICAL_RECORD'
            result['model_name'] = 'DeepSeek-R1-Distill-Qwen-32B'
            result['start_time'] = start_time
            result['end_time'] = deepseek_end_time
            result['input_text'] = inputText
            result['think_text'] = think
            result['output_text'] = outputText
            result['prompt_tokens'] = generated_tokens['prompt_tokens']
            result['completion_tokens'] = generated_tokens['completion_tokens']
            result['business_id'] = task.patient_id
            ModelInvocationLog.objects.create(**result)
            generated_tokens, inputText, outputText, think = get_generated_tokens_think_from_ai_reply(llm2_response,
                                                                                                      prompt)

            result['model_name'] = 'qwen3-32b'
            result['input_text'] = inputText
            result['think_text'] = think
            result['end_time'] = pangu_end_time
            result['output_text'] = outputText
            result['prompt_tokens'] = generated_tokens['prompt_tokens']
            result['completion_tokens'] = generated_tokens['completion_tokens']
            ModelInvocationLog.objects.create(**result)
            # llm1_response = deepseek_r1_qwen_32b(prompt)
            # llm2_response = pangu_v35_llm(prompt) pass
            # llm2_response = pangu_v35_128k(prompt)
            # llm1_response, llm2_response = get_responses_sync(prompt)
            print(f"DeepSeek-R1-Distill-Qwen-32B Response: {llm1_response}")
            llm1_data = get_json_from_ai_reply(llm1_response)
            # llm1_data = sorted(llm1_data, key=lambda x: x['id'])
            print(llm1_data)
            key_mapping = {
                '序号': 'id',
                '不良事件名称': 'ae_name',
                'CTCAE分级': 'ae_grade',
                '不良事件定义': 'ae_desc',
                '异常值标记': 'abnormal_flag'
            }
            # 转换数据
            llm1_data = [{key_mapping[k]: v for k, v in data.items() if k in key_mapping} for data in llm1_data]
            llm1_data_map = {i['id']: i for i in llm1_data}

            print(f"qwen3-32b Response: {llm2_response}")
            llm2_data = get_json_from_ai_reply(llm2_response)
            # llm2_data = sorted(llm2_data, key=lambda x: x['id'])
            print(llm2_data)
            llm2_data = [{key_mapping[k]: v for k, v in data.items() if k in key_mapping} for data in llm2_data]
            llm2_data_map = {i['id']: i for i in llm2_data}


        # ========== 统一数据处理逻辑（与旧版本完全一致） ==========
        # 待更新的对象列表
        update_list = []

        activate_conn(connection)
        """
                 1. 迭代原始数据，根据原始id提取处理后的三个字段：
                 item的数据是第一个大模型的结果：ae_name，ae_grade，ae_desc
                 2. 判断abnormal_flag
                 3. test_result.ae_ai_result_list = [llm1, llm2]
        """
        for test_result in test_results:
            llm1 = llm1_data_map.get(test_result.id)
            if not llm1:
                llm1 = {
                    "id": test_result.id,
                    "ae_name": None,
                    "ae_grade": 0,
                    "ae_desc": None,
                    "abnormal_flag": 0
                }

            # 安全地移除id字段，避免KeyError
            if 'id' in llm1:
                llm1.pop('id')
            else:
                logger.error(f"llm1数据缺少id字段，test_result.id={test_result.id}, llm1={llm1}")
            llm1['model'] = 'DeepSeek-R1-Distill-Qwen-32B'

            if not llm1["ae_name"]:
                llm1["ae_name"] = None

            try:
                llm1["ae_grade"] = int(llm1["ae_grade"])
            except:
                pass

            test_result.ae_name = llm1["ae_name"]
            test_result.ae_grade = llm1["ae_grade"]
            test_result.ae_desc = llm1["ae_desc"][:100] if llm1["ae_desc"] else ''

            ae_grade = None
            try:
                ae_grade = int(llm1["ae_grade"])
                if ae_grade > 0:
                    test_result.abnormal_flag = "1"
            except:
                pass

            llm2 = llm2_data_map.get(test_result.id)
            if not llm2:
                llm2 = {
                    "id": test_result.id,
                    "ae_name": None,
                    "ae_grade": 0,
                    "ae_desc": None,
                    "abnormal_flag": 0
                }

            # 安全地移除id字段，避免KeyError
            if 'id' in llm2:
                llm2.pop('id')
            else:
                logger.error(f"llm2数据缺少id字段，test_result.id={test_result.id}, llm2={llm2}")
            llm2['model'] = 'qwen3-32b'

            if not llm2["ae_name"]:
                llm2["ae_name"] = None

            try:
                llm2["ae_grade"] = int(llm2["ae_grade"])
            except:
                pass

            test_result.ae_ai_result_list = [llm1, llm2]
            test_result.ae_ai_result_flag = int(
                not (all([llm1["ae_name"] == llm2["ae_name"], llm1["ae_grade"] == llm2["ae_grade"]])))
            update_list.append(test_result)

        activate_conn(connection)
        print(f"update_list：{update_list}")

        # 批量更新TestResult
        if update_list:
            with transaction.atomic():
                unique_ae_names = set([obj.test_name for obj in update_list if
                                       obj.test_name is not None and obj.test_flag is not None and obj.test_flag > 0])

                test_results_history = TestResult.objects.filter(subject_id=subject_item.subject_id,
                                                         test_name__in=unique_ae_names, medical_history_flag=1,
                                                         delete_flag=0)
                # 用于存储分组信息
                grouped_results = {}
                # 对结果进行分组
                for result in test_results_history:
                    key = (result.subject_id, result.test_name)
                    if key not in grouped_results:
                        grouped_results[key] = []
                    grouped_results[key].append(result)

                # 遍历每个分组
                for group in grouped_results.values():
                    latest_result = max(group, key=lambda x: x.create_time)
                    target_test_name = latest_result.test_name
                    target_medical_history_flag = latest_result.medical_history_flag
                    target_ae_name = latest_result.ae_name

                    for obj in update_list:
                        if obj.test_name == target_test_name:
                            obj.medical_history_flag = target_medical_history_flag
                            obj.ae_name = target_ae_name

                with transaction.atomic():
                    TestResult.objects.bulk_update(
                        update_list,
                        ["medical_history_flag", "ae_name", "ae_grade", "ae_desc", "ae_edit_flag",
                         "ae_ai_result_list", "ae_ai_result_flag", "abnormal_flag"]
                    )

        # 更新SubjectItem和AeTrackerTask状态
        with transaction.atomic():
            print(f"COMPLETED: {subject_item.subject_item_id}")
            task.status = 'COMPLETED'
            task.end_time = datetime.datetime.now()
            task.save()
            SubjectItem.objects.filter(subject_item_id=subject_item.subject_item_id).update(ae_ai_current_step=3)

    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"ERROR: 处理任务 {task_id} 时发生异常: {e}")
        activate_conn(connection)

        # 更新任务状态为错误
        try:
            with transaction.atomic():
                logger.error(f"任务处理失败: {subject_item.subject_item_id}")
                task.status = 'ERROR'
                task.end_time = datetime.datetime.now()
                task.update_time = datetime.datetime.now()
                task.save()
                SubjectItem.objects.filter(subject_item_id=subject_item.subject_item_id).update(ae_ai_current_step=2)
        except Exception as save_error:
            logger.error(f"保存错误状态失败: {save_error}")


def main():
    parser = argparse.ArgumentParser(description="AE识别任务")
    parser.add_argument('--task_id', type=int, required=True, help="task_id")
    args = parser.parse_args()
    process_task(args.task_id)  # 现在是同步调用，不需要asyncio.run


if __name__ == "__main__":
    # python -m script.ae_grade_recognition --task_id=1
    main()
