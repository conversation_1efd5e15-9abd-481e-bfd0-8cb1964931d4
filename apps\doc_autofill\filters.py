from django_filters import rest_framework as filters

from . import models


# 模板中心列表过滤
class HospDocTmplCenterListFilter(filters.FilterSet):
    # hospital 表 hosp_name 字段过滤
    hosp_name = filters.CharFilter(field_name='hospital__hosp_name', lookup_expr='icontains',label="中心名称")
    status_code = filters.ChoiceFilter(field_name='status_code', choices=models.HospDocTmplCenter.StatusCodeEnum.choices, label="模板状态")
    class Meta:
        model = models.HospDocTmplCenter
        fields = ['hosp_name', 'status_code']

# 中心模板文件过滤
class HospDocTmplInfoListFilter(filters.FilterSet):
    # hospital 表 hosp_id 字段过滤
    hosp_id = filters.CharFilter(field_name='hospital__hosp_id', lookup_expr='exact',label="中心ID")
    # hosp_doc_tmpl_info 表 file_name 字段过滤
    file_name = filters.CharFilter(field_name='file_name', lookup_expr='icontains',label="文件名称")

    class Meta:
        model = models.HospDocTmplInfo
        fields = ['hosp_id','file_name']


# 项目中心列表过滤
class ProjectSiteDocCenterListFilter(filters.FilterSet):
    # project_id（项目ID）、--入口约束 project_site_id（铨融中心,是否展示看产品，理论上应该为这个层级，对应 hosp_department_no 中心编号）、hosp_id（中心医院）
    # hospital 表 hosp_name 字段过滤
    project_id = filters.CharFilter(field_name='project__project_id', lookup_expr='exact',label="项目ID")
    hosp_name = filters.CharFilter(field_name='hospital__hosp_name', lookup_expr='icontains',label="中心名称")
    status_code = filters.ChoiceFilter(field_name='status_code', choices=models.ProjectSiteDocCenter.StatusCodeEnum.choices, label="项目文件状态")
    class Meta:
        model = models.ProjectSiteDocCenter
        fields = ['project_id', 'hosp_name', 'status_code']

# 项目中心文件过滤
class ProjectSiteDocInfoListFilter(filters.FilterSet):

    project_site_id = filters.CharFilter(field_name='project_site__project_site_id', lookup_expr='exact',label="项目站点ID")
    # project_site_doc_info 表 file_name 字段过滤
    file_name = filters.CharFilter(field_name='file_name', lookup_expr='icontains',label="文件名称")

    class Meta:
        model = models.ProjectSiteDocInfo
        fields = ['project_site_id','file_name']
# 自动回填任务
class DocAutofillTaskCategoryFilter(filters.FilterSet):
    category = filters.CharFilter(field_name='category', choices=models.DocAutofillTask.Category.choices,  label='任务分类')
