import os
import logging
import uuid
from datetime import timed<PERSON>ta

from minio import <PERSON><PERSON>

from django.http import FileResponse
from django.conf import settings
from django.shortcuts import render
from django.db import transaction
from django.db.models import Subquery, OuterRef
from django.db.models import Count
from rest_framework.viewsets import GenericViewSet
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.fields import <PERSON>r<PERSON><PERSON>
from rest_framework import filters, viewsets
from rest_framework.mixins import ListModelMixin, CreateModelMixin, UpdateModelMixin, DestroyModelMixin
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.filters import OrderingFilter
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.renderers import BaseRenderer
from rest_framework.exceptions import ValidationError, NotFound, APIException
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import APIException

from django_filters.rest_framework import DjangoFilterBackend
from apps.subject.models import Subject, SubjectItem, SubjectVisit, SubjectEpoch

from rest_pandas.views import PandasViewBase
from rest_pandas import PandasExcelRenderer
from openpyxl.styles import Font, Border
from openpyxl.styles import Alignment

from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema, OpenApiParameter

from common.auth import ERPSysJWTAuthentication
from common.utils import calculate_file_hash
from common.minio_client import get_minio_client
from common.pagination import StandardResultsSetPagination
from common.tools import trigger_dag
from common.ocr_tools import heic_to_jpg_stream, heic_to_jpg_file, doc_to_docx_file, bytes_word_to_pdf

from apps.project.models import Project, ProjectSite
from apps.subject.models import Subject
from . import serializers
from . import models
from . import filters
from typing import List, Dict, Union, Tuple
from apps.ae_tracker.models import AeTrackerTask, TestResult, TestOcrResult
from apps.subject.models import Subject, SubjectItem, SubjectVisit, SubjectEpoch
from apps.system.models import OperationLog
from apps.subject_medical.models import SubjectMedicalInfo
from apps.medical_collection.models import MedicalCollectionTask

class FileAlreadyExistsError(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "文件已存在！！！"
    default_code = "error"


class TaskExistsStatusIN_PROGRESS(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "异常值tracker任务流程还未结束,正在处理中"
    default_code = "error"


class CrfWordTaskExistsStatusIN_PROGRESS(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "(CRF|Word小结)任务流程还未结束,正在处理中"
    default_code = "error"


logger = logging.getLogger('app')


class OCRTaskInProgressError(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "OCR任务正在进行中，请勿重复提交"
    default_code = "ocr_in_progress"


class BaseAPIView(APIView):
    authentication_classes = [ERPSysJWTAuthentication]
    permission_classes = [IsAuthenticated]


class BaseViewSet(BaseAPIView, GenericViewSet):
    pass


class BaseListViewSet(BaseViewSet, ListModelMixin):
    filter_backends = (DjangoFilterBackend, OrderingFilter)


def _sort_ocr_results(ocr_data: List[Dict], return_blocks: bool = False) -> Union[str, Tuple[str, List[List[Dict]]]]:
    """
    对OCR识别结果进行行列排序，以还原图片中的文本布局。
    
    Args:
        ocr_data (list): 一个包含字典的列表，每个字典代表一个识别出的文本块，
                         需要包含 "words" (文本内容) 和 "location" (边界框坐标)。
        return_blocks (bool): 是否同时返回排序后的数据块列表，默认False
    
    Returns:
        Union[str, Tuple[str, List[List[Dict]]]]:
            - 当 return_blocks=False 时，返回排序后的markdown格式字符串
            - 当 return_blocks=True 时，返回元组 (markdown_result, sorted_words_block_list)
    """
    if not ocr_data:
        if return_blocks:
            return "", []
        return ""

    # 1. 预处理：创建数据副本并计算每个文本块的Y坐标信息
    processed_boxes = []
    for box in ocr_data:
        # 创建原始数据的副本，避免修改原始数据
        box_copy = box.copy()
        y_top = box_copy['location'][0][1]
        y_bottom = box_copy['location'][3][1]
        box_copy['y_top'] = y_top
        box_copy['y_bottom'] = y_bottom
        box_copy['y_center'] = (y_top + y_bottom) / 2
        box_copy['height'] = y_bottom - y_top
        processed_boxes.append(box_copy)

    # 2. 按Y中心坐标排序
    sorted_boxes = sorted(processed_boxes, key=lambda x: x['y_center'])

    # 3. 基于严格的Y坐标重叠判断进行分组
    lines = []
    used_indices = set()

    for i, current_box in enumerate(sorted_boxes):
        if i in used_indices:
            continue

        current_line = [current_box]
        used_indices.add(i)

        # 查找与当前文本块在同一行的其他文本块
        for j, other_box in enumerate(sorted_boxes):
            if j in used_indices or j <= i:
                continue

            # 计算Y坐标重叠
            overlap_top = max(current_box['y_top'], other_box['y_top'])
            overlap_bottom = min(current_box['y_bottom'], other_box['y_bottom'])
            overlap_height = max(0, overlap_bottom - overlap_top)

            # 计算重叠比例（相对于两个文本块的较小高度）
            min_height = min(current_box['height'], other_box['height'])
            overlap_ratio = overlap_height / min_height if min_height > 0 else 0

            # 中心点距离
            center_distance = abs(current_box['y_center'] - other_box['y_center'])

            # 严格的同行判断条件
            max_center_distance = min_height / 2

            if overlap_ratio >= 0.7 or center_distance <= max_center_distance:
                current_line.append(other_box)
                used_indices.add(j)

        lines.append(current_line)

    # 4. 行内排序和格式化输出
    output_lines = []
    sorted_lines = []  # 存储排序后的数据块列表

    for line in lines:
        # 对每一行内的文本框根据x坐标从左到右排序
        sorted_line = sorted(line, key=lambda x: x['location'][0][0])

        # 如果需要返回数据块，清理临时字段并创建干净的副本
        if return_blocks:
            clean_line = []
            for box in sorted_line:
                # 创建不包含临时字段的干净副本
                clean_box = {k: v for k, v in box.items()
                           if k not in ['y_top', 'y_bottom', 'y_center', 'height']}
                clean_line.append(clean_box)
            sorted_lines.append(clean_line)

        # 将行内所有文本框的文字用空格连接起来
        line_text = " ".join([box['words'] for box in sorted_line])
        output_lines.append(line_text)

    # 5. 将所有行用换行符连接成最终的输出字符串
    markdown_result = "\n".join(output_lines)

    if return_blocks:
        return markdown_result, sorted_lines
    return markdown_result


def _generate_markdown_with_line_break(ocr_data: List[Dict]) -> str:
    """
    使用line_break标记生成markdown格式的文本
    
    Args:
        ocr_data: OCR识别结果列表，每个元素包含words和可选的line_break字段
    
    Returns:
        str: 基于line_break标记生成的markdown格式文本
    """
    if not ocr_data:
        return ""
    
    output_lines = []
    current_line = []
    
    for block in ocr_data:
        words = block.get('words', '')
        line_break = block.get('line_break', False)
        
        if words:  # 只处理非空文本
            current_line.append(words)
            
            # 如果需要换行或遇到强制换行标记
            if line_break:
                output_lines.append(" ".join(current_line))
                current_line = []
    
    # 添加最后一行的内容
    if current_line:
        output_lines.append(" ".join(current_line))
    
    return "\n".join(output_lines)


def generate_ocr_text_mask(ocr_box: List[Dict], original_filename: str) -> str:
    """
    根据ocr_box生成ocr_text_mask格式的文本
    
    Args:
        ocr_box: OCR坐标信息列表
        original_filename: 原始文件名
    
    Returns:
        str: 格式化的ocr_text_mask文本
    """
    logger.info(f"generate_ocr_text_mask调用 - ocr_box类型: {type(ocr_box)}, original_filename: {original_filename}")
    
    if not ocr_box or not original_filename:
        logger.warning(f"参数为空 - ocr_box: {bool(ocr_box)}, original_filename: {bool(original_filename)}")
        return ""
    
    # 处理ocr_box可能是字符串的情况
    import json
    if isinstance(ocr_box, str):
        logger.info(f"ocr_box是字符串，尝试解析JSON")
        try:
            ocr_box = json.loads(ocr_box)
            logger.info(f"JSON解析成功，解析后类型: {type(ocr_box)}, 长度: {len(ocr_box) if isinstance(ocr_box, list) else 'N/A'}")
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"Invalid ocr_box format: {ocr_box}, 错误: {e}")
            return ""
    
    if not isinstance(ocr_box, list):
        logger.error(f"ocr_box should be a list, got {type(ocr_box)}")
        return ""
    
    result_lines = []
    
    # 统一按页码数量处理：单页时不显示页码，多页时显示页码
    logger.info(f"ocr_box页数: {len(ocr_box)}")
    
    if len(ocr_box) == 1:
        # 单页情况：不显示页码
        page_data = ocr_box[0]
        logger.info(f"单页处理 - page_data类型: {type(page_data)}")
        
        if not isinstance(page_data, dict):
            logger.error(f"page_data should be a dict, got {type(page_data)}: {page_data}")
            return ""
        
        words_block_list = page_data.get('words_block_list', [])
        logger.info(f"words_block_list长度: {len(words_block_list)}")
        
        # 检查是否有line_break标记
        has_line_break = any(block.get('line_break', False) for block in words_block_list)
        logger.info(f"是否有line_break标记: {has_line_break}")
        
        if has_line_break:
            # 使用line_break标记生成文本
            sorted_text = _generate_markdown_with_line_break(words_block_list)
            logger.info(f"使用line_break方法生成文本，长度: {len(sorted_text)}")
        else:
            # 使用原始的坐标排序方法
            sorted_text = _sort_ocr_results(words_block_list)
            logger.info(f"使用坐标排序方法生成文本，长度: {len(sorted_text)}")
        
        result_lines.append(f"[{original_filename}]")
        result_lines.append(sorted_text)
    else:
        # 多页情况：显示页码
        logger.info("多页处理")
        result_lines.append(f"[{original_filename}]")
        
        for i, page_data in enumerate(ocr_box):
            logger.info(f"处理第{i+1}页 - page_data类型: {type(page_data)}")
            
            if not isinstance(page_data, dict):
                logger.error(f"page_data should be a dict, got {type(page_data)}: {page_data}")
                continue
                
            page_num = page_data.get('page', 1)
            words_block_list = page_data.get('words_block_list', [])
            logger.info(f"第{page_num}页 words_block_list长度: {len(words_block_list)}")
            
            # 检查是否有line_break标记
            has_line_break = any(block.get('line_break', False) for block in words_block_list)
            logger.info(f"第{page_num}页是否有line_break标记: {has_line_break}")
            
            if has_line_break:
                # 使用line_break标记生成文本
                sorted_text = _generate_markdown_with_line_break(words_block_list)
                logger.info(f"第{page_num}页使用line_break方法生成文本，长度: {len(sorted_text)}")
            else:
                # 使用原始的坐标排序方法
                sorted_text = _sort_ocr_results(words_block_list)
                logger.info(f"第{page_num}页使用坐标排序方法生成文本，长度: {len(sorted_text)}")
            
            result_lines.append(f"[Page {page_num}]")
            result_lines.append(sorted_text)
            result_lines.append("")  # 页面之间添加空行
    
    final_result = "\n".join(result_lines).rstrip()
    logger.info(f"最终结果长度: {len(final_result)}")
    
    return final_result


class MedicalInfoLiteListViewSet(BaseListViewSet, CreateModelMixin):
    parser_classes = (MultiPartParser, )  # 支持文件上传
    queryset = models.MedicalInfoLite.objects.filter(delete_flag=0).select_related(
    'file', 'file_masked', 'project', 'project_site', 'subject', 'subject_visit')
    serializer_class = serializers.MedicalInfoLiteSerializer
    filterset_class = filters.MedicalInfoLiteFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    ordering = ['create_time']

    @extend_schema(
        summary='病历文件列表',
        tags=['病历归集'],
        # request=serializers.ProjectMaterialLatestRequestSerializer,
        responses=serializers.MedicalInfoLiteSerializer
    )
    def list(self, request, format=None):
        return super().list(request, format)

    @extend_schema(
        summary='上传病历文件',
        tags=['病历归集'],
        request=serializers.MedicalInfoLiteCreateRequestSerializer,
        responses=serializers.MedicalInfoFileSerializer
    )
    def create(self, request, format=None):
        file = request.FILES.get('file')
        project_id = request.POST.get('project_id')
        project_site_id = request.POST.get('project_site_id')
        subject_visit_id = request.POST.get('subject_visit_id')
        subject_id = request.POST.get('subject_id')
        subject_item_id = request.POST.get('subject_item_id')
        subject_epoch_id = request.POST.get('subject_epoch_id')
        project_no = request.POST.get('project_no')

        task = AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[AeTrackerTask.IN_PROGRESS, AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        if task:
            raise TaskExistsStatusIN_PROGRESS()

        task1 = MedicalCollectionTask.objects.filter(
            subject_id=subject_id,
            subject_visit_id=subject_visit_id,
            status__in=[AeTrackerTask.IN_PROGRESS, AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        if task1:
            raise CrfWordTaskExistsStatusIN_PROGRESS()

        serializer = serializers.MedicalInfoLiteCreateRequestSerializer(data=request.data)
        if not serializer.is_valid():
            raise ValidationError(serializer.errors)

        if not file:
            raise ValidationError({'file': ['未提供文件']})
        valid_extensions = ['.jpg', '.jpeg', '.png', '.heic', '.pdf', '.docx', '.doc']
        file_extension = '.' + file.name.split('.')[-1].lower()
        if file_extension not in valid_extensions:
            raise ValidationError({'file': ['病史信息仅支持图片、pdf、word类型文件']})
        # 检查数据是否存在
        try:
            project = Project.objects.filter(delete_flag=0).get(project_id=project_id)
        except Project.DoesNotExist:
            raise NotFound({'project_id': ['项目不存在']})

        try:
            project_site = ProjectSite.objects.filter(delete_flag=0).get(project_site_id=project_site_id)
        except Project.DoesNotExist:
            raise NotFound({'project_site': ['项目中心不存在']})

        try:
            subject = Subject.objects.filter(delete_flag=0).get(subject_id=subject_id)
        except Subject.DoesNotExist:
            raise NotFound({'subject_id': ['受试者不存在']})
        try:
            subject_item = SubjectItem.objects.filter(delete_flag=0).get(subject_item_id=subject_item_id)
        except Subject.DoesNotExist:
            raise NotFound({'subject_id': ['受试者操作项不存在']})

        # 计算文件的 SHA-256 哈希值
        base_name, ext = os.path.splitext(file.name)
        if ext.lower() == '.heic':
            file = heic_to_jpg_file(file)
            base_name, ext = os.path.splitext(file.name)
            # print(file.content_type)
            # print(file.size)
            # print(file.name)
            # ext = '.jpg'
            # original_filename = base_name + ext
        # if ext.lower() == '.doc' or ext.lower() == '.docx':
        #     file = bytes_word_to_pdf(file, ext.lower())
        #     base_name, ext = os.path.splitext(file.name)
        #     print(file.content_type)
        #     print(file.size)
        #     print(file.name)
        #     print(ext)
        # 生成唯一的对象名称
        # _, ext = os.path.splitext(file.name)
        hash = calculate_file_hash(file)
        if models.MedicalFile.objects.filter(delete_flag=0).filter(
                subject_medical_info__delete_flag=0,
                subject_medical_info__project_id=project_id,
                subject_medical_info__project_site_id=project_site_id,
                subject_medical_info__subject_id=subject_id,
                subject_medical_info__subject_item_id=subject_item_id,
                subject_medical_info__subject_visit_id=subject_visit_id,
                hash=hash
        ).exists():
            raise FileAlreadyExistsError("文件已存在")

        object_name = f"{uuid.uuid4().hex}{ext}"
        bucket_name = settings.MINIO_BUCKET_NAME

        # 保存文件
        try:
            minio_client = get_minio_client()
            # Ensure bucket exists
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)
            # Upload file to MinIO
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file,
                length=file.size,
                part_size=1024 * 1024 * 5,
                content_type=file.content_type
            )
        except Exception as e:
            logger.error(e)
            raise APIException(f"文件上传失败：{e}")

        # 数据入库
        with transaction.atomic():
            medical_file = {
                'original_filename': file.name,
                'bucket_name': bucket_name,
                'object_name': object_name,
                'content_type': file.content_type,
                'size': file.size,
                'hash': hash,
                'create_user': request.sys_user.username,
                'create_name': request.sys_user.realname,
            }
            medical_file = models.MedicalFile.objects.create(**medical_file)
            subject_visit = subject_item.subject_visit
            medical_info = models.MedicalInfoLite.objects.create(
                subject_epoch_id=subject_visit.subject_epoch.subject_epoch_id,
                subject_visit_id = subject_visit_id,
                subject_item_id=subject_item_id,
                project=project,
                project_site=project_site,
                subject=subject,
                patient=subject.patient,
                file=medical_file,
                create_user=request.sys_user.username,
                create_name=request.sys_user.realname,
            )

            serializer = serializers.MedicalInfoLiteSerializer(medical_info)
            # if not serializer.is_valid():
            #     raise ValidationError(serializer.errors)
        try:
            dag_conf = {'task_id': medical_info.file_id}
            if project_no:
                dag_conf['project_no'] = project_no
            trigger_dag('medical_file_mask', conf=dag_conf)
        except Exception as e:
            logger.error(e)
            medical_info.mask_status = 'ERROR'
            medical_info.save()

        test_result_ids = list(
            TestResult.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).values_list("id", flat=True)
        )
        TestResult.objects.filter(id__in=test_result_ids).update(delete_flag=1)
        OperationLog.objects.filter(
            target_id__in=test_result_ids
        ).update(delete_flag=1)
        OperationLog.objects.filter(
            target_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        TestOcrResult.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        # 软删除 AeTrackerTask 模型的相关对象
        AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        # SubjectMedicalInfo.objects.filter(
        #     subject_id=subject_id,
        #     subject_item_id=subject_item_id,
        #     delete_flag=0
        # ).update(file_masked_id=None, ocr_time=None)
        # 软删除 OperationLog 模型的相关对象1
        # a = SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).first()
        SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
            ae_ai_current_step=0, ae_ai_task_id=0)

        return Response(serializer.data)

    @extend_schema(
        summary='上传新脱敏文件并替换旧脱敏文件',
        tags=['病历归集'],
        request=serializers.MedicalInfoLiteMaskedUpdateRequestSerializer,
        responses=serializers.MedicalInfoLiteSerializer
    )
    @action(url_path='update-masked-file', detail=False, methods=['post'])
    def upload_new_masked_file(self, request, *args, **kwargs):
        # serializer = serializers.MedicalInfoLiteMaskedUpdateRequestSerializer(data=request.data)
        print(request.data)
        subject_id = request.data.get('subject_id')
        subject_item_id = request.POST.get('subject_item_id')
        project_id = request.data.get('project_id')
        file_masked_id = request.data.get('file_masked_id')
        project_site_id = request.data.get('project_site_id')
        subject_visit_id = request.data.get('subject_visit_id')

        instance = models.MedicalInfoLite.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            project_id=project_id,
            project_site_id=project_site_id,
            file_masked_id=file_masked_id,
            subject_visit_id=subject_visit_id,
            delete_flag=0
        ).first()
        # print(12345)
        if not instance:
            raise NotFound("未找到匹配的病历信息")
        # print("1234")
        # print(request.data)
        # print(5678)
        serializer = serializers.MedicalInfoLiteMaskedUpdateRequestSerializer(data=request.data)
        if not serializer.is_valid():
            raise ValidationError(serializer.errors)

        data = serializer.validated_data
        file = data.get('file')
        # project_id = data.get('project').get('project_id')
        # project_site_id = data.get('project_site').get('project_site_id')
        # subject_id = data.get('subject').get('subject_id')
        # category = data.get('category')
        # mask_instance =  models.MedicalFileMasked.objects.filter(id=file_masked_id).first()
        # 2. 构建 MinIO 存储路径
        base_name, ext = os.path.splitext(file.name)
        object_name = f"{uuid.uuid4().hex}{ext}"
        bucket_name = settings.MINIO_BUCKET_NAME

        try:
            minio_client = get_minio_client()
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)

            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file,
                length=file.size,
                part_size=1024 * 1024 * 5,
                content_type=file.content_type
            )
        except Exception as e:
            logger.error(e)
            raise APIException(f"文件上传失败：{e}")
        
        with transaction.atomic():
            # 1. 如果存在旧脱敏文件，则标记为删除
            if instance.file_masked:
                old_masked_file = instance.file_masked
                old_masked_file.delete_flag = 1
                # old_masked_file.update_user = request.sys_user.username
                old_masked_file.save()

            

            # 3. 创建新的脱敏文件记录
            new_masked_file = models.MedicalFileMasked.objects.create(
                original_filename=file.name,
                bucket_name=bucket_name,
                object_name=object_name,
                content_type=file.content_type,
                size=file.size,
                hash=calculate_file_hash(file),
                create_user=request.sys_user.username,
                create_name=request.sys_user.realname
            )

            # 4. 更新 MedicalInfoLite 的关联字段
            instance.file_masked = new_masked_file
            instance.subject_item_id = subject_item_id
            instance.update_user = request.sys_user.username
            instance.update_name = request.sys_user.realname
            instance.save(update_fields=[
                'file_masked',
                'subject_item_id',
                'update_user',
                'update_name'
            ])

        # 5. 返回更新后的数据
        medical_info_serializer = self.serializer_class(instance)
        return Response(medical_info_serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        summary='更新OCR坐标信息',
        tags=['病历归集'],
        request=serializers.MedicalInfoLiteOcrBoxUpdateRequestSerializer,
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'success': {'type': 'boolean', 'example': True},
                    'message': {'type': 'string', 'example': 'OCR坐标信息更新成功'}
                }
            }
        }
    )
    @action(url_path='update-ocr-box', detail=False, methods=['post'])
    def update_ocr_box(self, request, *args, **kwargs):
        """更新OCR坐标信息或OCR打码文本
        
        请求参数中的 ocr_box 应为页面数组格式：
        [
            {
                "page": 1,
                "words_block_list": [
                    {
                        "words": "识别的文本内容",
                        "location": [[x1, y1], [x2, y2], [x3, y3], [x4, y4]],
                        "confidence": 0.95
                    }
                ]
            }
        ]
        
        支持两种模式：
        1. 仅更新 ocr_text_mask：直接提供 ocr_text_mask 参数
        2. 更新 ocr_box 并自动生成 ocr_text_mask：提供 ocr_box 参数，系统会自动计算 ocr_text_mask
        """
        serializer = serializers.MedicalInfoLiteOcrBoxUpdateRequestSerializer(data=request.data)
        if not serializer.is_valid():
            raise ValidationError(serializer.errors)

        validated_data = serializer.validated_data
        medical_info_id = validated_data['medical_info_id']
        ocr_box = validated_data.get('ocr_box')
        ocr_text_mask = validated_data.get('ocr_text_mask')
        original_filename = validated_data.get('original_filename', '')

        try:
            medical_info = models.MedicalInfoLite.objects.get(id=medical_info_id, delete_flag=0)
        except models.MedicalInfoLite.DoesNotExist:
            raise NotFound("病历信息不存在")

        # 如果提供了 ocr_box，则自动生成 ocr_text_mask
        if ocr_box:
            # 获取原始文件名
            if not original_filename:
                original_filename = medical_info.file.original_filename if medical_info.file else '未知文件'
            
            logger.info(f"开始生成ocr_text_mask - medical_info_id: {medical_info_id}, original_filename: {original_filename}")
            logger.info(f"ocr_box类型: {type(ocr_box)}, 长度: {len(ocr_box) if isinstance(ocr_box, (list, str)) else 'N/A'}")
            
            generated_ocr_text_mask = generate_ocr_text_mask(ocr_box, original_filename)
            
            logger.info(f"生成的ocr_text_mask长度: {len(generated_ocr_text_mask) if generated_ocr_text_mask else 0}")
            if not generated_ocr_text_mask:
                logger.warning(f"ocr_text_mask为空 - medical_info_id: {medical_info_id}, ocr_box: {ocr_box[:500] if isinstance(ocr_box, str) else str(ocr_box)[:500]}")
            
            # 使用生成的 ocr_text_mask
            ocr_text_mask = generated_ocr_text_mask
        
        # 更新医疗信息
        update_fields = ['update_user', 'update_name']
        
        if ocr_box:
            medical_info.ocr_box = ocr_box
            update_fields.append('ocr_box')
        
        if ocr_text_mask:
            medical_info.ocr_text_mask = ocr_text_mask
            update_fields.append('ocr_text_mask')
        
        # 如果有 original_filename 参数，还需要更新相关文件的文件名
        if original_filename:
            with transaction.atomic():
                # 更新主文件名
                if medical_info.file:
                    medical_info.file.original_filename = original_filename
                    medical_info.file.update_user = request.sys_user.username
                    medical_info.file.update_name = request.sys_user.realname
                    medical_info.file.save()
                
                # 更新脱敏文件名
                if medical_info.file_masked:
                    medical_info.file_masked.original_filename = original_filename
                    medical_info.file_masked.update_user = request.sys_user.username
                    medical_info.file_masked.update_name = request.sys_user.realname
                    medical_info.file_masked.save()
        
        medical_info.update_user = request.sys_user.username
        medical_info.update_name = request.sys_user.realname
        medical_info.save(update_fields=update_fields)

        # 构建响应消息
        message_parts = []
        if ocr_box:
            message_parts.append("OCR坐标信息")
        if ocr_text_mask:
            message_parts.append("OCR打码文本")
        
        message = "、".join(message_parts) + "更新成功"

        return Response({
            'success': True,
            'message': message
        })

    @extend_schema(
        summary='重新OCR处理',
        tags=['病历归集'],
        parameters=[
            OpenApiParameter(name='id', description='病历信息ID', required=True, type=int)
        ],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'success': {'type': 'boolean'},
                    'message': {'type': 'string'},
                    'data': {
                        'type': 'object',
                        'properties': {
                            'id': {'type': 'integer'},
                            'file_id': {'type': 'integer'},
                            'mask_status': {'type': 'string'},
                            'trigger_time': {'type': 'string'},
                            'project_no': {'type': 'string'}
                        }
                    }
                }
            }
        }
    )
    @action(url_path='re-ocr', detail=False, methods=['get'])
    def re_ocr(self, request, *args, **kwargs):
        """重新OCR处理接口"""
        from django.utils import timezone
        from apps.subject_medical.models import SubjectMedicalInfo
        
        # 1. 参数验证
        medical_info_id = request.query_params.get('id')
        if not medical_info_id:
            return Response({
                "success": False, 
                "message": "缺少id参数"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            medical_info_id = int(medical_info_id)
        except ValueError:
            return Response({
                "success": False, 
                "message": "id参数必须是整数"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 2. 查找记录
        try:
            medical_info = SubjectMedicalInfo.objects.select_related('project').get(
                id=medical_info_id, 
                delete_flag=0
            )
        except SubjectMedicalInfo.DoesNotExist:
            return Response({
                "success": False, 
                "message": "记录不存在"
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 3. 权限检查 - 验证用户是否有权限访问该记录
        # 这里可以添加具体的权限检查逻辑，例如检查项目访问权限等
        # 暂时跳过具体权限检查，只检查基础认证
        
        # 4. 状态检查
        if medical_info.mask_status == 'IN_PROGRESS':
            return Response({
                "success": False, 
                "message": "OCR任务正在进行中，请勿重复提交",
                "error_code": "ocr_in_progress"
            }, status=status.HTTP_409_CONFLICT)
        
        # 5. 重置OCR相关字段
        medical_info.ocr_text = ''
        medical_info.ocr_text_mask = ''
        medical_info.file_masked_id = None
        medical_info.page_count = None
        medical_info.ocr_box = ''
        medical_info.preprocess_duration = None
        medical_info.preprocess_end_time = None
        medical_info.preprocess_start_time = None
        medical_info.save(update_fields=['ocr_text', 'ocr_text_mask', 'file_masked_id', 'page_count', 'ocr_box', 'preprocess_duration', 'preprocess_end_time', 'preprocess_start_time'])
        
        # 6. 构建 DAG 参数
        dag_conf = {
            'task_id': medical_info.file_id
        }
        
        # 7. 添加可选的 project_no
        if hasattr(medical_info, 'project') and medical_info.project:
            project_no = getattr(medical_info.project, 'project_no', None)
            if project_no:
                dag_conf['project_no'] = project_no
        
        # 8. 触发 DAG
        try:
            trigger_dag('medical_file_mask', conf=dag_conf)
            
            # 9. 返回成功响应
            return Response({
                "success": True,
                "message": "重新OCR任务已提交",
                "data": {
                    "id": medical_info.id,
                    "file_id": medical_info.file_id,
                    "mask_status": "IN_PROGRESS",
                    "trigger_time": timezone.now().isoformat(),
                    "project_no": dag_conf.get('project_no')
                }
            })
        except Exception as e:
            logger.error(f"触发DAG任务失败: {e}")
            return Response({
                "success": False,
                "message": f"触发DAG任务失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MedicalInfoLiteDetailViewSet(BaseViewSet, UpdateModelMixin, DestroyModelMixin):
    queryset = models.MedicalInfoLite.objects.filter(delete_flag=0)
    serializer_class = serializers.MedicalInfoLiteSerializer
    http_method_names = ['patch', 'delete']

    @extend_schema(
        summary='更新病历文件名',
        tags=['病历归集'],
        request=serializers.MedicalInfoLiteUpdateRequestSerializer,
        responses=serializers.MedicalInfoLiteSerializer
    )
    def partial_update(self, request, *args, **kwargs):
        data = request.data.copy()
        data['update_user'] = request.sys_user.username
        data['update_name'] = request.sys_user.realname

        medical_info = self.get_object()
        partial = kwargs.pop('partial', True)

        serializer = serializers.MedicalInfoLiteUpdateRequestSerializer(data=data, partial=partial)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        with transaction.atomic():
            serializer = serializers.MedicalInfoUpdateSerializer(medical_info, data=data, partial=partial)
            if serializer.is_valid():
                self.perform_update(serializer)

            file = medical_info.file
            serializer = serializers.MedicalFileUpdateSerializer(file, data=data, partial=partial)
            if serializer.is_valid():
                self.perform_update(serializer)

            file_masked = medical_info.file_masked
            if file_masked:
                serializer = serializers.MedicalFileMaskedUpdateSerializer(file_masked, data=data, partial=partial)
                if serializer.is_valid():
                    self.perform_update(serializer)

        serializer = self.serializer_class(medical_info)
        return Response(serializer.data)

    @extend_schema(
        summary='删除病历文件',
        tags=['病历归集'],
        request=serializers.MedicalFileUpdateSerializer,
        responses=serializers.MedicalInfoLiteSerializer
    )
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        subject_item_id = instance.subject_item_id
        subject_id = instance.subject_id
        subject_visit_id = instance.subject_visit_id

        task = AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[AeTrackerTask.IN_PROGRESS, AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        if task:
            raise TaskExistsStatusIN_PROGRESS()

        task1 = MedicalCollectionTask.objects.filter(
            subject_id=subject_id,
            subject_visit_id=subject_visit_id,
            status__in=[AeTrackerTask.IN_PROGRESS, AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        if task1:
            raise CrfWordTaskExistsStatusIN_PROGRESS()

        instance.delete_flag = 1
        instance.update_user = request.sys_user.username
        instance.update_name = request.sys_user.realname
        instance.save()

        instance.file.delete_flag = 1
        instance.file.update_user = request.sys_user.username
        instance.file.update_name = request.sys_user.realname
        instance.file.save()

        test_result_ids = list(
            TestResult.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).values_list("id", flat=True)
        )
        TestResult.objects.filter(id__in=test_result_ids).update(delete_flag=1)
        OperationLog.objects.filter(
            target_id__in=test_result_ids
        ).update(delete_flag=1)
        OperationLog.objects.filter(
            target_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        TestOcrResult.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        # 软删除 AeTrackerTask 模型的相关对象
        AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        # SubjectMedicalInfo.objects.filter(
        #     subject_id=subject_id,
        #     subject_item_id=subject_item_id,
        #     delete_flag=0
        # ).update(file_masked_id=None, ocr_time=None)
        # 软删除 OperationLog 模型的相关对象
        # a = SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).first()

        SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
            ae_ai_current_step=0, ae_ai_task_id=0)
        return Response(status=status.HTTP_204_NO_CONTENT)
