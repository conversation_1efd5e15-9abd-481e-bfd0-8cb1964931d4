from datetime import timedelta
from django.conf import settings
from rest_framework import serializers

from common.minio_client import get_minio_client
from common.models import BaseFileModel
from common.serializers import FileUrlMixin
from apps.subject_medical.serializers import SubjectMedicalInfoSerializer as MedicalInfoLiteSerializer
from . import models

class MedicalInfoLiteMaskedUpdateRequestSerializer(serializers.ModelSerializer):
    project_id = serializers.CharField(source='project.project_id', label="项目ID")
    project_site_id = serializers.CharField(source='project_site.project_site_id', label="项目中心ID")
    subject_id = serializers.Char<PERSON>ield(source='subject.subject_id', label="受试者ID")
    subject_epoch_id = serializers.CharField(source='subject_epoch.subject_epoch_id', label="受试者阶段ID", required=False) 
    subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID")
    subject_item_id = serializers.Char<PERSON><PERSON>(source='subject_item.subject_item_id', label="受试者操作项ID")
    file = serializers.FileField(required=True)  # 文件上传字段
    file_masked_id = serializers.CharField(required=True, label="脱敏文件ID") 

    class Meta:
        model = models.MedicalInfoLite
        fields = [
            'project_id', 
            'project_site_id', 
            'subject_id', 
            'subject_epoch_id',
            'subject_visit_id',  # 加入新字段
            'subject_item_id', 
            'file',
            'file_masked_id'
        ]

class MedicalInfoFileSerializer(FileUrlMixin, serializers.ModelSerializer):
    url = serializers.SerializerMethodField(label='文件下载url')

    class Meta:
        model = models.MedicalFile
        exclude = ['create_user', 'create_name', 'update_user', 'update_name']


# class MedicalInfoLiteSerializer(serializers.ModelSerializer):
#     project_id = serializers.CharField(source='project.project_id', label="项目ID")
#     project_site_id = serializers.CharField(source='project_site.project_site_id', label="项目中心ID")
#     subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
#     subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID")
#     # ocr_text = serializers.CharField(label="OCR文本")
#     file = MedicalInfoFileSerializer()
#     file_masked = MedicalInfoFileSerializer()
#
#     class Meta:
#         model = models.MedicalInfoLite
#         exclude = ['project', 'project_site', 'subject', 'patient',
#                    'create_user', 'create_name', 'update_user', 'update_name']


class MedicalInfoLiteCreateRequestSerializer(serializers.ModelSerializer):
    project_id = serializers.CharField(source='project.project_id', label="项目ID")
    project_site_id = serializers.CharField(source='project_site.project_site_id', label="项目中心ID")
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    file = serializers.FileField(required=True)  # 文件上传字段
    # 新增字段
    project_no = serializers.CharField(required=False, label="项目编号")

    subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID")
    subject_item_id = serializers.CharField(source='subject_item.subject_item_id', label="受试者操作项ID")
    


    class Meta:
        model = models.MedicalInfoLite
        fields = ['subject_visit_id','subject_item_id','project_id', 'project_site_id', 'subject_id', 'project_no', 'file']


class MedicalInfoLiteUpdateRequestSerializer(serializers.ModelSerializer):
    original_filename = serializers.CharField(label="原始文件名")

    class Meta:
        model = models.MedicalInfoLite
        fields = ['ocr_text_mask', 'original_filename']


class MedicalInfoUpdateSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.MedicalInfoLite
        fields = ['ocr_text_mask']


class MedicalInfoLiteOcrBoxUpdateRequestSerializer(serializers.ModelSerializer):
    """更新OCR坐标信息的请求序列化器"""
    medical_info_id = serializers.IntegerField(label="病历信息ID")
    ocr_box = serializers.JSONField(label="OCR带坐标识别结果", required=False, allow_null=True)
    ocr_text_mask = serializers.CharField(label="OCR打码文本", required=False, allow_null=True, allow_blank=True)
    original_filename = serializers.CharField(label="原始文件名", help_text="用于生成ocr_text_mask的文件名", required=False, allow_blank=True)

    class Meta:
        model = models.MedicalInfoLite
        fields = ['medical_info_id', 'ocr_box', 'ocr_text_mask', 'original_filename']

    def validate(self, attrs):
        """
        验证逻辑：ocr_box 和 ocr_text_mask 至少要提供一个
        """
        ocr_box = attrs.get('ocr_box')
        ocr_text_mask = attrs.get('ocr_text_mask')
        
        if not ocr_box and not ocr_text_mask:
            raise serializers.ValidationError("ocr_box 和 ocr_text_mask 至少要提供一个")
        
        return attrs


class MedicalFileUpdateSerializer(FileUrlMixin, serializers.ModelSerializer):

    class Meta:
        model = models.MedicalFile
        fields = ['original_filename']


class MedicalFileMaskedUpdateSerializer(FileUrlMixin, serializers.ModelSerializer):

    class Meta:
        model = models.MedicalFileMasked
        fields = ['original_filename']


class MedicalInfoLiteReOcrRequestSerializer(serializers.Serializer):
    """重新OCR请求序列化器"""
    id = serializers.IntegerField(label="病历信息ID", min_value=1)

    def validate_id(self, value):
        """验证ID是否存在且有效"""
        try:
            # 延迟导入避免循环依赖
            from apps.subject_medical.models import SubjectMedicalInfo
            instance = SubjectMedicalInfo.objects.get(id=value, delete_flag=0)
            return value
        except SubjectMedicalInfo.DoesNotExist:
            raise serializers.ValidationError("指定的病历信息不存在")
