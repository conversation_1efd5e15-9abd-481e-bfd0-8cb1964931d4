import os

import django
from django.db import transaction

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

from common.tools import sql_to_list
from apps.hospital.models import Hospital
from apps.doc_autofill.models import HospDocTmplCenter
"""
同步erp医院信息
erp smo_hosptial_info --> ai hospital_info
code --> hosp_id 'h'+code
code --> hosp_code
sitenamecn --> hosp_name
slid --> site_leader_id
sl --> site_leader_name
create_time --> create_time
update_time --> update_time
create_by --> create_user smo99
create_by --> create_name !smo99
update_by --> update_user smo99
update_by --> update_name !smo99
          --> doc_tmpl_center_id 触发新业务表
"""

PAGE_SIZE = 1000  # 每次查询的记录数，可以根据实际情况调整
WORK_NO_PREFIX  = 'smo99'
# 迁移医院信息
def migrate_hospitals():
    # 获取erp smo_hosptial_info 信息
    page = 0
    next_flag = True
    # 循环遍历数据记录，直至无数据
    while next_flag:
        offset = page * PAGE_SIZE
        # 查询有效hospital_info数据（code为null忽略）
        sql = f"""
                SELECT code,sitenamecn,slid,sl,create_time,update_time,create_by,update_by 
                FROM smo_hosptial_info 
                WHERE code is not null 
                LIMIT {offset},{PAGE_SIZE}
                """
        print(f'offset:{offset},pagesize:{PAGE_SIZE}')
        item_list = sql_to_list('master', sql )
        for item in item_list:
            try:
                update_or_insert_hospital_info(item)
            except Exception as e:
                print(e)
                continue
        if item_list is None or item_list.__len__() == 0:
            next_flag = False
        page+=1

# 更新或插入医院信息
def update_or_insert_hospital_info(item):
    if not item:
        return
    # 约定处理创建人、更新人
    create_by = item['create_by']
    update_by = item['update_by']
    create_user = create_by if (create_by and create_by.find(WORK_NO_PREFIX) != -1) else ''
    create_name = create_by if (create_by and create_by.find(WORK_NO_PREFIX) == -1) else ''
    update_user = update_by if (update_by and update_by.find(WORK_NO_PREFIX) != -1) else ''
    update_name = update_by if (update_by and update_by.find(WORK_NO_PREFIX) == -1) else ''
    # 同步数据，已有数据更新，否则插入新数据
    hospital = Hospital.objects.filter(hosp_code=item['code']).first()
    with transaction.atomic():
        if hospital:
            print("======update======" + hospital.hosp_code)
            hospital.hosp_code = item['code']
            hospital.hosp_name = item['sitenamecn']
            hospital.site_leader_id = item['slid']
            hospital.site_leader_name = item['sl']
            hospital.create_time = item['create_time']
            hospital.update_time = item['update_time']
            hospital.create_user = create_user
            hospital.create_name = create_name
            hospital.update_user = update_user
            hospital.update_name = update_name
            hospital.save()
        else:
            print("======insert======" + item['code'])
            # 初始化中心模板
            hosp_doc_tmpl_center = {
                'status_code': HospDocTmplCenter.StatusCodeEnum.UNRELEASED.value,
                'total_amount': 0,
                'project_used_amount': 0,
                'create_user': 'admin',
                'create_name': 'admin',
            }
            hosp_doc_tmpl_center = HospDocTmplCenter.objects.create(**hosp_doc_tmpl_center)
            new_hospital = {
                'hosp_id': 'h' + item['code'],
                'hosp_code': item['code'],
                'hosp_name': item['sitenamecn'],
                'site_leader_id': item['slid'],
                'site_leader_name': item['sl'],
                'create_time': item['create_time'],
                'update_time': item['update_time'],
                'create_user': create_user,
                'create_name': create_name,
                'update_user': update_user,
                'update_name': update_name,
                'doc_tmpl_center': hosp_doc_tmpl_center
            }
            Hospital.objects.create(**new_hospital)

if __name__ == "__main__":
    # python -m script.sync_erp_hospital
    migrate_hospitals()
