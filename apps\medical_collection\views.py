import os
import logging
import uuid
from datetime import timedelta
from io import Bytes<PERSON>
from openpyxl import Workbook
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.utils import get_column_letter
import json
import hashlib
from minio import Minio
from copy import deepcopy
import pandas as pd
from django.http import FileResponse
from django.conf import settings
from django.shortcuts import render
from django.db import transaction
from django.shortcuts import get_list_or_404
from django.db.models import Subquery, OuterRef
from rest_framework.viewsets import GenericViewSet
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.fields import Char<PERSON>ield
from rest_framework import filters, viewsets
from rest_framework.mixins import ListModelMixin, CreateModelMixin, DestroyModelMixin
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.filters import OrderingFilter
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.renderers import Base<PERSON>enderer
from rest_framework.exceptions import ValidationError, NotFound, APIException
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import APIException

from django_filters.rest_framework import DjangoFilterBackend

from rest_pandas.views import PandasViewBase
from rest_pandas import PandasExcelRenderer
from openpyxl.styles import Font, Border
from openpyxl.styles import Alignment
from openpyxl import load_workbook

from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema, OpenApiParameter

from common.auth import ERPSysJWTAuthentication
from common.utils import calculate_file_hash
from common.minio_client import get_minio_client
from common.pagination import StandardResultsSetPagination
from apps.project.models import ProjectMaterialInfo
from apps.project.serializers import ProjectMaterialCreateRequestSerializer, ProjectMaterialLatestRequestSerializer
from apps.patient.models import Patient
from apps.subject.models import Subject
from apps.project.models import Project, ProjectSite
from apps.project.models import ProjectMaterialInfo, ProjectMaterialFile
from apps.medical.models import MedicalInfoLite
from apps.subject_medical.models import SubjectMedicalInfo
from apps.project.serializers import ProjectMaterialFileSerializer
from common.minio_client import get_minio_client
from . import serializers
from . import models
from . import filters
from common.tools import trigger_dag
from common.utils import get_project_prompt_info
from django.db import connection
from datetime import datetime


class FileAlreadyExistsError(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "文件已存在！！！"
    default_code = "error"


class CRFNotExistsError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "CRF表头暂未上传,无法生成！！！"
    default_code = "error"

class MedicalRecordNotExistsError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "Word病例暂未上传,无法生成！！！"
    default_code = "error"


class MedicalInfoNotExistsError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "请先上传文件信息,暂无法生成！！！"
    default_code = "error"


class PatientNotExistsError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "未找到受试者,无法生成！！！"
    default_code = "error"


class ProjectNotExistsError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "未找到项目,无法生成！！！"
    default_code = "error"


class Project_SiteNotExistsError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "未找到项目中心,无法生成！！！"
    default_code = "error"


class TaskExistsStatusTODO(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "先前任务，待办中！！！"
    default_code = "error"


class TaskExistsStatusIN_PROGRESS(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "先前任务,正在处理中！！！"
    default_code = "error"


class TaskExistsStatusCOMPLETED(APIException):
    status_code = status.HTTP_200_OK
    default_detail = "已生成任务,任务已完成！！！"
    default_code = "success"


class TaskExistsStatusCANCELLED(APIException):
    status_code = status.HTTP_200_OK
    default_detail = "已生成任务,任务取消！！！"
    default_code = "success"


class TaskNotExistsError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "未找到处理完的病例！！！"
    default_code = "error"


class TaskFileNotExistsError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "未找到处理完的病例！！！"
    default_code = "error"


class OcrTaskExistsStatusIN_PROGRESS(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "当前访视OCR结果提取还未完全结束！！！"
    default_code = "error"


logger = logging.getLogger('app')

# 定义对齐方式：默认中心对齐和左对齐
center_alignment = Alignment(horizontal="center", vertical="center")
left_alignment = Alignment(horizontal="left", vertical="center")
wrap_left_alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)

# 字体设置
bold_font = Font(bold=True)
gray_font = Font(color="808080")  # 50%灰色

def set_dimensions(sheet):
    # 统一设置所有工作表的列宽和行高
    # 设置所有列宽为30
    max_col = sheet.max_column
    for col in range(1, max_col + 1):
        col_letter = get_column_letter(col)
        sheet.column_dimensions[col_letter].width = 30
    # 设置所有行高为20，Sheet1后续再对特定行修改
    for row in range(1, sheet.max_row + 1):
        sheet.row_dimensions[row].height = 20

def write_crf_data_to_sheet(sheet: Worksheet, data: list) -> None:

    current_col = 1  # 当前起始列

    # 遍历每个顶层记录，写入 Sheet1
    for record in data:
        fields = record["fields"]
        num_fields = len(fields)
        start_col = current_col
        end_col = current_col + num_fields - 1

        # 第一行：合并单元格写入 title，居中且加粗
        sheet.merge_cells(start_row=1, start_column=start_col, end_row=1, end_column=end_col)
        title_cell = sheet.cell(row=1, column=start_col, value=record["title"])
        title_cell.alignment = center_alignment
        title_cell.font = bold_font



        # 遍历每个字段
        for i, field in enumerate(fields):
            col = current_col + i
            # 第2行：key，加粗，居中
            cell_key = sheet.cell(row=2, column=col, value=field.get("key", ""))
            cell_key.alignment = center_alignment
            cell_key.font = bold_font

            # 第4行：value_a，居中；根据 check 值设置背景色
            cell_value_a = sheet.cell(row=3, column=col, value=field.get("value", ""))
            cell_value_a.alignment = center_alignment


        current_col = end_col + 1

    set_dimensions(sheet)


    # 首行行高 30
    sheet.row_dimensions[1].height = 30

def save_crf_result_to_minio(result_dict, request, latest_task):
    def convert_data(raw_data):
        cleaned_data = []
        for item in raw_data:
            title = item.get("title")
            fields = item.get("fields", [])

            cleaned_fields = [
                {
                    "key": field.get("key"),
                    "value": field.get("value")
                }
                for field in fields
            ]

            cleaned_data.append({
                "title": title,
                "fields": cleaned_fields
            })

        return cleaned_data
    result_dict = convert_data(result_dict)
    wb = Workbook()
    sheet = wb.active
    sheet.title = "CRF"
    write_crf_data_to_sheet(sheet, result_dict)
    # # 转换为 DataFrame
    # columns = []
    # values = []
    # for group in result_dict:
    #     title = group["title"]
    #     for field in group["fields"]:
    #         columns.append(title)
    #         values.extend([field["key"], field["value"]])
    # row1 = values[::2]
    # row2 = values[1::2]
    # df = pd.DataFrame([row1, row2], columns=columns)

    # 保存为 Excel 流
    output = BytesIO()
    wb.save(output)
    output.seek(0)

    # 生成文件信息
    subject_info = Subject.objects.filter(subject_id=request.data.get('subject_id'),delete_flag=0).first()

    # return subject_info.real_name, subject_info.code
    original_filename = latest_task.project.project_no + '-' + subject_info.code + '-' + "CRF" + '.xlsx'
    object_name = f"{uuid.uuid4().hex}.xlsx"
    bucket_name = settings.MINIO_BUCKET_NAME
    hash_value = hashlib.sha256(output.getvalue()).hexdigest()

    # 上传到 MinIO
    client = get_minio_client()
    if not client.bucket_exists(bucket_name):
        client.make_bucket(bucket_name)
    client.put_object(
        bucket_name=bucket_name,
        object_name=object_name,
        data=output,
        length=-1,         # 文件大小（-1 表示自动计算）
        part_size=10*1024*1024  # 分块上传大小（10MB）
    )

    # 保存到数据库（可选）
    material_file = {
        'task_id': latest_task.id,
        'original_filename': original_filename,
        'bucket_name': bucket_name,
        'version': f'v{datetime.now().strftime("%Y%m%d%H%M%S")}'+'manual_edit',
        'object_name': object_name,
        'content_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'size': output.getbuffer().nbytes,
        'hash': hash_value,
        'update_user': request.sys_user.username,
        'update_name': request.sys_user.realname,
    }

    task_info = models.MedicalCollectionFile.objects.create(**material_file)
    return task_info


class BaseAPIView(APIView):
    authentication_classes = [ERPSysJWTAuthentication]
    permission_classes = [IsAuthenticated]


class BaseListViewSet(BaseAPIView, GenericViewSet, ListModelMixin):
    filter_backends = (DjangoFilterBackend, OrderingFilter)


class MedicalCollectionTasksView(BaseAPIView, GenericViewSet, CreateModelMixin):
    # parser_classes = (MultiPartParser, )  # 支持文件上传
    queryset = models.MedicalCollectionTask.objects.filter(delete_flag=0)
    serializer_class = serializers.MedicalCollectionTaskSerializer
    filterset_class = filters.ProjectMaterialInfoFilter

    # def get_serializer_class(self):
    #     if self.action == 'list':
    #         return serializers.ProjectMaterialInfoSerializer
    #     elif self.action == 'create':
    #         return serializers.ProjectMaterialFileSerializer
    #     return super().get_serializer_class()



    # @extend_schema(
    #     summary='获取最新的项目素材信息',
    #     tags=['项目'],
    #     parameters=[ProjectMaterialLatestRequestSerializer],
    #     responses=serializers.ProjectMaterialInfoSerializer
    # )
    # @action(detail=False, methods=['get'])
    # def latest(self, request, project_id, format=None):
    #     category = request.query_params.get('category')
    #     queryset = self.queryset
    #     if category:
    #         queryset = queryset.filter(project_id=project_id, category=category)
    #     latest_info = queryset.order_by('-create_time').first()
    #     if not latest_info:
    #         raise NotFound("还没有上传素材文件")
    #     serializer = self.get_serializer(latest_info)
    #     return Response(serializer.data)

    @extend_schema(
        summary='创建CRF、Word病历生成任务',
        tags=['病历归集'],
        request=serializers.MedicalCollectionTaskCreatRequestSerializer,
        responses=serializers.MedicalCollectionTaskSerializer
    )
    def create(self, request, format=None):
        # file = request.FILES.get('file')
        project_id = request.data.get('project_id')
        project_site_id = request.data.get('project_site_id')
        subject_id = request.data.get('subject_id')
        subject_visit_id = request.data.get('subject_visit_id')
        ocr_task = SubjectMedicalInfo.objects.filter(
            subject_id=subject_id,
            subject_visit_id=subject_visit_id,
            ocr_status__in=[models.MedicalCollectionTask.IN_PROGRESS, models.MedicalCollectionTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        if ocr_task:
            raise OcrTaskExistsStatusIN_PROGRESS()

        category = request.data.get('category')
        def get_epoch_id_by_subject_visit(subject_visit_id):
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT t2.label
                    FROM subject_visit_info AS t1
                    JOIN subject_epoch_info AS t2 ON t1.subject_epoch_id = t2.subject_epoch_id
                    WHERE t1.subject_visit_id = %s AND t2.delete_flag = 0;
                """, [subject_visit_id])
                
                result = cursor.fetchone()

            return result[0] if result else None
        epoch_name = get_epoch_id_by_subject_visit(subject_visit_id)
        # print("category:", category)
        if category == models.MedicalCollectionTask.CRF:
            t_c = ProjectMaterialInfo.CRF_TEMPLATE
        if category == models.MedicalCollectionTask.MEDICAL_RECORD:
            t_c = ProjectMaterialInfo.MEDICAL_RECORD_TEMPLATE



        subject = Subject.objects.filter(subject_id=subject_id, delete_flag=0).first()
        if not subject:
            raise PatientNotExistsError()

        project = Project.objects.filter(project_id=project_id, delete_flag=0).first()
        if not project:
            raise ProjectNotExistsError()

        project_site = ProjectSite.objects.filter(project_site_id=project_site_id, delete_flag=0).first()
        if not project_site:
            raise Project_SiteNotExistsError()

        # if not ProjectMaterialInfo.objects.filter(
        #         project_id=project_id, category=t_c, delete_flag=0
        # ).exists():
        #     raise CRFNotExistsError()

        if category == models.MedicalCollectionTask.MEDICAL_RECORD:
            if not ProjectMaterialFile.objects.filter(
                    material_info__project_id=project_id, material_info__category=t_c, delete_flag=0, material_info__epoch_name=epoch_name
            ).exists():
                raise MedicalRecordNotExistsError()

        if category == models.MedicalCollectionTask.CRF:

            project_promopt_info = get_project_prompt_info(project_id, epoch_name)
            if project_promopt_info is None:
                raise CRFNotExistsError()

        if not MedicalInfoLite.objects.filter(
                subject_visit_id = subject_visit_id,subject_id=subject_id, delete_flag=0
        ).exists():
            raise MedicalInfoNotExistsError()

        # if not Subject.objects.filter(
        #         subject_id=subject_id
        # ).exists():
        #     raise PatientNotExistsError()

        task = models.MedicalCollectionTask.objects.filter(
                subject_visit_id=subject_visit_id,subject_id=subject_id, category=category, delete_flag=0
            ).order_by('-create_time').first()
        if task:
            if task.status == models.MedicalCollectionTask.TODO:
                raise TaskExistsStatusTODO()
            if task.status == models.MedicalCollectionTask.IN_PROGRESS:
                raise TaskExistsStatusIN_PROGRESS()
            # if task.status == models.MedicalCollectionTask.COMPLETED:
            #     raise TaskExistsStatusCOMPLETED()
            if task.status == models.MedicalCollectionTask.CANCELLED:
                raise TaskExistsStatusCANCELLED()

        # 数据入库
        with transaction.atomic():

            task_info = {
                'name': str(category) + '-' + str(subject_id) + 'task',
                'category': category,
                'patient': subject.patient,
                'subject': subject,
                'project': project,
                'project_site': project_site,
                'create_user': request.sys_user.username,
                'create_name': request.sys_user.realname,
                'subject_visit_id': subject_visit_id
            }
            task_info = models.MedicalCollectionTask.objects.create(**task_info)

            serializer = serializers.MedicalCollectionTaskSerializer(task_info)
            # if not serializer.is_valid():
            #     raise APIException(serializer.errors)

        if category == models.MedicalCollectionTask.CRF:
            try:
                trigger_dag('crf_generation', conf={'task_id': task_info.id})
            except Exception as e:
                logger.error(e)
                task_info.status = 'ERROR'
                task_info.save()
        if category == models.MedicalCollectionTask.MEDICAL_RECORD:
            try:
                trigger_dag('word_generation', conf={'task_id': task_info.id})
            except Exception as e:
                logger.error(e)
                task_info.status = 'ERROR'
                task_info.save()
        return Response(serializer.data)

    @extend_schema(
        summary='查询病历归集特定任务状态',
        tags=['病历归集'],
        # parameters=[serializers.AeTrackerTaskListRequestSerializer],
        parameters=[serializers.MedicalCollectionTaskCreatRequestSerializer],
        responses=serializers.MedicalCollectionTaskSerializer
    )
    @action(url_path='get-specific-tasks', detail=False, methods=['get'])
    def get_specific_tasks(self, request, format=None):
        subject_id = request.query_params.get('subject_id')
        subject_visit_id = request.query_params.get('subject_visit_id')
        category = request.query_params.get('category')
        print(subject_id, subject_visit_id)
        queryset = self.queryset.filter(
            subject_id=subject_id,
            subject_visit_id=subject_visit_id,
            category=category,
            # status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        serializer = serializers.MedicalCollectionTaskSerializer(queryset)
        print(serializer.data)
        return Response(serializer.data)


class MedicalCollectionTasksLatestMedicalRecordView(BaseListViewSet):
    # parser_classes = (MultiPartParser, )  # 支持文件上传
    queryset = models.MedicalCollectionFile.objects.filter(delete_flag=0)
    serializer_class = serializers.MedicalCollectionFileSerializer
    @extend_schema(
        summary='获取最新的CRF、Word病历文件',
        tags=['病历归集'],
        parameters=[serializers.MedicalCollectionGetFileRequestSerializer],
        responses=ProjectMaterialFileSerializer
    )
    def list(self, request, format=None):
        subject_id = request.GET.get('subject_id')
        category = request.GET.get('category')
        subject_visit_id = request.GET.get('subject_visit_id')
        MedicalCollectionTask_tasks = models.MedicalCollectionTask.objects.filter(subject_id=subject_id,subject_visit_id=subject_visit_id, category=category, status=models.MedicalCollectionTask.COMPLETED)
        if not MedicalCollectionTask_tasks.exists():
            raise TaskNotExistsError()
        MedicalCollectionTask_ids = [task.id for task in MedicalCollectionTask_tasks]
        MedicalCollectionFiles = models.MedicalCollectionFile.objects.filter(
            task_id__in=MedicalCollectionTask_ids,
            delete_flag=0
        )
        if not MedicalCollectionFiles.exists():
            raise TaskFileNotExistsError()
        if category == models.MedicalCollectionTask.MEDICAL_RECORD:
            latest_files = MedicalCollectionFiles.order_by('-create_time')
            model_name_dict = {}
            for file in latest_files:
                if file.model_name not in model_name_dict:
                    model_name_dict[file.model_name] = file
                    if len(model_name_dict) == 2:
                        break
            latest_two_unique = list(model_name_dict.values())
            latest_two_unique.sort(key=lambda x: x.create_time, reverse=True)
            serializer = serializers.MedicalCollectionTasksLatestResponseSerializer(latest_two_unique,
                                                                                    many=True)
        if category == models.MedicalCollectionTask.CRF:
            latest_medical_collection_file = MedicalCollectionFiles.order_by('-create_time').first()
            serializer = serializers.MedicalCollectionTasksLatestResponseSerializer(latest_medical_collection_file)
        return Response(serializer.data)


class MedicalCollectionTasksLatestMedicalRecordJsonView(BaseListViewSet):
    queryset = models.MedicalCollectionFile.objects.filter(delete_flag=0)
    @extend_schema(
        summary='获取最新CRF JSON',
        tags=['病历归集'],
        parameters=[serializers.MedicalCollectionGetFileRequestSerializer],
        responses=ProjectMaterialFileSerializer
    )
    def list(self, request, format=None):
        category = request.query_params.get('category')
        subject_id = request.query_params.get('subject_id')
        subject_visit_id = request.query_params.get('subject_visit_id')
        MedicalCollectionTask_tasks = models.MedicalCollectionTask.objects.filter(subject_id=subject_id,
                                                                                  category=category,
                                                                                  subject_visit_id= subject_visit_id,
                                                                                  status=models.MedicalCollectionTask.COMPLETED)
        if not MedicalCollectionTask_tasks.exists():
            raise TaskNotExistsError()
        MedicalCollectionTask_ids = [task.id for task in MedicalCollectionTask_tasks]
        MedicalCollectionFiles = models.MedicalCollectionFile.objects.filter(
            task_id__in=MedicalCollectionTask_ids,
            delete_flag=0
        )
        if not MedicalCollectionFiles.exists():
            raise TaskFileNotExistsError()
        latest_medical_collection_file = MedicalCollectionFiles.order_by('-create_time').first()
        # serializer = ProjectMaterialFileSerializer(latest_medical_collection_file)
        # print(serializer.data)

        minio_client = get_minio_client()

        import io
        import pandas as pd
        from django.http import JsonResponse

        # 存储桶名称和对象名称
        bucket_name = latest_medical_collection_file.bucket_name
        object_name = latest_medical_collection_file.object_name

        # 从 MinIO 中获取对象
        response = minio_client.get_object(bucket_name, object_name)

        # 读取对象内容到内存中
        data = response.read()

        # 关闭响应
        response.close()
        response.release_conn()

        # 使用 Pandas 读取 Excel 数据
        df = pd.read_excel(io.BytesIO(data))

        columns = df.columns.tolist()

        # 遍历表头，从第二个元素开始
        for i in range(1, len(columns)):
            if 'Unnamed' in columns[i]:
                columns[i] = columns[i - 1]

        # 更新表头
        df.columns = columns
        column_names = df.columns
        """
        result = {}

        # 遍历 DataFrame 的列
        df = df.fillna("")
        for col_index, col in enumerate(df.columns):
            if col not in result:
                result[col] = []
            # 获取该列第一行和第二行的值
            key = str(df.iloc[0, col_index])
            value = str(df.iloc[1, col_index])
            # 组成键值对并添加到结果字典中对应列名的列表里
            result[col].append({key: value})
        """
        temp_result = {}
        df = df.fillna("")
        def format_number(x):
            try:
                num = float(x)
                if num.is_integer():
                    return str(int(num))
                else:
                    return str(num)
            except ValueError:
                # 如果不是数值类型，直接转为字符串
                return str(x)
        # 对整个DataFrame应用format_number函数
        df = df.applymap(format_number)
        for col_index, col in enumerate(df.columns):
            key = str(df.iloc[0, col_index])
            if len(key) < 1:
                key = " "
            try:
                value = str(df.iloc[2, col_index])
            except:
                value = ""
            field = {
                "key": key,
                "value": value
            }
            if col in temp_result:
                temp_result[col]['fields'].append(field)
            else:
                temp_result[col] = {
                    "title": col,
                    "fields": [field]
                }
        result = list(temp_result.values())

        # print(result)
        # df.to_excel('20250225.xlsx')
        # # 将 DataFrame 转换为字典以便返回 JSON 响应
        # result = df.to_dict(orient='records')
        # print(result)
        return JsonResponse(result, safe=False)
    @extend_schema(
        summary='获取最新CRF 文本',
        tags=['病历归集'],
        parameters=[
            serializers.MedicalCrfResultTextSerializer
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "subject_visit_id": {"type": "string"},
                    "subject_id": {"type": "string"},
                    "project_id": {"type": "string"},
                    "project_site_id": {"type": "string"},
                    "result_text": {"type": "string"}
                }
            },
            400: {"description": "缺少必要参数"},
            404: {"description": "未找到匹配的 CRF 结果"}
        }
    )
    @action(url_path='latest_crf_text',detail=False, methods=['get'])
    def latest_crf_text(self, request, *args, **kwargs):

        subject_visit_id = request.query_params.get('subject_visit_id')
        subject_id = request.query_params.get('subject_id')
        project_id = request.query_params.get('project_id')
        project_site_id = request.query_params.get('project_site_id')

        if not all([subject_visit_id, subject_id, project_id, project_site_id]):
            return Response({"error": "缺少参数"}, status=400)

        try:
            result = models.MedicalCollectionCrfResult.objects.filter(
                delete_flag = 0,
                subject_visit_id=subject_visit_id,
                subject_id=subject_id,
                project_id=project_id,
                project_site_id=project_site_id
            ).order_by('-id').first()
            if result:
                return Response({
                    "subject_visit_id": subject_visit_id,
                    "subject_id": subject_id,
                    "project_id": project_id,
                    "project_site_id": project_site_id,
                    "result_text": result.result_text
                })
            else:
                return Response({
                    "subject_visit_id": subject_visit_id,
                    "subject_id": subject_id,
                    "project_id": project_id,
                    "project_site_id": project_site_id,
                    "result_text":  None
                })
        except models.MedicalCollectionCrfResult.DoesNotExist:
            raise NotFound("未找到匹配的 CRF 结果")
    @extend_schema(
        summary='保存 CRF 修改记录到数据库',
        tags=['病历归集'],
        parameters= [serializers.MedicalCollectionCrfLogCreateSerializer],
        responses={200: {"description": "保存成功的响应"}}
    )
    @action(url_path='save_modified_history',detail=False, methods=['post'])
    def save_modified_history(self, request, *args, **kwargs):
        """
        接收 subject_id, subject_visit_id, project_id, project_site_id 和 modified_history，
        并将其保存到数据库中。
        """
        subject_visit_id = request.data.get('subject_visit_id')
        subject_id = request.data.get('subject_id')
        project_id = request.data.get('project_id')
        project_site_id = request.data.get('project_site_id')
        create_user = request.data.get('create_user')
        modified_history = request.data.get('modified_history')

        # 处理 modified_history 可能为字符串的情况
        try:
            if isinstance(modified_history, str):
                try:
                    modified_history = json.loads(modified_history.strip("'"))
                except:
                    # print("modified_history：",repr(modified_history))
                    import traceback
                    traceback.print_exc()
        except Exception as e:
            return Response({"error": "modified_history 解析失败", "detail": str(e)}, status=400)

        # 确保所有必填字段存在
        required_fields = ['subject_id', 'subject_visit_id', 'project_id', 'project_site_id']
        if not all([request.data.get(f) for f in required_fields]):
            return Response({"error": "缺少必要参数"}, status=400)

        success_list = []
        collection_crf_result = models.MedicalCollectionCrfResult.objects.filter(subject_id=subject_id, project_id=project_id,subject_visit_id=subject_visit_id,project_site_id=project_site_id,delete_flag=0).order_by('-id').first()
        try:
            if isinstance(collection_crf_result.result_text, str):
                result_dict = json.loads(collection_crf_result.result_text)
        except Exception as e:
            print(collection_crf_result)
            return Response({"error": "result_text 解析失败", "detail": str(e)}, status=400)
        def mark_field_for_modification(data, target_title, target_key,value):
            """
            遍历数据，找到指定 title 和 key 的字段，并设置 'modify': True。

            :param data: 输入的 JSON 数据（列表）
            :param target_title: 要匹配的模块标题（str）
            :param target_key: 要匹配的字段 key（str）
            :return: 修改后的 data（原数据会被修改，同时返回）
            """
            before_value = ''
            for module in data:
                if module.get("title") == target_title:
                    for field in module.get("fields", []):
                        if field.get("key") == target_key:
                            field['check'] = False
                            field["modify"] = True
                            before_value = deepcopy(field["value"])
                            field["value"] = value

            return data,before_value
        if len(modified_history) != 0:
            for modified_dict in modified_history:
                title = modified_dict.get("title")
                item_key = modified_dict.get("key")
                content_text = modified_dict.get("modified_result")
                result_dict,before_value = mark_field_for_modification(result_dict, title, item_key,content_text)
                content_text = f"修改原值{before_value}为新值{content_text}"
                res_dict = {
                    "medical_collection_crf_result_id":collection_crf_result.id,
                    "title": title,
                    "item_key": item_key,
                    "content_text": content_text,
                    "create_user": create_user,
                    "create_name": request.sys_user.realname
                }

                # 创建记录
                models.MedicalCollectionCrfLog.objects.create(**res_dict)
                success_list.append(res_dict)
            latest_task = models.MedicalCollectionTask.objects.filter(
                subject_id=subject_id,
                category="CRF",
                subject_visit_id = subject_visit_id,
                delete_flag=0
            ).order_by('-create_time').first()


            save_crf_result_to_minio(result_dict, request, latest_task)
            collection_crf_result.result_text = json.dumps(result_dict, ensure_ascii=False)
            collection_crf_result.save()
            return Response({
                "message": "修改记录已成功保存",
                "data": success_list
            }, status=200)
        else:
            return Response({
                "message": "记录未修改",
            }, status=200)

    @extend_schema(
    summary='查询特定CRF字段的修改历史记录',
    tags=['病历归集'],
    parameters=[serializers.MedicalCollectionCrfLogQuerySerializer],
    responses={200: {"type": "array", "items": {"type": "object"}}}
)
    @action(url_path='crf_modification_history', detail=False, methods=['get'])
    def crf_modification_history(self, request, *args, **kwargs):
        """
        根据 project_id, project_site_id, subject_id, subject_visit_id, title, key 查询 CRF 修改记录
        """
        serializer = serializers.MedicalCollectionCrfLogQuerySerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        validated_data = serializer.validated_data

        project_id = validated_data.get('project_id')
        project_site_id = validated_data.get('project_site_id')
        subject_id = validated_data.get('subject_id')
        subject_visit_id = validated_data.get('subject_visit_id')
        title = validated_data.get('title')
        item_key = validated_data.get('key')


        # collection_crf_result = models.MedicalCollectionCrfResult.objects.filter(subject_id=subject_id, subject_visit_id=subject_visit_id,project_id=project_id,project_site_id=project_site_id,delete_flag=0).first()
        # 查询对应的 MedicalCollectionCrfResult
        crf_result = models.MedicalCollectionCrfResult.objects.filter(
            # imedical_collection_crf_result_id = collection_crf_result.id
            delete_flag=0,
            subject_id=subject_id,
            subject_visit_id=subject_visit_id,
            project_id=project_id,
            project_site_id=project_site_id,
        ).order_by('-id').first()

        if not crf_result:
            raise NotFound("未找到匹配的 CRF 结果")

        # 查询该字段的所有修改记录
        logs = models.MedicalCollectionCrfLog.objects.filter(
            medical_collection_crf_result_id=crf_result.id,
            title=title,
            item_key=item_key,
            delete_flag=0
        ).order_by('-create_time')

        if not logs.exists():
            return Response([], status=status.HTTP_200_OK)

        # 序列化返回结果
        log_serializer = serializers.MedicalCollectionCrfLogResponseSerializer(logs, many=True)
        return Response(log_serializer.data, status=status.HTTP_200_OK)



class MedicalCollectionTasksLatestMedicalRecordChangeJsonView(BaseAPIView, GenericViewSet, CreateModelMixin):
    # parser_classes = (MultiPartParser, )  # 支持文件上传
    queryset = models.MedicalCollectionTask.objects.filter(delete_flag=0)
    serializer_class = serializers.MedicalCollectionTaskSerializer
    # filterset_class = filters.ProjectMaterialInfoFilter

    @extend_schema(
        summary='编辑CRF',
        tags=['病历归集'],
       
        request={
            "application/json": {
                "type": "object",
                "properties": {
                    "subject_id": {
                        "type": "string",
                        "title": "受试者ID"
                    },
                    "subject_visit_id": {"type": "string", "title": "受试者访视ID"},
                    "category": {
                        "type": "string",
                        "title": "类型"
                    },
                    "json_data": {
                        "type": "array",
                        "items": {
                            "type": "object"
                        },
                        "title": "CRF Json"
                    }
                },
                "required": ["subject_visit_id","subject_id", "category", "json_data"]
            }
        },
        responses=serializers.MedicalCollectionFileSerializer
    )
    def create(self, request, format=None):
        # file = request.FILES.get('file')
        category = request.data.get('category')
        if category == models.MedicalCollectionTask.CRF:
            t_c = ProjectMaterialInfo.CRF_TEMPLATE
        if category == models.MedicalCollectionTask.MEDICAL_RECORD:
            t_c = ProjectMaterialInfo.MEDICAL_RECORD_TEMPLATE

        json_data = request.data.get('json_data')
        subject_id = request.data.get('subject_id')
        subject_visit_id = request.data.get('subject_visit_id')

        MedicalCollectionTask_tasks = models.MedicalCollectionTask.objects.filter(subject_id=subject_id,
                                                                                  category=category,
                                                                                  subject_visit_id=subject_visit_id,
                                                                                  status=models.MedicalCollectionTask.COMPLETED)
        if not MedicalCollectionTask_tasks.exists():
            raise TaskNotExistsError()
        MedicalCollectionTask_ids = [task.id for task in MedicalCollectionTask_tasks]
        MedicalCollectionFiles = models.MedicalCollectionFile.objects.filter(
            task_id__in=MedicalCollectionTask_ids,
            delete_flag=0
        )
        if not MedicalCollectionFiles.exists():
            raise TaskFileNotExistsError()
        latest_medical_collection_file = MedicalCollectionFiles.order_by('-create_time').first()

        filename = latest_medical_collection_file.original_filename
        task_id = latest_medical_collection_file.task_id
        # 生成唯一的对象名称
        _, ext = os.path.splitext(filename)
        object_name = f"{uuid.uuid4().hex}{ext}"
        bucket_name = settings.MINIO_BUCKET_NAME

        client = get_minio_client()
        # 1. 从 MinIO 获取文件流
        response = client.get_object(bucket_name, latest_medical_collection_file.object_name)
        file_stream = BytesIO(response.read())  # 读取为内存流
        response.close()
        response.release_conn()

        # 2. 用 openpyxl 加载 Excel
        wb = load_workbook(filename=file_stream)
        ws = wb.worksheets[0]  # 获取第一个 sheet
        f_column = 1
        
        for g_seq, group in enumerate(json_data):
            for f_seq, field in enumerate(group['fields']):
                ws.cell(row=4, column=f_column).value = field.get('value', '')
                f_column += 1
        
        output = BytesIO()
        wb.save(output)

        # data = json_data
        # """
        # # 初始化列名列表和值列表
        # columns = []
        # values = []
        # # 遍历 JSON 数据
        # for key, sub_list in data.items():
        #     for sub_dict in sub_list:
        #         for sub_key, sub_value in sub_dict.items():
        #             columns.append(key)
        #             values.extend([sub_key, sub_value])
        # # 分割值列表为两行
        # row1 = values[::2]
        # row2 = values[1::2]
        # # 创建 DataFrame
        # """
        # columns = []
        # values = []

        # # 遍历 JSON 数据
        # for item in data:
        #     title = item["title"]
        #     for field in item["fields"]:
        #         key = field["key"]
        #         value = field["value"]
        #         columns.append(title)
        #         values.extend([key, value])

        # # 分割值列表为两行
        # row1 = values[::2]
        # row2 = values[1::2]

        # df = pd.DataFrame([row1, row2], columns=columns)

        # # 假设df是存储病例数据的DataFrame

        # output = io.BytesIO()
        # # 将 DataFrame 保存到二进制流中
        # df.to_excel(output, index=False)
        # output.seek(0)
        # # 加载内存中的 Excel 文件
        # wb = load_workbook(output)
        # ws = wb.active
        # # 开始合并单元格操作
        # start_col = 1
        # current_col_name = ws.cell(row=1, column=start_col).value
        # for col in range(2, len(columns) + 1):
        #     col_name = ws.cell(row=1, column=col).value
        #     if col_name == current_col_name:
        #         continue
        #     else:
        #         if col - start_col > 1:
        #             start_letter = get_column_letter(start_col)
        #             end_letter = get_column_letter(col - 1)
        #             ws.merge_cells(f'{start_letter}1:{end_letter}1')
        #         start_col = col
        #         current_col_name = col_name

        # # 处理最后一组相同列名的情况
        # if len(columns) - start_col + 1 > 1:
        #     start_letter = get_column_letter(start_col)
        #     end_letter = get_column_letter(len(columns))
        #     ws.merge_cells(f'{start_letter}1:{end_letter}1')

        # output.seek(0)
        # output.truncate()
        # wb.save(output)
        output.seek(0)

        class FileStreamWithName:
            def __init__(self, stream, filename):
                self.stream = stream
                self.filename = filename
                self.size = len(stream.getvalue())
                self.content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

            @property
            def name(self):
                return self.filename

        # 创建包含文件流和文件名的对象
        file = FileStreamWithName(output, filename)

        # 保存文件
        try:
            minio_client = get_minio_client()
            # Ensure bucket exists
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)
            # Upload file to MinIO
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=output,  # 直接使用 output 作为 data 参数
                length=file.size,
                part_size=1024 * 1024 * 5,
                content_type=file.content_type
            )
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(e)
            raise APIException(f"文件上传失败：{e}")

        # 数据入库
        with transaction.atomic():
            # 计算 hash
            hash_object = hashlib.sha256(output.getvalue())
            hash = hash_object.hexdigest()
            now = datetime.now()
            formatted_time = now.strftime("%Y%m%d%H%M%S")
            material_file = {
                'task_id': task_id,
                'original_filename': file.name,
                'bucket_name': bucket_name,
                'version': 'v' + str(formatted_time),
                'object_name': object_name,
                'content_type': file.content_type,
                'size': file.size,
                'hash': hash,
                # 'create_user': request.sys_user.username,
                # 'create_name': request.sys_user.realname,
                'update_user': request.sys_user.username,
                'update_name': request.sys_user.realname,
            }


        task_info = models.MedicalCollectionFile.objects.create(**material_file)

        serializer = serializers.MedicalCollectionFileSerializer(task_info)

        return Response(serializer.data)



class MedicalCollectionFileHistoryView(BaseListViewSet):
    # parser_classes = (MultiPartParser, )  # 支持文件上传
    queryset = models.MedicalCollectionFile.objects.filter(delete_flag=0)
    serializer_class = serializers.MedicalCollectionFileHistoryResponseSerializer
    filterset_class = filters.MedicalCollectionFileHistoryFilter
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        return self.queryset.select_related('task').filter(task__status=models.MedicalCollectionTask.COMPLETED)

    @extend_schema(
        summary='历史生成文件',
        tags=['病历归集'],
        # parameters=[serializers.MedicalCollectionGetFileRequestSerializer],
        responses=ProjectMaterialFileSerializer
    )
    def list(self, request, format=None):
        return super().list(request, format)




class MedicalCollectionFileDeleteView(BaseListViewSet, DestroyModelMixin):
    # parser_classes = (MultiPartParser, )  # 支持文件上传
    queryset = models.MedicalCollectionFile.objects.filter(delete_flag=0)
    serializer_class = serializers.MedicalCollectionFileHistoryResponseSerializer
    http_method_names = ['delete']
    # def get_queryset(self):
    #     return self.queryset.select_related('task').filter(task__status=models.MedicalCollectionTask.COMPLETED)

    # @extend_schema(
    #     summary='删除历史生成文件',
    #     tags=['病历归集'],
    #     # parameters=[serializers.MedicalCollectionGetFileRequestSerializer],
    #     responses=ProjectMaterialFileSerializer
    # )
    @extend_schema(
        summary='删除历史生成文件',
        tags=['病历归集'],
        # request=serializers.ProjectMaterialFileUpdateSerializer,
        responses=ProjectMaterialFileSerializer
    )
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete_flag = 1
        instance.update_user = request.sys_user.username
        instance.update_name = request.sys_user.realname
        instance.save()
        # 如果该文件关联了 CRF 任务，则同时软删除相关的所有 CRF 结果和日志
        if instance.task.category == models.MedicalCollectionTask.CRF:
            # 删除该任务下的所有 MedicalCollectionCrfResult 记录
            crf_results = models.MedicalCollectionCrfResult.objects.filter(
                medical_collection_task_id=instance.task.id,
                delete_flag=0
            )

            for crf_result in crf_results:
                # 软删除 CRF 结果
                crf_result.delete_flag = 1
                crf_result.update_user = request.sys_user.username
                crf_result.update_name = request.sys_user.realname
                crf_result.save()

                # 软删除相关的 MedicalCollectionCrfLog 记录
                crf_logs = models.MedicalCollectionCrfLog.objects.filter(
                    medical_collection_crf_result_id=crf_result.id,
                    delete_flag=0
                )

                # 逐个更新记录，因为模型字段与其它模型不一致
                for log in crf_logs:
                    log.delete_flag = 1
                    # MedicalCollectionCrfLog 没有 update_user/update_name 字段，只能使用 create_* 字段
                    log.create_user = request.sys_user.username
                    log.create_name = request.sys_user.realname
                    log.save()

        return Response(status=status.HTTP_204_NO_CONTENT)



class MedicalCollectionManualUploadView(BaseAPIView, GenericViewSet):
    parser_classes = [MultiPartParser, FormParser]  # 支持文件上传
    queryset = models.MedicalCollectionFile.objects.filter(delete_flag=0)
    serializer_class = serializers.MedicalCollectionFileSerializer

    @extend_schema(
        summary='人工上传 Medical Record Word 文件',
        tags=['病历归集'],
        request={
            "multipart/form-data": {
                "type": "object",
                "properties": {
                    "subject_id": {"type": "string", "description": "受试者 ID"},
                    "subject_visit_id": {"type": "string", "description": "受试者访视 ID"},
                    "file": {"type": "string", "format": "binary", "description": "Word 文件 (.docx/.doc)"},
                },
                "required": ["subject_id", "file"]
            },
        },
        responses={200: {"description": "文件上传成功"}}
    )
    @action(detail=False, methods=["post"], parser_classes=[MultiPartParser])
    def manual_upload_medical_record(self, request, *args, **kwargs):
        subject_id = request.data.get("subject_id")
        uploaded_file = request.FILES.get("file")
        subject_visit_id = request.data.get("subject_visit_id")

        # 参数校验
        if not subject_id:
            return Response({"error": "缺少 subject_id"}, status=status.HTTP_400_BAD_REQUEST)
        if not uploaded_file:
            return Response({"error": "未上传文件"}, status=status.HTTP_400_BAD_REQUEST)

        # 获取 Subject 对象
        subject = Subject.objects.filter(subject_id=subject_id).first()
        patient= Patient.objects.filter(subject_id=subject_id).first()
        if not subject:
            raise PatientNotExistsError()

        # 获取项目相关信息
        project = subject.project
        project_site = subject.project_site
        patient_id = patient.patient_id

        # 构造文件名和对象名
        original_filename = uploaded_file.name
        ext = os.path.splitext(original_filename)[1].lower()
        if ext not in [".doc", ".docx"]:
            return Response({"error": "仅支持 .doc 或 .docx 文件"}, status=status.HTTP_400_BAD_REQUEST)

        object_name = f"{uuid.uuid4().hex}{ext}"
        bucket_name = settings.MINIO_BUCKET_NAME

        # 上传至 MinIO
        try:
            minio_client = get_minio_client()
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)

            file_stream = BytesIO(uploaded_file.read())
            file_size = uploaded_file.size
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file_stream,
                length=file_size,
                part_size=1024 * 1024 * 5,
                content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            )
        except Exception as e:
            logger.error(e)
            raise APIException(f"文件上传失败：{e}")
        latest_task = models.MedicalCollectionTask.objects.filter(
            subject_id=subject_id,
            category="MEDICAL_RECORD",
            subject_visit_id = subject_visit_id,
            delete_flag=0
        ).order_by('-create_time').first()
        # 数据入库

        def calculate_file_hash(file_obj, chunk_size=65536):
            # 确保指针在开头
            file_obj.seek(0)

            sha256 = hashlib.sha256()
            while True:
                chunk = file_obj.read(chunk_size)
                if not chunk:
                    break
                if isinstance(chunk, str):
                    chunk = chunk.encode('utf-8')  # 如果是字符串，转成 bytes
                sha256.update(chunk)

            return sha256.hexdigest()

        with transaction.atomic():
            hash_value = calculate_file_hash(file_stream)  # 假设你已有该函数
            now = datetime.now()
            formatted_time = now.strftime("%Y%m%d%H%M%S")
            if latest_task:
                material_file = {
                    'task_id': latest_task.id,  # 可选，若没有关联任务可留空
                    'original_filename': original_filename,
                    'bucket_name': bucket_name,
                    'version': f'v{formatted_time}Person',
                    'object_name': object_name,
                    'content_type': "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    'size': file_size,
                    'hash': hash_value,
                    'update_user': request.sys_user.username,
                    'update_name': request.sys_user.realname,
                    "model_name":"DeepSeek"
                }
            else:
                task_file = {
                    "subject_id": subject_id,
                    "subject_visit_id": subject_visit_id,
                    "project_id": project.project_id,
                    "project_site_id": project_site.project_site_id,
                    "status":"COMPLETED",
                    "patient_id": patient.patient_id,
                    # "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "category": "MEDICAL_RECORD",
                    'create_user': request.sys_user.username,
                    'create_name': request.sys_user.realname,

                }
                models.MedicalCollectionTask.objects.create(**task_file)
                latest_task = models.MedicalCollectionTask.objects.filter(
                subject_id=subject_id,
                category="MEDICAL_RECORD",
                subject_visit_id = subject_visit_id,
                delete_flag=0
                    ).order_by('-create_time').first()
                material_file = {
                    'task_id': latest_task.id,  # 可选，若没有关联任务可留空
                    'original_filename': original_filename,
                    'bucket_name': bucket_name,
                    'version': f'v{formatted_time}Person',
                    'object_name': object_name,
                    'content_type': "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    'size': file_size,
                    'hash': hash_value,
                    'update_user': request.sys_user.username,
                    'update_name': request.sys_user.realname,
                    "model_name":"DeepSeek"
                }

            task_info = models.MedicalCollectionFile.objects.create(**material_file)

        serializer = self.serializer_class(task_info)
        return Response(serializer.data, status=status.HTTP_200_OK)



class MedicalCollectionFileDeletesView(GenericViewSet,BaseAPIView):
    queryset = models.MedicalCollectionFile.objects.filter(delete_flag=0)
    serializer_class = serializers.MedicalCollectionFileHistoryResponseSerializer
    @extend_schema(
        summary='批量删除历史生成文件',
        tags=['病历归集'],
        request={
            'application/json': {
                'type': 'object',
                'properties': {
                    'ids': {
                        'type': 'array',
                        'items': {'type': 'string'},
                        'description': '需要删除的文件 ID 列表'
                    }
                },
                'required': ['ids']
            }
        },
        responses={200: None}
    )
    @action(detail=False, methods=["post"])
    def delete_files(self, request, *args, **kwargs):
        # 获取前端传入的 ID 列表
        ids = request.data.get('ids', [])
        print(ids)
        if not ids:
            return Response({"error": "缺少需要删除的文件ID列表"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            ids = [int(x) for x in ids]
        except ValueError:
            return Response({"error": "文件ID必须为整数"}, status=status.HTTP_400_BAD_REQUEST)

        # 查询所有符合条件的对象（用于验证是否存在）
        instances = get_list_or_404(self.queryset, id__in=ids)

        # 批量更新 delete_flag 和审计字段
        self.queryset.filter(id__in=ids).update(
            delete_flag=1,
            update_user=request.sys_user.username,
            update_name=request.sys_user.realname
        )

           # 查找这些文件中属于 CRF 类型的，并处理相关的 CRF 记录
        crf_files = models.MedicalCollectionFile.objects.filter(
            delete_flag=1,
            id__in=ids,
            task__category=models.MedicalCollectionTask.CRF
        )
        
        if crf_files.exists():
            # 获取关联的 task IDs
            task_ids = list(crf_files.values_list('task_id', flat=True).distinct())

            # 对每个 task_id，删除所有相关的 MedicalCollectionCrfResult 和 MedicalCollectionCrfLog 记录
            for task_id in task_ids:
                # 删除该任务下的所有 MedicalCollectionCrfResult 记录
                crf_results = models.MedicalCollectionCrfResult.objects.filter(
                    medical_collection_task_id=task_id,
                    delete_flag=0
                )

                for crf_result in crf_results:
                    # 软删除 CRF 结果
                    crf_result.delete_flag = 1
                    crf_result.update_user = request.sys_user.username
                    crf_result.update_name = request.sys_user.realname
                    crf_result.save()

                    # 软删除相关的 MedicalCollectionCrfLog 记录
                    crf_logs = models.MedicalCollectionCrfLog.objects.filter(
                        medical_collection_crf_result_id=crf_result.id,
                        delete_flag=0
                    )

                    # 逐个更新记录，因为模型字段与其它模型不一致
                    for log in crf_logs:
                        log.delete_flag = 1
                        # MedicalCollectionCrfLog 没有 update_user/update_name 字段，只能使用 create_* 字段
                        log.create_user = request.sys_user.username
                        log.create_name = request.sys_user.realname
                        log.save()

        return Response(status=status.HTTP_200_OK)







