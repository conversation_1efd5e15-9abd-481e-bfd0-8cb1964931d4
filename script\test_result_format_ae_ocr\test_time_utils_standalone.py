#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的时间处理工具测试
不依赖Django环境，直接测试时间修复逻辑
"""

import re
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TimeUtils:
    """时间格式处理工具类 - 增强版本"""

    @staticmethod
    def attempt_time_fix(time_str):
        """
        修复无空格的时间格式 YYYY-MM-DDHH:MM -> YYYY-MM-DD HH:MM，支持单位数时间组件
        增强版本：添加Django DateTimeField兼容性验证，无法修复时返回None
        """
        import re

        if not time_str or not time_str.strip():
            return None

        time_str = time_str.strip()

        # 首先检查是否已经是有效的Django时间格式
        if TimeUtils.is_valid_django_datetime(time_str):
            return time_str

        # 尝试修复各种常见的时间格式问题
        fixed_time = None

        # 修复1: 无空格格式 YYYY-MM-DD[H]H:MM:SS -> YYYY-MM-DD HH:MM:SS (支持单位数时间组件)
        no_space_pattern_with_seconds = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2}:\d{1,2})$'
        match = re.match(no_space_pattern_with_seconds, time_str)
        if match:
            date_part, time_part = match.groups()
            # 标准化时间部分，确保都是两位数
            time_components = time_part.split(':')
            normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
            fixed_time = f"{date_part} {normalized_time}"
        
        # 修复2: 无空格格式 YYYY-MM-DD[H]H:MM -> YYYY-MM-DD HH:MM (支持单位数时间组件)
        if not fixed_time:
            no_space_pattern = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2})$'
            match = re.match(no_space_pattern, time_str)
            if match:
                date_part, time_part = match.groups()
                # 标准化时间部分，确保都是两位数
                time_components = time_part.split(':')
                normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
                fixed_time = f"{date_part} {normalized_time}"

        # 修复3: 处理其他常见格式问题（可以根据需要扩展）
        if not fixed_time:
            # 尝试处理中文字符或其他分隔符
            cleaned = re.sub(r'[年月日时分秒]', '', time_str)
            cleaned = re.sub(r'[：]', ':', cleaned)
            cleaned = re.sub(r'\s+', ' ', cleaned)
            if cleaned != time_str:
                # 递归调用自己处理清理后的字符串
                return TimeUtils.attempt_time_fix(cleaned)

        # 验证修复结果
        if fixed_time and TimeUtils.is_valid_django_datetime(fixed_time):
            logger.debug(f"时间修复成功: '{time_str}' -> '{fixed_time}'")
            return fixed_time
        
        # 无法修复，记录警告并返回None
        logger.warning(f"无法修复时间格式: '{time_str}'，将设置为None")
        return None

    @staticmethod
    def is_valid_django_datetime(time_str):
        """验证时间字符串是否符合Django DateTimeField要求"""
        if not time_str or not isinstance(time_str, str):
            return False
        
        try:
            # 使用标准库的datetime来验证，模拟Django的行为
            from datetime import datetime
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M',
                '%Y-%m-%d',
            ]
            
            for fmt in formats:
                try:
                    datetime.strptime(time_str, fmt)
                    return True
                except ValueError:
                    continue
            return False
        except Exception:
            return False

    @staticmethod
    def validate_and_fix_datetime(time_str, field_name="time"):
        """
        验证并修复时间字符串，确保符合Django DateTimeField要求
        
        Args:
            time_str: 待验证的时间字符串
            field_name: 字段名称，用于日志记录
            
        Returns:
            str or None: 有效的时间字符串或None
        """
        if not time_str or not str(time_str).strip():
            return None
            
        time_str = str(time_str).strip()
        
        # 首先检查是否已经是有效格式
        if TimeUtils.is_valid_django_datetime(time_str):
            return time_str
        
        # 尝试修复
        fixed_time = TimeUtils.attempt_time_fix(time_str)
        
        if fixed_time and TimeUtils.is_valid_django_datetime(fixed_time):
            logger.info(f"{field_name} 时间格式修复成功: '{time_str}' -> '{fixed_time}'")
            return fixed_time
        
        # 修复失败，记录警告并返回None
        logger.warning(f"{field_name} 时间格式无效，已设置为None: '{time_str}'")
        return None

def test_enhanced_time_handling():
    """测试增强的时间处理功能"""
    print("=" * 80)
    print("增强时间处理功能测试")
    print("=" * 80)
    
    # 测试用例：模拟真实的错误时间格式
    test_cases = [
        # 原始错误案例
        ("2024-06-278:00:15", "原始错误案例：单位数小时"),
        ("2024-06-279:30:45", "单位数小时+分钟秒"),
        ("2024-06-2710:5:30", "单位数分钟"),
        ("2024-06-2710:30:5", "单位数秒"),
        
        # 其他常见错误格式
        ("2024-06-2708:00:15", "无空格标准格式"),
        ("2024-06-279:5:5", "全部单位数"),
        ("2024-06-279:30", "无秒格式"),
        
        # 正确格式
        ("2024-06-27 08:00:15", "已有空格的正确格式"),
        ("2024-06-27 10:30", "正确的无秒格式"),
        
        # 无效格式
        ("invalid-time", "完全无效的格式"),
        ("2024-13-45 25:70:80", "无效的日期时间"),
        ("", "空字符串"),
        (None, "None值"),
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    print("🔧 测试 validate_and_fix_datetime 方法:")
    print("-" * 60)
    
    for i, (test_input, description) in enumerate(test_cases, 1):
        result = TimeUtils.validate_and_fix_datetime(test_input, f"test_case_{i}")
        
        # 判断结果是否符合预期
        if test_input in [None, "", "invalid-time", "2024-13-45 25:70:80"]:
            # 对于无效输入，期望返回None
            is_success = result is None
            expected = "None"
        else:
            # 对于有效输入，期望返回有效的时间字符串
            is_success = result is not None and TimeUtils.is_valid_django_datetime(result)
            expected = "有效时间格式"
        
        status = "✅ 成功" if is_success else "❌ 失败"
        if is_success:
            success_count += 1
        
        print(f"{i:2d}. {description}")
        print(f"    输入: {test_input}")
        print(f"    输出: {result}")
        print(f"    期望: {expected}")
        print(f"    状态: {status}")
        print()
    
    # 输出测试结果
    success_rate = (success_count / total_count) * 100
    print("=" * 80)
    print(f"测试结果: {success_count}/{total_count} 成功 ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 测试通过！时间处理功能工作正常")
        print("💡 现在可以安全处理各种时间格式错误，避免数据库插入失败")
        return True
    else:
        print("⚠️  测试未完全通过，需要进一步优化")
        return False

def test_database_safety():
    """测试数据库安全性：确保所有输出都是安全的"""
    print("\n" + "=" * 80)
    print("数据库安全性测试")
    print("=" * 80)
    
    # 模拟可能导致数据库错误的时间格式
    dangerous_inputs = [
        "2024-06-278:00:15",  # 原始错误
        "2024-06-279:5:5",    # 多个单位数
        "invalid-format",      # 完全无效
        "2024-99-99 99:99:99", # 超出范围
        "",                    # 空字符串
        None,                  # None值
    ]
    
    print("🛡️  测试危险输入的安全处理:")
    print("-" * 60)
    
    all_safe = True
    for i, dangerous_input in enumerate(dangerous_inputs, 1):
        result = TimeUtils.validate_and_fix_datetime(dangerous_input, f"dangerous_{i}")
        
        # 检查结果是否安全（要么是有效格式，要么是None）
        is_safe = result is None or TimeUtils.is_valid_django_datetime(result)
        
        if not is_safe:
            all_safe = False
        
        safety_status = "🛡️ 安全" if is_safe else "⚠️ 危险"
        
        print(f"{i}. 输入: {dangerous_input}")
        print(f"   输出: {result}")
        print(f"   状态: {safety_status}")
        print()
    
    if all_safe:
        print("✅ 所有危险输入都得到了安全处理！")
        print("💡 不会再有时间格式错误导致数据库插入失败")
        return True
    else:
        print("❌ 发现不安全的输出，可能导致数据库错误")
        return False

def main():
    """主测试函数"""
    print("🚀 开始独立时间处理功能测试\n")
    
    # 运行功能测试
    function_success = test_enhanced_time_handling()
    
    # 运行安全性测试
    safety_success = test_database_safety()
    
    # 总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    if function_success and safety_success:
        print("🎉 所有测试通过！时间处理功能增强成功")
        print("✅ 功能测试: 通过")
        print("✅ 安全性测试: 通过")
        print("\n💡 关键改进:")
        print("  - 支持单位数时间组件的修复")
        print("  - 无法修复的格式返回None而不是错误字符串")
        print("  - 所有输出都经过Django DateTimeField兼容性验证")
        print("  - 可以避免因时间格式错误导致的数据库插入失败")
        return True
    else:
        print("⚠️  部分测试失败:")
        print(f"  功能测试: {'✅ 通过' if function_success else '❌ 失败'}")
        print(f"  安全性测试: {'✅ 通过' if safety_success else '❌ 失败'}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
