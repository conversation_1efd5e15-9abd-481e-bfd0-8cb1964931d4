# Generated by Django 4.1.5 on 2025-04-22 17:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("hospital", "0001_initial"),
        ("project", "0002_alter_project_update_time_and_more"),
        ("doc_autofill", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="projectsitedocinfo",
            name="hospital",
            field=models.ForeignKey(
                db_column="hosp_id",
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_infos",
                to="hospital.hospital",
                to_field="hosp_id",
                verbose_name="医院ID",
            ),
        ),
        migrations.AddField(
            model_name="projectsitedocinfo",
            name="project",
            field=models.ForeignKey(
                db_column="project_id",
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_infos",
                to="project.project",
                to_field="project_id",
                verbose_name="项目ID",
            ),
        ),
        migrations.AddField(
            model_name="projectsitedocinfo",
            name="project_site",
            field=models.ForeignKey(
                db_column="project_site_id",
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_infos",
                to="project.projectsite",
                to_field="project_site_id",
                verbose_name="项目中心ID",
            ),
        ),
        migrations.AddField(
            model_name="projectsitedoccenter",
            name="hospital",
            field=models.ForeignKey(
                db_column="hosp_id",
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_centers",
                to="hospital.hospital",
                to_field="hosp_id",
                verbose_name="医院ID",
            ),
        ),
        migrations.AddField(
            model_name="projectsitedoccenter",
            name="project",
            field=models.ForeignKey(
                db_column="project_id",
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_centers",
                to="project.project",
                to_field="project_id",
                verbose_name="项目ID",
            ),
        ),
        migrations.AddField(
            model_name="projectsitedoccenter",
            name="project_site",
            field=models.ForeignKey(
                db_column="project_site_id",
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_centers",
                to="project.projectsite",
                to_field="project_site_id",
                verbose_name="项目中心ID",
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplinfo",
            name="hospital",
            field=models.ForeignKey(
                db_column="hosp_id",
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_tmpl_infos",
                to="hospital.hospital",
                to_field="hosp_id",
                verbose_name="医院ID",
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplinfo",
            name="tmpl_file",
            field=models.OneToOneField(
                db_column="tmpl_file_id",
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_tmpl_info",
                to="doc_autofill.hospdoctmplfile",
                verbose_name="中心模板文件ID",
            ),
        ),
        migrations.AddField(
            model_name="hospdoctmplinfo",
            name="tmpl_file_processed",
            field=models.OneToOneField(
                db_column="tmpl_file_processed_id",
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_tmpl_info",
                to="doc_autofill.hospdoctmplfileprocessed",
                verbose_name="中心模板文件ID",
            ),
        ),
        migrations.AddField(
            model_name="docautofilltask",
            name="hospital",
            field=models.ForeignKey(
                db_column="hosp_id",
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_autofill_tasks",
                to="hospital.hospital",
                to_field="hosp_id",
                verbose_name="医院ID",
            ),
        ),
        migrations.AddField(
            model_name="docautofilltask",
            name="project",
            field=models.ForeignKey(
                db_column="project_id",
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_autofill_tasks",
                to="project.project",
                to_field="project_id",
                verbose_name="项目ID",
            ),
        ),
        migrations.AddField(
            model_name="docautofilltask",
            name="project_site",
            field=models.ForeignKey(
                db_column="project_site_id",
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_autofill_tasks",
                to="project.projectsite",
                to_field="project_site_id",
                verbose_name="项目中心ID",
            ),
        ),
    ]
