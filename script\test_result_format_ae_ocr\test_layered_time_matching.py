#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分层匹配策略的时间修复功能
验证每种时间格式错误都有对应的处理方案
"""

import sys
import os
import logging

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_layered_time_matching():
    """测试分层匹配策略"""
    print("=" * 80)
    print("分层匹配策略时间修复测试")
    print("=" * 80)
    
    # 按照优先级分类的测试用例
    test_categories = [
        {
            "category": "日期末位与时间首位连接",
            "cases": [
                ("2024-06-278:00:15", "2024-06-27 08:00:15", "带秒格式"),
                ("2024-06-279:30", "2024-06-27 09:30:00", "无秒格式"),
                ("2024-12-318:45:30", "2024-12-31 08:45:30", "年末日期"),
                ("2024-01-019:15", "2024-01-01 09:15:00", "年初日期"),
            ]
        },
        {
            "category": "完整日期与时间直接连接",
            "cases": [
                ("2024-07-0213:42:15", "2024-07-02 13:42:15", "带秒格式"),
                ("2024-07-0213:42", "2024-07-02 13:42:00", "无秒格式"),
                ("2024-07-029:30", "2024-07-02 09:30:00", "单位数小时"),
                ("2024-12-3123:59", "2024-12-31 23:59:00", "年末时间"),
            ]
        },
        {
            "category": "通用无空格格式",
            "cases": [
                ("2024-06-2710:05:30", "2024-06-27 10:05:30", "标准格式"),
                ("2024-06-279:5:5", "2024-06-27 09:05:05", "全单位数"),
                ("2024-06-2710:05", "2024-06-27 10:05:00", "无秒格式"),
            ]
        },
        {
            "category": "中文时间格式",
            "cases": [
                ("2024年06月27日08时00分15秒", "2024-06-27 08:00:15", "完整中文格式"),
                ("2024年6月27日8时0分", "2024-06-27 08:00:00", "单位数中文格式"),
                ("2024年12月31日23时59分59秒", "2024-12-31 23:59:59", "年末中文格式"),
            ]
        },
        {
            "category": "中文冒号格式",
            "cases": [
                ("2024-07-02：13：42", "2024-07-02 13:42:00", "中文冒号"),
                ("2024-07-02：9：30", "2024-07-02 09:30:00", "中文冒号+单位数"),
            ]
        },
        {
            "category": "已正确格式",
            "cases": [
                ("2024-07-02 13:42:15", "2024-07-02 13:42:15", "标准格式"),
                ("2024-07-02 09:30", "2024-07-02 09:30:00", "无秒格式"),
            ]
        },
        {
            "category": "无效格式",
            "cases": [
                ("invalid-time", None, "完全无效"),
                ("2024-13-45 25:70:80", None, "超出范围"),
                ("", None, "空字符串"),
                (None, None, "None值"),
            ]
        }
    ]
    
    try:
        from script.test_result_format_ae_ocr.nodes import TimeUtils
        
        total_success = 0
        total_cases = 0
        
        for category_info in test_categories:
            category = category_info["category"]
            cases = category_info["cases"]
            
            print(f"\n🔧 测试类别: {category}")
            print("-" * 60)
            
            category_success = 0
            for i, (input_time, expected, description) in enumerate(cases, 1):
                total_cases += 1
                
                print(f"{i:2d}. {description}")
                print(f"    输入: {input_time}")
                print(f"    期望: {expected}")
                
                try:
                    result = TimeUtils.attempt_time_fix(input_time)
                    print(f"    输出: {result}")
                    
                    # 判断是否成功
                    if result == expected:
                        category_success += 1
                        total_success += 1
                        print(f"    状态: ✅ 成功")
                    else:
                        print(f"    状态: ❌ 失败")
                        
                except Exception as e:
                    print(f"    状态: ❌ 异常 - {e}")
                
                print()
            
            success_rate = (category_success / len(cases)) * 100
            print(f"类别成功率: {category_success}/{len(cases)} ({success_rate:.1f}%)")
        
        # 总体结果
        overall_success_rate = (total_success / total_cases) * 100
        print("\n" + "=" * 80)
        print(f"总体测试结果: {total_success}/{total_cases} 成功 ({overall_success_rate:.1f}%)")
        
        if overall_success_rate >= 90:
            print("🎉 分层匹配策略测试通过！")
            print("💡 每种时间格式错误都有对应的处理方案")
            return True
        else:
            print("⚠️  分层匹配策略仍需优化")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_early_exit_mechanism():
    """测试早期退出机制"""
    print("\n" + "=" * 80)
    print("早期退出机制测试")
    print("=" * 80)
    
    # 测试用例：应该被第一个匹配的模式处理
    test_cases = [
        {
            "input": "2024-06-278:00:15",
            "expected_pattern": "日期末位与时间首位连接",
            "description": "应该被最具体的模式匹配"
        },
        {
            "input": "2024-07-0213:42",
            "expected_pattern": "完整日期与时间直接连接",
            "description": "应该被特定模式匹配，而不是通用模式"
        }
    ]
    
    print("🔧 测试早期退出机制:")
    print("-" * 60)
    
    try:
        from script.test_result_format_ae_ocr.nodes import TimeUtils
        
        success_count = 0
        for i, case in enumerate(test_cases, 1):
            print(f"{i}. {case['description']}")
            print(f"   输入: {case['input']}")
            
            # 这里我们主要测试是否能成功修复
            # 在实际实现中，可以通过日志来验证使用了哪个模式
            result = TimeUtils.attempt_time_fix(case['input'])
            
            if result and TimeUtils.is_valid_django_datetime(result):
                success_count += 1
                print(f"   输出: {result}")
                print(f"   状态: ✅ 成功修复")
            else:
                print(f"   输出: {result}")
                print(f"   状态: ❌ 修复失败")
            print()
        
        if success_count == len(test_cases):
            print("✅ 早期退出机制工作正常")
            return True
        else:
            print("❌ 早期退出机制存在问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_pattern_priority():
    """测试模式优先级"""
    print("\n" + "=" * 80)
    print("模式优先级测试")
    print("=" * 80)
    
    print("📋 分层匹配策略的优先级顺序:")
    print("-" * 60)
    
    priorities = [
        "1. 日期末位与时间首位连接 (最具体)",
        "2. 完整日期与时间直接连接",
        "3. 通用无空格格式",
        "4. 中文时间格式",
        "5. 中文冒号格式 (最通用)",
    ]
    
    for priority in priorities:
        print(f"   {priority}")
    
    print("\n💡 设计原则:")
    print("   - 从最具体到最通用的顺序")
    print("   - 早期退出：匹配成功立即返回")
    print("   - 每种错误格式都有专门的处理逻辑")
    print("   - 提高修复成功率和代码可维护性")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始分层匹配策略测试\n")
    
    # 运行分层匹配测试
    layered_success = test_layered_time_matching()
    
    # 运行早期退出测试
    early_exit_success = test_early_exit_mechanism()
    
    # 运行优先级测试
    priority_success = test_pattern_priority()
    
    # 总结
    print("\n" + "=" * 80)
    print("分层匹配策略测试总结")
    print("=" * 80)
    
    if layered_success and early_exit_success and priority_success:
        print("🎉 所有测试通过！分层匹配策略实现成功")
        print("✅ 分层匹配: 通过")
        print("✅ 早期退出: 通过")
        print("✅ 优先级设计: 通过")
        print("\n💡 关键改进:")
        print("  - 每种时间格式错误都有专门的处理方案")
        print("  - 按优先级从具体到通用的匹配顺序")
        print("  - 早期退出机制提高性能")
        print("  - 代码结构清晰，易于维护和扩展")
        return True
    else:
        print("⚠️  部分测试失败:")
        print(f"  分层匹配: {'✅ 通过' if layered_success else '❌ 失败'}")
        print(f"  早期退出: {'✅ 通过' if early_exit_success else '❌ 失败'}")
        print(f"  优先级设计: {'✅ 通过' if priority_success else '❌ 失败'}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
