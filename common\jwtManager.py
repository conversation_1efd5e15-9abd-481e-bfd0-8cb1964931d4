import time

import jwt
from django.conf import settings

token_secret = settings.ONLY_OFFICE['secret']

# 加密payload 对象为 token，有效期为 2 小时
def encode(payload):
    # now = time.time()
    # payload['iat'] = now
    # payload['exp'] = now + 2 * 60 * 60
    return jwt.encode(payload, token_secret, algorithm='HS256')


# 解密token
def decode(string):
    return jwt.decode(string, token_secret, algorithms=['HS256'])


