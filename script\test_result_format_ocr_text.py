import os
import json
import ast
import math
import base64
import requests
import re
import pandas as pd
from concurrent.futures import ThreadPoolExecutor
from sqlalchemy import text
from PIL import Image, ImageDraw
import requests
import re
import base64
import ast
import time
import json
from common.minio_client import get_minio_client
# from common.tools import sql_to_df
import io
import fitz
from datetime import datetime, timedelta
import base64
import argparse
import asyncio

import os
import django
from django.db import connections
from django.conf import settings

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

django.setup()
from sqlalchemy import create_engine
from sqlalchemy.pool import NullPool
from common.tools import get_db_engin_url, collect_time_choice
from django.db import transaction
from apps.medical.models import MedicalFileMasked
from apps.ae_tracker.models import TestResult, AeTrackerTask, TestOcrResult
from apps.subject.models import SubjectItem, Subject
from script.test_result_format_ocr import request_llm_api1
from apps.system.models import ModelInvocationLog
from apps.subject_medical.models import SubjectMedicalInfo

db_erp = create_engine(get_db_engin_url('default'), poolclass=NullPool)


def close_db_connection():
    # 关闭数据库连接
    db_erp.dispose()
    print("Database connection closed")


def getBase64(data):
    if isinstance(data, str):
        # 如果传入的是文件路径
        with open(data, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode()
    elif isinstance(data, bytes):
        # 如果传入的是字节流
        encoded_string = base64.b64encode(data).decode()
    else:
        raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")
    return encoded_string


def getOcrResult(url, payload, headers):
    """通过 API 获取 OCR 结果"""
    response = requests.post(url, headers=headers, data=payload, verify=False)
    return response.text


def getKeyWords(ocrResult):
    """提取 OCR 结果中的文本内容"""
    text = ""
    try:
        ocr_dict = ast.literal_eval(ocrResult)
        text_list = []
        for result in ocr_dict["result"]["words_block_list"]:
            text_list.append(result["words"])
            text += result["words"] + "\n"
        print(text_list)
    except Exception as e:
        print(f"❌ OCR结果解析失败: {e}")
    return text.strip()


def ocr_main(imgpath):
    """执行图片 OCR 识别并返回识别到的文本内容"""
    try:
        # 读取图片数据
        if isinstance(imgpath, str):
            with open(imgpath, "rb") as image_file:
                image_bytes = image_file.read()
        elif isinstance(imgpath, bytes):
            image_bytes = imgpath
        else:
            raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")
        
        # 使用统一的OCR服务，启用角度矫正（对应原来的detect_direction=True）
        from common.clients.ocr_service import process_ocr
        result = process_ocr(image_bytes, use_correction=True)
        
        # 直接使用统一OCR服务返回的markdown_text作为完整文本
        text = result.get("markdown_text", "")
        
        print(f"📝 提取的文本长度: {len(text)}")
        return text.strip()
        
    except Exception as e:
        print(f"❌ 图片 {imgpath} OCR识别失败: {e}")
        return ""


# 调用 LLM 处理 OCR 结果转换为 JSON
def request_llm_api111111(ocr_text):
    """将 OCR 结果传给大模型，转换为标准的 JSON 格式"""
    url = 'https://***************:30334/v1/infer/46c6c41b-ae45-4362-92f9-152e6cc67975/v1/chat/completions'
    headers = {
        "Content-Type": "application/json",
        "Auth-Username": "mauser",
        "Auth-Password": "Prs@123456"
    }
    example_json = """
    {
        "test_results": [
        {
        "test_code": "检查代码",
        "test_name": "检查名称",
        "test_value": "检查结果值",
        "test_flag": "检查结果值标志",//0：正常，1：偏高，2：偏低；不可为空，必须为 0，1，2 三个当中的一个
        "test_type": "检查结果值类型",//必须为 数值，定性 两个当中的一个
        "test_unit": "检查单位",
        "reference_value": "参考范围",
        "reference_range_min": "参考范围最小值",
        "reference_range_max": "参考范围最大值",
        "collect_time": "采集时间",//时间格式需要转换为 yyyy-mm-dd hh:mm:ss
        "report_time": "报告时间",//时间格式需要转换为 yyyy-mm-dd hh:mm:ss
        },
        // 若有多个测试结果，按照上述格式继续添加对象，每个对象对应一个测试结果；每个对象里面的字段是可以为空的。
        ]
    }
    """
    body = {
        "model": "DeepSeek-R1-Distill-Qwen-32B",
        "messages": [{"role": "user",
                      "content": f"请参考以下输出例子结构，将 OCR 识别结果转为标准的 JSON 格式。例子结构如下：{example_json}。OCR 识别结果为：{ocr_text}。不要返回除了JSON格式的字符串之外任何其他额外其他的内容。"}],
        "max_tokens": 8192,
        "temperature": 0.0,
        "top_p": 0.95,
        "stream": False
    }

    print("🚀 开始调用【Deepseek-distil-Qwen32B】大模型转换 OCR 结果为 JSON...")
    response = requests.post(url, json=body, headers=headers, verify=False)
    result = response.json()

    # 打印大模型返回的完整JSON
    # print(f"📄 大模型返回的 OCR JSON:\n{json.dumps(result, indent=4)}")

    content = result.get("choices", [{}])[0].get("message", {}).get("content", "{}")
    cleaned_content = clean_llm_response(content)

    print(f"📄 处理后的 JSON OCR 结果:\n{cleaned_content}")
    pattern = r'```json(.*?)```'
    match = re.search(pattern, cleaned_content, re.DOTALL)
    cleaned_content = {"test_results": []}
    if match:
        cleaned_content = match.group(1)
    cleaned_content = json.loads(cleaned_content)
    return cleaned_content


# 清理大模型返回的内容，去除 <think> 标签内的内容
def clean_llm_response(content):
    """清理大模型返回的内容"""
    # 使用正则去除 <think> 标签及其内容
    return re.sub(r"<think>.*?</think>", "", content, flags=re.DOTALL).strip()


def process_images_in_directory(images_directory):
    """遍历指定文件夹中的所有图片文件并进行 OCR 识别"""
    ocr_texts = []
    print("🔍 开始进行 OCR 识别...")

    for filename in sorted(os.listdir(images_directory)):
        image_path = os.path.join(images_directory, filename)

        if image_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
            ocr_result = ocr_main(image_path)
            char_count = len(ocr_result)
            print(f"📄 图片 {filename} 识别完成，字符数: {char_count}")

            if ocr_result:
                ocr_texts.append(f"## {filename}\n{ocr_result}\n")

    print("✅ OCR 识别全部完成！")
    return "\n".join(ocr_texts)


def main(images_directory):
    """主函数：获取 OCR 识别并将结果转换为 JSON"""
    ocr_text = process_images_in_directory(images_directory)
    if ocr_text:
        ocr_json = request_llm_api1(ocr_text)
        print("OCR 转换为 JSON 后的结果：")
        print(ocr_json)
    else:
        print("⚠️ 没有获取到 OCR 识别结果")


def hw_ocr_general_text(
    image_path=None,
    image_url=None,
    detect_direction=True,
    quick_mode=False,
    character_mode=False,
    language="zh",
    single_orientation_mode=True,
    pdf_page_number=1
):
    """
    调用华为 OCR API 进行文字识别。
    :param project_id: 项目 ID
    :param token: 认证 Token
    :param image_path: 本地图片路径（与 image_url 二选一）
    :param image_url: 远程图片 URL（与 image_path 二选一）
    :param detect_direction: 是否校正图片倾斜角度
    :param quick_mode: 是否开启快速模式
    :param character_mode: 是否开启单字符模式
    :param language: 语言选择（默认为中英文）
    :param single_orientation_mode: 是否开启单朝向模式
    :param pdf_page_number: 指定 PDF 识别的页码
    :param endpoint: OCR API 终端节点
    :return: 识别结果
    """
    try:
        if image_path:
            # 处理图片路径或字节数据
            if isinstance(image_path, str):
                with open(image_path, "rb") as file:
                    image_bytes = file.read()
            else:
                image_bytes = image_path
        elif image_url:
            # 处理远程图片URL
            response = requests.get(image_url)
            response.raise_for_status()
            image_bytes = response.content
        else:
            raise ValueError("image_path 或 image_url 其中之一必须提供")

        # 使用统一OCR服务，映射detect_direction参数到use_correction
        use_correction = detect_direction  # 映射参数
        from common.clients.ocr_service import process_ocr
        result = process_ocr(image_bytes, use_correction=use_correction)
        
        # 返回原始OCR结果格式，保持向后兼容
        return result["ocr_result"]
        
    except Exception as e:
        print(f"❌ OCR服务调用失败: {e}")
        raise


def format_ocr_text(result, image_path):
    """处理 OCR 识别结果，返回格式化的文本"""
    def rotate_point(point, Cx, Cy, theta):
        """计算旋转后的坐标"""
        x, y = point

        # 平移到中心
        x_prime = x - Cx
        y_prime = y - Cy

        # 应用旋转公式
        x_double_prime = x_prime * math.cos(theta) + y_prime * math.sin(theta)
        y_double_prime = -x_prime * math.sin(theta) + y_prime * math.cos(theta)

        # 平移回原坐标系
        x_rot = x_double_prime + Cx
        y_rot = y_double_prime + Cy

        return [x_rot, y_rot]

    # 提取带位置信息的文本块
    text_blocks = []
    if "result" in result and "words_block_list" in result["result"]:
        ocr_result = result["result"]
        direction = ocr_result["direction"]

        if direction:
            print(f"direction: {direction}")
            # image = Image.open(image_path)
            image_file = io.BytesIO(image_path)
            # 用 Image.open 打开文件对象
            image = Image.open(image_file)
            # 获取图片宽高
            W, H = image.size
            Cx, Cy = W / 2, H / 2  # 中心点

            # 顺时针旋转角度（弧度制）
            theta = math.radians(direction)

            for box in ocr_result["words_block_list"]:
                location = []
                for point in box['location']:
                    # 旋转单个点
                    rotated_point = rotate_point(point, Cx, Cy, theta)
                    location.append(rotated_point)
                box['location'] = location

        # 处理文本块
        for block in result["result"]["words_block_list"]:
            if "words" in block and "location" in block:
                text = block["words"]
                location = block["location"]

                # 计算文本块中心点坐标
                center_x = sum(point[0] for point in location) / 4
                center_y = sum(point[1] for point in location) / 4

                # 计算高度 (使用左上角和左下角的y坐标差)
                # 这里我们使用中心点高度作为参考
                min_y = min([p[1] for p in location])
                max_y = max([p[1] for p in location])
                height = max_y - min_y

                text_blocks.append({
                    "text": text,
                    "center_y": center_y,
                    "center_x": center_x,
                    "height": height if height > 0 else 1  # 确保高度为正
                })

    if not text_blocks:
        return ""

    # 基于文本块高度分析确定行高容差
    heights = [block["height"] for block in text_blocks]

    if not heights:
        return ""

    # avg_height = sum(heights) / len(heights)

    # 使用中位数可能比平均值更稳健
    heights.sort()
    median_height = heights[len(heights) // 2]

    # 根据中位数计算行高容差
    line_height_tolerance = median_height * 0.7  # 可以根据实际情况调整系数

    # 使用确定的line_height_tolerance分组文本块
    rows = {}

    for block in text_blocks:
        # 查找最近的行
        found_row = False
        for row_y in rows.keys():
            if abs(block["center_y"] - row_y) < line_height_tolerance:
                rows[row_y].append(block)
                found_row = True
                break

        # 如果没有找到匹配的行，创建新行
        if not found_row:
            rows[block["center_y"]] = [block]

    # 对每一行按x坐标排序，然后将行按y坐标排序
    formatted_text = ""
    for row_y in sorted(rows.keys()):
        row_blocks = sorted(rows[row_y], key=lambda b: b["center_x"])
        line_text = " ".join([block["text"] for block in row_blocks])
        formatted_text += line_text + "\n"

    return formatted_text.strip()


def add_timestamp(func):
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        return result, end_time
    return wrapper


async def async_get_responses(prompt, model_type1, model_type2):
    wrapped_api = add_timestamp(request_llm_api1)

    task1 = asyncio.to_thread(wrapped_api, prompt, model_type1)
    task2 = asyncio.to_thread(wrapped_api, prompt, model_type2)
    return await asyncio.gather(task1, task2)

def get_responses_sync(prompt, model_type1, model_type2):
    return asyncio.run(async_get_responses(prompt, model_type1, model_type2))


def process_row(task_id):
    task = AeTrackerTask.objects.filter(id=task_id, category=AeTrackerTask.OCR_EXTRACTION_TEXT, delete_flag=0).first()
    if not task:
        print(f"没有发现任务")
        return
    subject_id = task.subject_id
    subject_item_id = task.subject_item_id
    queryset = SubjectItem.objects.select_related(
        'subject_visit').filter(delete_flag=0, subject_item_id=subject_item_id).first()
    if queryset:
        data = [{
            'subject_visit__visit_date': queryset.subject_visit.visit_date if queryset.subject_visit else None,
            'item_type': queryset.item_type,
            'item_id': queryset.item_id
        }]
    else:
        data = []
    print(data)
    try:
        ocr_result = SubjectMedicalInfo.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0,
            ocr_text_mask__isnull=False  # 排除空值
        ).values_list('ocr_text_mask', flat=True)
        ocr_text = '\n\n'.join(ocr_result)

        print(ocr_text)
        start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        (llm1_response, end_time1), (llm2_response, end_time2) = get_responses_sync(ocr_text, 'DeepSeek-R1-Distill-Qwen-32B', 'qwen3-32b')
        ocr_json, inputText, outputText, think, generated_tokens = llm1_response[0], llm1_response[1], llm1_response[2], llm1_response[3], llm1_response[4]
        ocr_json1, inputText1, outputText1, think1, generated_tokens1 = llm2_response[0], llm2_response[1], llm2_response[2], llm2_response[3], llm2_response[4]
        result = {}
        result['task_id'] = task_id
        result['create_user'] = task.create_user
        result['create_name'] = task.create_name
        result['category'] = 'DISEASE_RECOGNITION'
        result['model_name'] = 'DeepSeek-R1-Distill-Qwen-32B'
        result['start_time'] = start_time
        result['end_time'] = end_time1
        result['input_text'] = inputText
        result['think_text'] = think
        result['output_text'] = outputText
        result['prompt_tokens'] = generated_tokens['prompt_tokens']
        result['completion_tokens'] = generated_tokens['completion_tokens']
        result['business_id'] = task.patient_id
        ModelInvocationLog.objects.create(**result)

        result['model_name'] = 'qwen3-32b'
        result['start_time'] = start_time
        result['end_time'] = end_time2
        result['input_text'] = inputText1
        result['think_text'] = think1
        result['output_text'] = outputText1
        result['prompt_tokens'] = generated_tokens1['prompt_tokens']
        result['completion_tokens'] = generated_tokens1['completion_tokens']
        result['business_id'] = task.patient_id
        ModelInvocationLog.objects.create(**result)
        print(ocr_json)
        print(ocr_json1)
        # llm_results = [ocr_json, ocr_json1]
        seen_values = set()
        merged_results = []
        for item in ocr_json + ocr_json1:
            value = item["test_name"]
            if value not in seen_values:
                seen_values.add(value)
                merged_results.append({"test_name": value, "collect_time": item["collect_time"], "report_time": item["report_time"]})  # 只保留 test_value
        # 构建最终结果
        llm_results = merged_results
        test_value_list_deepseek = [item["test_name"] for item in ocr_json]
        test_value_list_pangu = [item["test_name"] for item in ocr_json1]
        subject_itemss = SubjectItem.objects.get(
            subject_item_id=subject_item_id,
            subject_id=subject_id, delete_flag=0)
        subjectss = Subject.objects.get(subject_id=subject_id, delete_flag=0)
        subject_visit = subject_itemss.subject_visit
        test_result_instances = []
        for i, result in enumerate(llm_results):
            print(result)
            print(1111111111111111111111)
            # result['report_time'] = '2024-09-14 09:38'
            # 假设result是你的字典
            # if result:  # 确保字典不为空
            #     # 获取需要保留的键列表
            #     keys_to_keep = ['test_value']  # 可以添加更多需要保留的字段
            #     # 获取需要删除的键列表（使用列表推导式）
            #     keys_to_delete = [key for key in result if key not in keys_to_keep]
            #     # 批量删除不需要的键
            #     for key in keys_to_delete:
            #         del result[key]
            print(result['collect_time'])
            print(type(result['collect_time']))
            collect_time = collect_time_choice(result['collect_time'], data)
            result['collect_time'] = collect_time

            if result['report_time'] == '':
                result['report_time'] = None

            if result['test_name'] in test_value_list_deepseek and result['test_name'] in test_value_list_pangu:
                ai_test_result_list = [{"model": "DS"}, {"model": "qwen3_32b"}]
            elif result['test_name'] in test_value_list_deepseek:
                ai_test_result_list = [{"model": "DS"}]
            elif result['test_name'] in test_value_list_pangu:
                ai_test_result_list = [{"model": "qwen3_32b"}]
            result['seq'] = i + 1
            # 去除所有不可见字符（保留中英文、数字、常见符号）
            result['test_name'] = re.sub(r'[\x00-\x1F\x7F]', '', result['test_name'])
            length = len(result['test_name'])
            print(f"字符串长度：{length}")  # 输出实际长度，确认是否超过100
            if length > 100:
                result['test_name'] = result['test_name'][:100]  # 截断为100字符
            result['test_value'] = result['test_name']
            result['ai_test_result_list'] = ai_test_result_list
            result['test_flag'] = 1
            result['abnormal_flag'] = 0
            result['subject_medical_info_id'] = 1
            result['project'] = subject_itemss.project
            result['project_site'] = subject_itemss.project_site
            result['subject_id'] = subject_id
            result['patient'] = subjectss.patient
            result['subject_item_id'] = subject_item_id
            result['subject_epoch'] = subject_visit.subject_epoch
            result['subject_visit'] = subject_visit
            print(result)
            test_result_instances.append(TestResult(**result))
        with transaction.atomic():
            TestResult.objects.bulk_create(test_result_instances)
            # for instance in created_instances:
            #     instance.seq = float(instance.id)
            # TestResult.objects.bulk_update(created_instances, ['seq'])
            params = {
                'subject_id': subject_id,
                'subject_item_id': subject_item_id,
                'ae_ai_current_step': 2
            }
            sql = text(
                f"""update subject_item_info set ae_ai_current_step=:ae_ai_current_step where subject_id=:subject_id and subject_item_id=:subject_item_id and ae_ai_current_step <= 2""")
            # print(sql)q
            with db_erp.begin() as conn:
                conn.execute(sql, params)
            conn.close()

            params = {
                'which_need_update_id': task_id,
                'mask_status': 'COMPLETED',
                'end_time': datetime.now()
            }
            sql = text(
                f"""update ae_tracker_task set status=:mask_status,end_time=:end_time where id=:which_need_update_id""")
            # print(sql)q
            with db_erp.begin() as conn:
                conn.execute(sql, params)
            conn.close()
            print(task_id)
            return True

    except Exception as e:
        print(e)
        import traceback
        print(traceback.format_exc())
        with db_erp.begin() as conn:
            # 执行第一个更新操作
            params = {
                'which_need_update_id': task_id,
                'mask_status': 'ERROR',
                'end_time': datetime.now()
            }
            sql = text("""
                UPDATE ae_tracker_task 
                SET status=:mask_status, end_time=:end_time 
                WHERE id=:which_need_update_id
            """)
            conn.execute(sql, params)

            # 执行第二个更新操作
            params = {
                'subject_id': subject_id,
                'subject_item_id': subject_item_id,
                'ae_ai_current_step': 0
            }
            sql = text("""
                UPDATE subject_item_info 
                SET ae_ai_current_step=:ae_ai_current_step 
                WHERE subject_id=:subject_id 
                AND subject_item_id=:subject_item_id 
                AND ae_ai_current_step <= 2
            """)
            conn.execute(sql, params)
        conn.close()
        print(task_id)
        return False


# def main():
#     sql = f"""
#                 select
#                 id,subject_id,subject_item_id
#                 from ae_tracker_task
#                 where delete_flag = 0 and (status = 'TODO' or status = 'IN_PROGRESS') and category = 'OCR_EXTRACTION'
#             """
#     df_task = pd.read_sql_query(sql, db_erp)
#
#     with ThreadPoolExecutor(max_workers=4) as executor:
#         futures = [executor.submit(process_row, row) for _, row in df_task.iterrows()]
#         for future in futures:
#             future.result()


def main():
    parser = argparse.ArgumentParser(description="AE-OCR任务")
    parser.add_argument('--task_id', type=int, required=True, help="task_id")
    args = parser.parse_args()
    process_row(args.task_id)


if __name__ == "__main__":
    main()
    close_db_connection()

    # image_path = r'C:\Users\<USER>\Desktop\检验单2.webp'
    # with open(image_path, 'rb') as file:
    #     # 读取文件内容到字节流
    #     file_bytesX = file.read()


    # result = hw_ocr_general_text(image_path=file_bytesX)
    # print(result)
    # ocr_text = format_ocr_text(result, image_path=file_bytesX)
    # print(ocr_text)

    # ocr_text = ocr_main(file_bytesX)
    # print(ocr_text)
    #
    # if ocr_text:
    #     ocr_json = request_llm_api1(ocr_text)
    #     print(ocr_json)

