import re
import json
import logging

import requests
import json_repair
from django.conf import settings

def pangu_v35_128k(content):
    """
    请求指定大模型 API，并返回结果
    """
    url = 'https://192.168.230.105:30334/v1/infer/6586eec4-d5ae-460f-9756-0accbb6b3db2/v1/chat/completions'

    headers = {
        "Content-Type": "application/json",
        "Auth-Username": "mauser",
        "Auth-Password": "Prs@123456"
    }
    body = {
        "model": 'pangu',
        "messages": [{"role": "user", "content": content}],
        "max_tokens": 8192,
        "temperature": 0,
        "top_p": 0.95,
        "stream": False
    }
    response = requests.post(url, json=body, headers=headers, verify=False)
    logging.debug(response)
    return response


def deepseek_r1_qwen_32b(content):
    """
    请求指定大模型 API，并返回结果
    """
    url = 'https://192.168.230.105:30334/v1/infer/46c6c41b-ae45-4362-92f9-152e6cc67975/v1/chat/completions'
    headers = {
        "Content-Type": "application/json",
        "Auth-Username": "mauser",
        "Auth-Password": "Prs@123456"
    }
    url = "http://192.168.230.5:48080/v1/chat/completions"  # 更新URL
    API_KEY = settings.QWEN_API_KEY
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}",
    }
    body = {
        "model": 'deepseek-r1-distill-qwen-32b',
        "messages": [{"role": "user", "content": content}],
        "max_tokens": 8192,
        "temperature": 0.3,
        "top_p": 0.5,
        "stream": False
    }
    response = requests.post(url, json=body, headers=headers, verify=False)
    logging.debug(response)
    return response


def deepseek_r1_llama_70b(content):
    """
    请求指定大模型 API，并返回结果
    """
    url = 'https://192.168.230.105:30334/v1/infer/ad266056-4b1f-4a1a-bdb2-8a5bbc2193fb/v1/chat/completions'
    headers = {
        "Content-Type": "application/json",
        "Auth-Username": "mauser",
        "Auth-Password": "Prs@123456"
    }
    body = {
        "model": 'DeepSeek-R1-Distill-Llama-70B',
        "messages": [{"role": "user", "content": content}],
        "max_tokens": 8192,
        "temperature": 0,
        "top_p": 0.95,
        "stream": False
    }
    response = requests.post(url, json=body, headers=headers, verify=False)
    logging.debug(response)
    return response


def pangu_reasoner(content):
    """
    请求指定大模型 API，并返回结果
    """
    url = "http://192.168.230.4:8089/v1/chat/completions"

    headers = {
        "Content-Type": "application/json",
    }
    body = {
        "model": 'pangu',
        "messages": [{"role": "user", "content": content}],
        "max_tokens": 8192,
        "temperature": 0,
        "top_p": 0.95,
        "stream": False
    }
    response = requests.post(url, json=body, headers=headers, verify=False)
    logging.debug(response)
    return response


def qwen3_32b_pass(content):
    """
    请求指定大模型 API，并返回结果

    """
    url = "http://192.168.230.4:1040/v1/chat/completions"

    headers = {
        "Content-Type": "application/json",
    }
    body = {
        "model": 'qwen3-32b',
        "messages": [{"role": "user", "content": content}],
        "max_tokens": 8192,
        "temperature": 0.3,
        "top_p": 0.5,
        "stream": False
    }
    response = requests.post(url, json=body, headers=headers, verify=False)
    logging.debug(response)
    return response


def qwen3_32b(content):
    """
    请求指定大模型 API，并返回结果
    """
    url = "http://192.168.230.5:48080/v1/chat/completions"  # 更新URL
    API_KEY = settings.QWEN_API_KEY
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}",
    }
    body = {
        "model": "qwen3-32b",  # 更新模型名称（小写）
        "messages": [{"role": "user", "content": content}],
        "max_tokens": 8192,
        "temperature": 0.3,
        "top_p": 0.5,
        "stream": False
    }
    response = requests.post(url, json=body, headers=headers, verify=False)
    logging.debug(response)
    return response

def qwen3_8b(content):
    """
    请求指定大模型 API，并返回结果
    """
    url = "http://192.168.230.5:48080/v1/chat/completions"  # 更新URL
    API_KEY = settings.QWEN_API_KEY
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}",
    }
    body = {
        "model": "qwen3-8b",  # 更新模型名称（小写）
        "messages": [{"role": "user", "content": content}],
        "max_tokens": 8192,
        "temperature": 0,
        "top_p": 0.95,
        "stream": False
    }
    response = requests.post(url, json=body, headers=headers, verify=False)

    return response

def get_json_from_ai_reply(response):
    """从AI回复中获取JSON内容"""
    try:
        data = response.json()
        assistant_message = data.get("choices", [{}])[0].get("message", {}).get("content", "")
        print(assistant_message)
        # generated_tokens = data.get('usage', {})
        # print(generated_tokens)
        json_pattern = re.search(r'```json\s*(.*?)\s*```', assistant_message, re.DOTALL)
        if json_pattern:
            return json_repair.loads(json_pattern.group(1).strip())

    except (json.JSONDecodeError, KeyError, IndexError):
        import traceback
        traceback.print_exc()

    # 解析失败时返回 None
    return None


def get_generated_tokens_think_from_ai_reply(response, prompt):
    """从AI回复中获取JSON内容"""
    try:
        result = response.json()
        inputText = prompt
        outputText = result
        think = result["choices"][0]["message"]["content"]
        # start_index = think.find("<think>") + len("<think>")
        # end_index = think.find("</think>")
        # think = think[start_index:end_index]
        json_pattern = re.search(r'<think>\s*(.*?)\s*</think>', think, re.DOTALL)
        if json_pattern:
            think = json_pattern.group(1).strip()
        else:
            think = None
        generated_tokens = result.get('usage', {})
        if generated_tokens:
            return generated_tokens, inputText, outputText, think

    except (json.JSONDecodeError, KeyError, IndexError):
        import traceback
        traceback.print_exc()

    # 解析失败时返回 None
    return None


def get_think_text_from_ai_reply(response):
    """从AI回复中获取<think>标签中的文本内容"""
    try:
        data = response.json()
        assistant_message = data.get("choices", [{}])[0].get("message", {}).get("content", "")
        print(assistant_message)
        json_pattern = re.search(r'<think>\s*(.*?)\s*</think>', assistant_message, re.DOTALL)
        if json_pattern:
            return json_pattern.group(1).strip()
    except Exception as e:
        print("解析出错：", e)

    return None


def get_reasoning_content_from_ai_reply(response):
    """从AI回复中获取<think>标签中的文本内容"""
    try:
        data = response.json()
        reasoning_content = data.get("choices", [{}])[0].get("message", {}).get("reasoning_content", "")
        return reasoning_content
    except Exception as e:
        print("解析出错：", e)

    return None


def get_json_from_ai_word_reply(response, model_name):
    """
    提取 <think>...</think> 标签后面的 JSON 段落内容。

    参数:
        text (str): 包含 <think> 标签和 JSON 内容的完整字符串。

    返回:
        list: 提取出的段落内容（JSON 解析后的对象）。
    """
    # 使用正则匹配 </think> 后的部分
    # data = response.json()
    # text = data.get("choices", [{}])[0].get("message", {}).get("content", "")
    # match = re.search(r"</think>\s*(\[.*\])", text, re.DOTALL)
    # if match:
    #     json_str = match.group(1)
    #     try:
    #         data = json.loads(json_str)
    #         return data
    #     except json.JSONDecodeError as e:
    #         print(f"JSON解析错误: {e}")
    #         return []
    # else:
    #     print("未找到 </think> 后的 JSON 段落")
    #     return []
    """
        提取 </think> 后的 Markdown 文本内容。
        """
    data = response.json()
    text = data.get("choices", [{}])[0].get("message", {}).get("content", "")

    # 提取 </think> 后所有文本（直到末尾）
    match = re.search(r"</think>\s*(.+)", text, re.DOTALL)
    if match:
        return match.group(1).strip()
    else:
        if model_name == 'DeepSeek':
            print("未找到 </think> 后的 Markdown 段落")
            return ""
        if model_name == 'pangu':
            return text
def get_findall_json_from_ai_reply(response):
    """
    从全量内容中提取所有json，并判断提取json的结果为list或者dict的默认为正确结果。
    """
    try:
        data = response.json()
        assistant_message = data.get("choices", [{}])[0].get("message", {}).get("content", "")
        print(assistant_message)
        # assistant_message = '```json```'
        json_pattern = re.findall(r'```json\s*(.*?)\s*```', assistant_message, re.DOTALL)
        for i in json_pattern:
            parsed = json_repair.loads(i)
            # print(parsed)
            if i and isinstance(parsed, (list, dict)):
                return parsed
    except (json.JSONDecodeError, KeyError, IndexError):
        import traceback
        traceback.print_exc()
        
    return None

def qwen3_32b_test(content):
    """
    本地测试时使用
    请求指定大模型 API，并返回结果
    """
    url = "http://192.168.230.5:48080/v1/chat/completions"  # 更新URL
    API_KEY = "gpustack_a737c3c0edaae104_5a909ed4b73f5422b5dfd104342b2e5d"
    # API_KEY = settings.QWEN_API_KEY
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}",
    }
    body = {
        "model": "qwen3-32b",  # 更新模型名称（小写）
        "messages": [{"role": "user", "content": content}],
        "max_tokens": 8192,
        "temperature": 0.3,
        "top_p": 0.5,
        "stream": False
    }
    response = requests.post(url, json=body, headers=headers, verify=False)
    logging.debug(response)
    return response


if __name__ == "__main__":
    # python -m common.llm_tools

    # result = deepseek_r1_qwen_32b('人生的意义，返回json')
    # print(result.text)
    # print(get_think_text_from_ai_reply(result))
    # print(get_json_from_ai_reply(result))

    # result = pangu_reasoner('人生的意义，返回json')
    # print(result.text)
    # print(get_reasoning_content_from_ai_reply(result))
    # print(get_json_from_ai_reply(result))
    
    # with open('./tmp/res.json') as f:
    #     data = json.load(f)
    # print(re.search(r'```json\s*(.*?)\s*```', data['choices'][0]['message']['content'], re.DOTALL).group(1))
    pass
