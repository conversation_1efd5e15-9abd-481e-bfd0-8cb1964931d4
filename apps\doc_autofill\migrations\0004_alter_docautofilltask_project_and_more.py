# Generated by Django 4.1.5 on 2025-04-24 09:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("project", "0002_alter_project_update_time_and_more"),
        ("doc_autofill", "0003_hospdoctmplfile_create_name_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="docautofilltask",
            name="project",
            field=models.ForeignKey(
                blank=True,
                db_column="project_id",
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_autofill_tasks",
                to="project.project",
                to_field="project_id",
                verbose_name="项目ID",
            ),
        ),
        migrations.AlterField(
            model_name="docautofilltask",
            name="project_site",
            field=models.ForeignKey(
                blank=True,
                db_column="project_site_id",
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_autofill_tasks",
                to="project.projectsite",
                to_field="project_site_id",
                verbose_name="项目中心ID",
            ),
        ),
    ]
