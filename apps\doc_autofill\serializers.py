from rest_framework import serializers

from common.serializers import FileUrlMixin
from . import models


###################文件存储对象###################
class HospDocTmplFileSerializer(FileUrlMixin, serializers.ModelSerializer):
    url = serializers.SerializerMethodField(label='文件下载url')

    class Meta:
        model = models.HospDocTmplFile
        exclude = ['create_user', 'create_name', 'update_user', 'update_name']

class HospDocTmplFileProcessedSerializer(FileUrlMixin, serializers.ModelSerializer):
    url = serializers.SerializerMethodField(label='文件下载url')

    class Meta:
        model = models.HospDocTmplFileProcessed
        exclude = ['create_user', 'create_name', 'update_user', 'update_name']

class ProjectSiteDocFileSerializer(FileUrlMixin, serializers.ModelSerializer):
    url = serializers.SerializerMethodField(label='文件下载url')

    class Meta:
        model = models.ProjectSiteDocFile
        exclude = ['create_user', 'create_name', 'update_user', 'update_name']


###################中心模板###################
# 中心模板列表序列化
class HospDocTmplCenterListSerializers(serializers.Serializer):
    # hospital 数据
    hosp_id = serializers.CharField(source='hospital.hosp_id',label="铨融医院ID")
    hosp_code = serializers.CharField(source='hospital.hosp_code',label="中心编码")
    hosp_name = serializers.CharField(source='hospital.hosp_name',label="中心名称")
    site_leader_id = serializers.CharField(source='hospital.site_leader_id',label="siteleader工号")
    site_leader_name = serializers.CharField(source='hospital.site_leader_name',label="siteleader")
    # hosp_doc_tmpl_center 数据
    id = serializers.IntegerField(label="模板中心ID")
    status_code = serializers.CharField(label="中心模板状态 UNRELEASED 未发布，RELEASED 已发布")
    total_amount = serializers.IntegerField(label="已上传文件清单个数")
    project_used_amount = serializers.IntegerField(label="引用项目")

    create_time = serializers.DateTimeField(label="创建时间")
    update_time = serializers.DateTimeField(label="更新时间")
    create_user = serializers.CharField(label="创建人工号")
    create_name = serializers.CharField(label="创建人姓名")
    update_user = serializers.CharField(label="更新人工号")
    update_name = serializers.CharField(label="更新人姓名")

    # 增加额外的扩展字段
    status_text = serializers.SerializerMethodField(label="中心模板状态")
    @staticmethod
    def get_status_text(status_code):
        return status_code
# 中心模板操作和序列化
class HospDocTmplCenterSerializers(serializers.ModelSerializer):

    class Meta:
        model = models.HospDocTmplCenter
        fields = '__all__'
# 中心模板文件发布
class HospDocTmplCenterPublishRequestSerializers(serializers.Serializer):
    status_code = serializers.CharField(label="模板发布状态:UNRELEASED 未发布; RELEASED 已发布")

###################中心模板文件信息###################
# 中心模板文件列表序列化
class HospDocTmplInfoListSerializers(serializers.Serializer):
    # hospital 数据
    hosp_id = serializers.CharField(source='hospital.hosp_id',label="医院id(中心ID)")
    # hosp_doc_tmpl_info 数据
    id = serializers.IntegerField(label="模板文件信息ID")
    file_category = serializers.CharField(label="文件类别,暂时未启用")
    file_type = serializers.CharField(label="文件类型,暂时未启用")
    file_name = serializers.CharField(label="文件名称")
    file_extension = serializers.CharField(label="文件格式")

    status_code = serializers.CharField(label="系统模板状态 NOT_CREATED 未生成 CREATING 生成中 ,COMPLETED 已生成")
    audit_status_code = serializers.CharField(label="中心项目文件审核状态 UNAPPROVED 待审核，APPROVED 审核通过，REJECT 审核驳回")
    # tmpl_file_id = serializers.IntegerField(label="中心模板文件ID")
    # hosp_doc_tmpl_file 数据
    tmpl_file = HospDocTmplFileSerializer()
    # tmpl_file_processed_id = serializers.IntegerField(label="中心模板文件ID")
    # hosp_doc_tmpl_file_processed 数据
    tmpl_file_processed = HospDocTmplFileProcessedSerializer()

    create_time = serializers.DateTimeField(label="创建时间")
    update_time = serializers.DateTimeField(label="更新时间")
    create_user = serializers.CharField(label="创建人工号")
    create_name = serializers.CharField(label="创建人姓名")
    update_user = serializers.CharField(label="更新人工号")
    update_name = serializers.CharField(label="更新人姓名")

    # 增加额外的扩展字段
    status_text = serializers.SerializerMethodField(label="系统模板状态")
    audit_status_text = serializers.SerializerMethodField(label="审核状态")

    @staticmethod
    def get_status_text(status_code):
        return status_code
    @staticmethod
    def get_audit_status_text(audit_status_code):
        return audit_status_code

# 中心模板文件信息操作和序列化
class HospDocTmplInfoSerializers(serializers.ModelSerializer):

    class Meta:
        model = models.HospDocTmplInfo
        fields = '__all__'

# 文件重命名请求
class FileNameUpdateRequestSerializers(serializers.Serializer):
    file_name = serializers.CharField(label="文件名称")
# 中心模板文件审核
class HospDocTmplInfoAuditRequestSerializers(serializers.Serializer):
    audit_status_code = serializers.CharField(label="文件审核状态:UNAPPROVED 待审核；APPROVED 审核通过;REJECT 审核驳回(项目文件)")

# 中心模板文件hosp_id
class HospDocTmplInfoHospIdRequestSerializers(serializers.Serializer):
    hosp_id = serializers.CharField(label="医院id(中心id)")

# 中心模板文件hosp_id
class HospDocTmplInfoDownloadRequestSerializers(serializers.Serializer):
    hosp_id = serializers.CharField(label="医院id(中心id)")
    tmpl_type = serializers.CharField(label="模板类型:BLANK_TMPL 空白模板；AI_TMPL AI模板")

# 中心模板文件上传
class HospDocTmplInfoUploadRequestSerializers(serializers.ModelSerializer):
    hosp_id = serializers.CharField(label="医院id(中心id)")
    file = serializers.FileField(required=True)  # 文件上传字段

    class Meta:
        model = models.HospDocTmplInfo
        fields = ['hosp_id', 'file']

class HospDocTmplFileUpdateSerializer(FileUrlMixin, serializers.ModelSerializer):

    class Meta:
        model = models.HospDocTmplFile
        fields = ['original_filename','update_user','update_name']

class HospDocTmplFileProcessedUpdateSerializer(FileUrlMixin, serializers.ModelSerializer):
    class Meta:
        model = models.HospDocTmplFileProcessed
        fields = ['original_filename','update_user','update_name']

###################中心模板生成任务###################
class DocAutofillTaskSingleCreatRequestSerializer(serializers.ModelSerializer):
    tmpl_info_id = serializers.IntegerField(required=False,label="模板文件信息ID,生成系统模板时不能为空")
    doc_info_id = serializers.IntegerField(required=False,label="项目文件信息ID,生成项目文件时不能为空")
    category = serializers.CharField(label="文件类别：GEN_SYS_TMPL 生成系统模板；GEN_PROJECT_FILE 生成项目文件")
    class Meta:
        model = models.DocAutofillTask
        fields = ['tmpl_info_id','doc_info_id','category']

class DocAutofillTaskBatchCreatRequestSerializer(serializers.ModelSerializer):
    hosp_id = serializers.CharField(label="医院ID")
    project_id = serializers.CharField(required=False,label="项目ID 生成项目文件时不能为空")
    project_site_id = serializers.CharField(required=False,label="项目中心ID 生成项目文件时不能为空")
    category = serializers.CharField(label="文件类别：GEN_SYS_TMPL 生成系统模板；GEN_PROJECT_FILE 生成项目文件")
    class Meta:
        model = models.DocAutofillTask
        fields = ['hosp_id','project_id','project_site_id','category']

class DocAutofillTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.DocAutofillTask
        fields = '__all__'

###################项目中心列表###################
# 项目中心列表
class ProjectSiteDocCenterListSerializers(serializers.Serializer):
    # hospital 数据
    hosp_id = serializers.CharField(source='hospital.hosp_id', label="铨融医院ID")
    hosp_code = serializers.CharField(source='hospital.hosp_code', label="中心编码")
    hosp_name = serializers.CharField(source='hospital.hosp_name', label="中心名称")
    # project 数据
    project_id = serializers.CharField(source='project.project_id', label="项目ID")
    # project_site 数据
    project_site_id = serializers.CharField(source='project_site.project_site_id', label="项目中心ID")
    # project_site_doc_center 数据
    id = serializers.IntegerField(label="模板中心ID")
    status_code = serializers.CharField(label="项目文件状态 PROCESSING 进行中，COMPLETED 已完成")
    total_amount = serializers.IntegerField(label="项目文总数")
    complete_amount = serializers.IntegerField(label="项目文件完成数")

    create_time = serializers.DateTimeField(label="创建时间")
    update_time = serializers.DateTimeField(label="更新时间")
    create_user = serializers.CharField(label="创建人工号")
    create_name = serializers.CharField(label="创建人姓名")
    update_user = serializers.CharField(label="更新人工号")
    update_name = serializers.CharField(label="更新人姓名")

    # 增加额外的扩展字段
    status_text = serializers.SerializerMethodField(label="中心模板状态")

    @staticmethod
    def get_status_text(status_code):
        return status_code

# 项目中心文件列表序列化
class ProjectSiteDocInfoListSerializers(serializers.Serializer):
    # hospital 数据
    hosp_id = serializers.CharField(source='hospital.hosp_id',label="医院id(中心ID)")
    # project_site 数据
    project_site_id = serializers.CharField(source='project_site.project_site_id',label="项目中心ID")
    # hosp_doc_tmpl_info 数据
    id = serializers.IntegerField(label="模板文件信息ID")
    file_category = serializers.CharField(label="文件类别,暂时未启用")
    file_type = serializers.CharField(label="文件类型,暂时未启用")
    file_name = serializers.CharField(label="文件名称")
    file_extension = serializers.CharField(label="文件格式")
    auditor_id = serializers.CharField(label="审核人id")
    auditor_name = serializers.CharField(label="审核人姓名")
    status_code = serializers.CharField(label="系统模板状态 NOT_CREATED 未生成 CREATING 生成中 ,COMPLETED 已生成")
    audit_status_code = serializers.CharField(label="中心项目文件审核状态 UNAPPROVED 待审核，APPROVED 审核通过，REJECT 审核驳回")
    # project_site_doc_file 数据
    doc_file = ProjectSiteDocFileSerializer()

    create_time = serializers.DateTimeField(label="创建时间")
    update_time = serializers.DateTimeField(label="更新时间")
    create_user = serializers.CharField(label="创建人工号")
    create_name = serializers.CharField(label="创建人姓名")
    update_user = serializers.CharField(label="更新人工号")
    update_name = serializers.CharField(label="更新人姓名")

    # 增加额外的扩展字段
    status_text = serializers.SerializerMethodField(label="系统模板状态")
    audit_status_text = serializers.SerializerMethodField(label="审核状态")

    @staticmethod
    def get_status_text(status_code):
        return status_code
    @staticmethod
    def get_audit_status_text(audit_status_code):
        return audit_status_code
# 项目中心文件信息操作和序列化
class ProjectSiteDocInfoDownloadRequestSerializers(serializers.Serializer):
    hosp_id = serializers.CharField(label="医院ID")
    project_id = serializers.CharField(label="项目ID")
    project_site_id = serializers.CharField(label="项目中心ID")

class ProjectSiteDocInfoSerializers(serializers.ModelSerializer):

    class Meta:
        model = models.ProjectSiteDocInfo
        fields = '__all__'

# 中心模板文件上传
class OnlyOfficeConfigsRequestSerializers(serializers.Serializer):
    biz_type = serializers.ChoiceField(choices=models.FileBizTypeEnum.choices, label="业务类型：SITE_SYS_TMPL 中心系统模板，SITE_PROJECT_FILE 中心项目文件")
    file_id = serializers.IntegerField(label="文件id")

