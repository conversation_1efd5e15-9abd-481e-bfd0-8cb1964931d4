#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的分层匹配策略测试
不依赖Django环境，直接测试时间修复逻辑
"""

import re
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class TimeUtils:
    """独立的时间处理工具类"""

    @staticmethod
    def is_valid_django_datetime(time_str):
        """验证时间字符串是否符合Django DateTimeField要求"""
        if not time_str or not isinstance(time_str, str):
            return False
        
        try:
            from datetime import datetime
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M',
                '%Y-%m-%d',
            ]
            
            for fmt in formats:
                try:
                    datetime.strptime(time_str, fmt)
                    return True
                except ValueError:
                    continue
            return False
        except Exception:
            return False

    @staticmethod
    def attempt_time_fix(time_str):
        """分层匹配策略修复时间格式错误"""
        import re

        if not time_str or not time_str.strip():
            return None

        time_str = time_str.strip()

        # 首先检查是否已经是有效的Django时间格式
        if TimeUtils.is_valid_django_datetime(time_str):
            return time_str

        # 定义分层匹配模式：按优先级从高到低排列
        time_patterns = [
            # 模式1: 日期末位与时间首位连接 (如: 2024-06-278:00:15)
            {
                'name': '日期末位与时间首位连接(带秒)',
                'pattern': r'^(\d{4}-\d{2}-\d)(\d:\d{1,2}:\d{1,2})$',
                'handler': TimeUtils._fix_date_digit_time_connection_with_seconds
            },
            {
                'name': '日期末位与时间首位连接(无秒)',
                'pattern': r'^(\d{4}-\d{2}-\d)(\d:\d{1,2})$',
                'handler': TimeUtils._fix_date_digit_time_connection
            },
            
            # 模式2: 完整日期与时间直接连接 (如: 2024-07-0213:42)
            {
                'name': '完整日期与时间直接连接(带秒)',
                'pattern': r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{2}:\d{2})$',
                'handler': TimeUtils._fix_full_date_time_connection_with_seconds
            },
            {
                'name': '完整日期与时间直接连接(无秒)',
                'pattern': r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{2})$',
                'handler': TimeUtils._fix_full_date_time_connection
            },
            
            # 模式3: 通用无空格格式 (如: 2024-06-2710:05:30)
            {
                'name': '通用无空格格式(带秒)',
                'pattern': r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2}:\d{1,2})$',
                'handler': TimeUtils._fix_general_no_space_with_seconds
            },
            {
                'name': '通用无空格格式(无秒)',
                'pattern': r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2})$',
                'handler': TimeUtils._fix_general_no_space
            },
            
            # 模式4: 中文时间格式
            {
                'name': '中文时间格式(完整)',
                'pattern': r'(\d{4})[年](\d{1,2})[月](\d{1,2})[日](\d{1,2})[时](\d{1,2})[分](\d{1,2})?[秒]?',
                'handler': TimeUtils._fix_chinese_time_format
            },
            {
                'name': '中文时间格式(无秒)',
                'pattern': r'(\d{4})[年](\d{1,2})[月](\d{1,2})[日](\d{1,2})[时](\d{1,2})[分]',
                'handler': TimeUtils._fix_chinese_time_format_simple
            },
            
            # 模式5: 中文冒号格式
            {
                'name': '中文冒号格式',
                'pattern': r'^(.+)[：](.+)$',
                'handler': TimeUtils._fix_chinese_colon_format
            }
        ]

        # 分层匹配：按优先级尝试每个模式
        for pattern_info in time_patterns:
            match = re.match(pattern_info['pattern'], time_str)
            if match:
                try:
                    fixed_time = pattern_info['handler'](match, time_str)
                    if fixed_time and TimeUtils.is_valid_django_datetime(fixed_time):
                        logger.debug(f"时间修复成功 [{pattern_info['name']}]: '{time_str}' -> '{fixed_time}'")
                        return fixed_time
                except Exception as e:
                    logger.warning(f"时间修复处理器异常 [{pattern_info['name']}]: {e}")
                    continue

        # 所有模式都无法修复，记录警告并返回None
        logger.warning(f"无法修复时间格式: '{time_str}'，将设置为None")
        return None

    @staticmethod
    def _fix_date_digit_time_connection_with_seconds(match, original_str):
        """处理日期末位与时间首位连接的格式(带秒): 2024-06-278:00:15"""
        date_prefix, time_part = match.groups()
        # 重新组合：将日期末位和时间首位分开
        date_part = date_prefix + time_part[0]  # 2024-06-27
        time_only = time_part[1:]  # 8:00:15
        # 标准化时间部分
        time_components = time_only.split(':')
        normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
        return f"{date_part} {normalized_time}"

    @staticmethod
    def _fix_date_digit_time_connection(match, original_str):
        """处理日期末位与时间首位连接的格式(无秒): 2024-06-279:30"""
        date_prefix, time_part = match.groups()
        # 重新组合：将日期末位和时间首位分开
        date_part = date_prefix + time_part[0]  # 2024-06-27
        time_only = time_part[1:]  # 9:30
        # 标准化时间部分
        time_components = time_only.split(':')
        normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
        return f"{date_part} {normalized_time}:00"  # 添加秒

    @staticmethod
    def _fix_full_date_time_connection_with_seconds(match, original_str):
        """处理完整日期与时间直接连接的格式(带秒): 2024-07-0213:42:15"""
        date_part, time_part = match.groups()
        # 标准化时间部分，确保小时是两位数
        time_components = time_part.split(':')
        normalized_time = ':'.join([time_components[0].zfill(2), time_components[1], time_components[2]])
        return f"{date_part} {normalized_time}"

    @staticmethod
    def _fix_full_date_time_connection(match, original_str):
        """处理完整日期与时间直接连接的格式(无秒): 2024-07-0213:42"""
        date_part, time_part = match.groups()
        # 标准化时间部分，确保小时是两位数
        time_components = time_part.split(':')
        normalized_time = ':'.join([time_components[0].zfill(2), time_components[1]])
        return f"{date_part} {normalized_time}:00"  # 添加秒

    @staticmethod
    def _fix_general_no_space_with_seconds(match, original_str):
        """处理通用无空格格式(带秒): 2024-06-2710:05:30"""
        date_part, time_part = match.groups()
        # 标准化时间部分，确保都是两位数
        time_components = time_part.split(':')
        normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
        return f"{date_part} {normalized_time}"

    @staticmethod
    def _fix_general_no_space(match, original_str):
        """处理通用无空格格式(无秒): 2024-06-2710:05"""
        date_part, time_part = match.groups()
        # 标准化时间部分，确保都是两位数
        time_components = time_part.split(':')
        normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
        return f"{date_part} {normalized_time}:00"  # 添加秒

    @staticmethod
    def _fix_chinese_time_format(match, original_str):
        """处理中文时间格式(完整): 2024年06月27日08时00分15秒"""
        groups = match.groups()
        year, month, day, hour, minute = groups[:5]
        second = groups[5] if len(groups) > 5 and groups[5] else "00"
        
        # 标准化所有组件为两位数
        month = month.zfill(2)
        day = day.zfill(2)
        hour = hour.zfill(2)
        minute = minute.zfill(2)
        second = second.zfill(2)
        
        return f"{year}-{month}-{day} {hour}:{minute}:{second}"

    @staticmethod
    def _fix_chinese_time_format_simple(match, original_str):
        """处理中文时间格式(无秒): 2024年06月27日08时00分"""
        year, month, day, hour, minute = match.groups()
        
        # 标准化所有组件为两位数
        month = month.zfill(2)
        day = day.zfill(2)
        hour = hour.zfill(2)
        minute = minute.zfill(2)
        
        return f"{year}-{month}-{day} {hour}:{minute}:00"

    @staticmethod
    def _fix_chinese_colon_format(match, original_str):
        """处理中文冒号格式: 将中文冒号替换为英文冒号后递归处理"""
        import re
        # 替换中文冒号为英文冒号
        cleaned = re.sub(r'[：]', ':', original_str)
        # 递归调用主修复方法
        if cleaned != original_str:
            return TimeUtils.attempt_time_fix(cleaned)
        return None

def test_layered_matching():
    """测试分层匹配策略"""
    print("=" * 80)
    print("分层匹配策略时间修复测试")
    print("=" * 80)
    
    # 重点测试用例
    test_cases = [
        # 日期末位与时间首位连接
        ("2024-06-278:00:15", "2024-06-27 08:00:15", "日期末位连接(带秒)"),
        ("2024-06-279:30", "2024-06-27 09:30:00", "日期末位连接(无秒)"),
        
        # 完整日期与时间直接连接
        ("2024-07-0213:42:15", "2024-07-02 13:42:15", "完整日期连接(带秒)"),
        ("2024-07-0213:42", "2024-07-02 13:42:00", "完整日期连接(无秒)"),
        ("2024-07-029:30", "2024-07-02 09:30:00", "完整日期连接(单位数小时)"),
        
        # 通用无空格格式
        ("2024-06-2710:05:30", "2024-06-27 10:05:30", "通用格式(带秒)"),
        ("2024-06-279:5:5", "2024-06-27 09:05:05", "通用格式(全单位数)"),
        
        # 中文格式
        ("2024年06月27日08时00分15秒", "2024-06-27 08:00:15", "中文格式(完整)"),
        ("2024年6月27日8时0分", "2024-06-27 08:00:00", "中文格式(单位数)"),
        
        # 中文冒号
        ("2024-07-02：13：42", "2024-07-02 13:42:00", "中文冒号"),
        
        # 已正确格式
        ("2024-07-02 13:42:15", "2024-07-02 13:42:15", "已正确格式"),
        
        # 无效格式
        ("invalid-time", None, "无效格式"),
    ]
    
    print("🔧 测试分层匹配策略:")
    print("-" * 60)
    
    success_count = 0
    for i, (input_time, expected, description) in enumerate(test_cases, 1):
        print(f"\n{i:2d}. {description}")
        print(f"    输入: {input_time}")
        print(f"    期望: {expected}")
        
        result = TimeUtils.attempt_time_fix(input_time)
        print(f"    输出: {result}")
        
        if result == expected:
            success_count += 1
            print(f"    状态: ✅ 成功")
        else:
            print(f"    状态: ❌ 失败")
    
    success_rate = (success_count / len(test_cases)) * 100
    print(f"\n总体成功率: {success_count}/{len(test_cases)} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 分层匹配策略测试通过！")
        return True
    else:
        print("⚠️  分层匹配策略需要优化")
        return False

def main():
    """主测试函数"""
    print("🚀 开始独立分层匹配策略测试\n")
    
    success = test_layered_matching()
    
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    if success:
        print("🎉 分层匹配策略实现成功！")
        print("💡 关键特性:")
        print("  - 每种时间格式错误都有专门的处理方案")
        print("  - 按优先级从具体到通用的匹配顺序")
        print("  - 早期退出机制提高性能")
        print("  - 代码结构清晰，易于维护和扩展")
        return True
    else:
        print("⚠️  分层匹配策略需要进一步优化")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
