from django.urls import path
from rest_framework.routers import DefaultRouter

from . import views

router = DefaultRouter(trailing_slash=False)

router.register(r'/hosp-doc-tmpl-centers', views.HospDocTmplCenterListViewSet)
router.register(r'/hosp-doc-tmpl-centers', views.HospDocTmplCenterDetailViewSet)
router.register(r'/hosp-doc-tmpl-infos', views.HospDocTmplInfoListViewSet)
router.register(r'/hosp-doc-tmpl-infos', views.HospDocTmplInfoDetailViewSet)
router.register(r'/hosp-doc-tmpl-infos/files', views.HospDocTmplInfoUploadViewSet) # 带文件上传组件，会影响参数格式
router.register(r'/project-site-doc-centers', views.ProjectSiteDocCenterListViewSet)
router.register(r'/project-site-doc-infos', views.ProjectSiteDocInfoListViewSet)
router.register(r'/project-site-doc-infos', views.ProjectSiteDocInfoDetailViewSet)
router.register(r'/doc-autofill-tasks', views.DocAutofillTasksView)






urlpatterns = [
    path('/only-office/config-infos', views.OnlyOfficeView.as_view()),
    path('/only-office/edit/callback/<str:biz_type>/<str:file_id>', views.OnlyOfficeCallbackView.as_view()),

]

urlpatterns = router.urls + urlpatterns
