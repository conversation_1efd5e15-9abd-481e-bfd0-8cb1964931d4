#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
携带日志参数的大模型请求最小样例
演示如何使用task_info传递参数的方式调用LLM并自动记录日志
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.clients.llm_client import deepseek_r1_distill_qwen_32b, qwen3_32b


def example_with_full_logging():
    """示例1：完整的日志参数，会自动记录到数据库"""
    print("=" * 60)
    print("示例1：完整的日志参数，会自动记录到数据库")
    print("=" * 60)
    
    # 准备任务信息
    task_info = {
        'task_id': 12345,
        'create_user': 'test_user',
        'create_name': '测试用户',
        'business_id': 'biz_001'
    }
    
    # 调用DeepSeek模型
    try:
        response = deepseek_r1_distill_qwen_32b(
            "请简单介绍一下人工智能的发展历程",
            timeout=30,
            temperature=0.7,
            max_tokens=200,
            category='AI_INTRODUCTION',  # 业务类别
            **task_info  # 🎯 直接展开字典参数，避免层层传递
        )
        print(f"DeepSeek响应: {response[:100]}...")
        print(f"响应长度: {len(response)} 字符")
        
    except Exception as e:
        print(f"DeepSeek调用失败: {e}")
    
    print("\n" + "-" * 40 + "\n")
    
    # 调用Qwen模型
    try:
        response = qwen3_32b(
            "什么是机器学习？",
            timeout=30,
            temperature=0.5,
            max_tokens=150,
            category='ML_EXPLANATION',  # 业务类别
            **task_info  # 🎯 直接展开字典参数
        )
        print(f"Qwen响应: {response[:100]}...")
        print(f"响应长度: {len(response)} 字符")
        
    except Exception as e:
        print(f"Qwen调用失败: {e}")


def example_without_logging():
    """示例2：不完整的日志参数，会跳过日志记录"""
    print("=" * 60)
    print("示例2：不完整的日志参数，会跳过日志记录")
    print("=" * 60)
    
    # 不完整的任务信息（缺少必要字段）
    incomplete_task_info = {
        'task_id': 12346,
        # 缺少 create_user, create_name, business_id
    }
    
    try:
        response = deepseek_r1_distill_qwen_32b(
            "请解释什么是深度学习",
            timeout=30,
            temperature=0.6,
            max_tokens=100,
            category='DL_EXPLANATION',
            **incomplete_task_info  # 🎯 不完整的参数，装饰器会自动跳过日志记录
        )
        print(f"DeepSeek响应: {response[:100]}...")
        print(f"响应长度: {len(response)} 字符")
        print("注意：由于日志参数不完整，此次调用不会记录到数据库")
        
    except Exception as e:
        print(f"DeepSeek调用失败: {e}")


def example_explicit_disable_logging():
    """示例3：显式禁用日志记录"""
    print("=" * 60)
    print("示例3：显式禁用日志记录")
    print("=" * 60)
    
    try:
        response = qwen3_32b(
            "请介绍一下自然语言处理",
            timeout=30,
            temperature=0.8,
            max_tokens=120,
            enable_logging=False  # 🎯 显式禁用日志记录
        )
        print(f"Qwen响应: {response[:100]}...")
        print(f"响应长度: {len(response)} 字符")
        print("注意：显式禁用了日志记录，此次调用不会记录到数据库")
        
    except Exception as e:
        print(f"Qwen调用失败: {e}")
