# Generated by Django 4.1.5 on 2025-04-27 11:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("doc_autofill", "0005_alter_docautofilltask_start_time"),
    ]

    operations = [
        migrations.AddField(
            model_name="docautofilltask",
            name="hosp_doc_tmpl",
            field=models.ForeignKey(
                db_column="tmpl_info_id",
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_autofill_tasks",
                to="doc_autofill.hospdoctmplinfo",
                verbose_name="中心模板文件信息ID",
            ),
        ),
        migrations.AddField(
            model_name="docautofilltask",
            name="project_site_doc",
            field=models.ForeignKey(
                db_column="doc_info_id",
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="doc_autofill_tasks",
                to="doc_autofill.projectsitedocinfo",
                verbose_name="项目中心文件信息ID",
            ),
        ),
        migrations.AlterField(
            model_name="projectsitedoccenter",
            name="status_code",
            field=models.CharField(
                default="",
                max_length=20,
                verbose_name="项目文件状态 PROCESSING 进行中，COMPLETED 已完成",
            ),
        ),
    ]
