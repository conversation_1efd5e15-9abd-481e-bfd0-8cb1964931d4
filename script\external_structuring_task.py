import os
import django
from django.db import connections
from django.conf import settings

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

django.setup()

import re
import base64
import json
import retry
import requests
import ast
import io
import fitz
from sqlalchemy import text
import hashlib
import argparse
from datetime import datetime, timedelta

from sqlalchemy import create_engine
from sqlalchemy.pool import NullPool
from django.db import transaction

from apps.medical.models import MedicalFileMasked
from apps.subject_medical.models import SubjectMedicalFileMasked, SubjectMedicalInfo
from apps.external.models import MaskingMaskedFile
from apps.ae_tracker.models import AeTrackerTask
from apps.external.models import MaskingTask
from common.tools import get_db_engin_url
from common.tools import sql_to_df, activate_conn
from common.ocr_mask.main import ocr_desensitive
from common.minio_client import get_minio_client
from script.test_result_format import request_llm_api1
from common.llm_tools import deepseek_r1_qwen_32b, qwen3_32b


def transform_test_results(original_data):
    """
    将原始数据转换为目标结构
    :param original_data: 包含test_results的原始字典
    :return: 转换后的列表
    """
    transformed = []
    # 遍历test_results中的每个对象
    for item in original_data.get('test_results', []):
        # 构建目标结构的单个对象
        transformed_item = {
            "项目": {
                "value": item.get('test_name', ''),  # 映射test_name到项目value
                "index": 0
            },
            "结果": {
                "value": item.get('test_value', ''),  # 映射test_value到结果value
                "index": 1
            },
            "单位": {
                "value": item.get('test_unit', ''),  # 映射test_unit到单位value
                "index": 2
            },
            # 映射reference_value到参考区间value，处理可能的字段名差异
            "参考区间": {
                "value": item.get('reference_value', item.get('参考值', '')),
                "index": 3
            }
        }
        transformed.append(transformed_item)
    return transformed


def find_all_indices(s, target_char):
    """
    找到所有字符串中指定字符的下标
    s：输入字符串
    target_char：要查找的字符
    return：所有下标的列表
    """
    if len(target_char) != 1:
        raise ValueError("target_char 必须是单个字符")
    return [i for i, char in enumerate(s) if char == target_char]


def mask_image_by_location(draw, image_path, ocrResult, keywords):
    """
    根据给定的位置信息对图像中的特定字符进行打码。

    :param image_path: 原始图像路径
    :param ocr_result: OCR识别结果
    :param keywords: 需要打码的关键字列表
    """

    # 提取位置信息
    for block in ast.literal_eval(ocrResult)["result"]["words_block_list"]:
        words = block["words"]
        locations = block["location"]
        if not locations:
            continue
        num_chars = len(words)

        # 计算每个字符的起始和结束位置
        x_coords = [point[0] for point in locations]
        y_coords = [point[1] for point in locations]

        left = min(x_coords)
        right = max(x_coords)
        top = min(y_coords)
        bottom = max(y_coords)

        # 使用线性插值计算每个字符的边界
        char_width = (right - left) / num_chars

        if keywords in words:
            previous_index = -1
            for keyword in keywords:
                indices = find_all_indices(words, keyword)
                if len(indices) == 1:
                    start_index = words.find(keyword)
                    if start_index != -1:
                        end_index = start_index + len(keyword)

                        for i in range(start_index, end_index):
                            if previous_index == -1 or i > previous_index:
                                char_left = left + i * char_width
                                char_right = left + (i + 1) * char_width

                                top_y, bottom_y = linear_interpolate(
                                    char_left, char_right, top, bottom, x_coords, y_coords)

                                draw.rectangle([char_left, top_y, char_right, bottom_y], fill='black')

                                previous_index = start_index
                else:
                    start_index = previous_index + 1
                    if start_index != -1:
                        end_index = start_index + len(keyword)

                        for i in range(start_index, end_index):
                            if previous_index == -1 or i > previous_index:
                                char_left = left + i * char_width
                                char_right = left + (i + 1) * char_width

                                top_y, bottom_y = linear_interpolate(
                                    char_left, char_right, top, bottom, x_coords, y_coords)

                                draw.rectangle([char_left, top_y, char_right, bottom_y], fill='black')

                                previous_index = start_index


def linear_interpolate(x_start, x_end, y_top, y_bottom, x_coords, y_coords):
    """
    使用线性插值计算顶部和底部的y坐标。

    :param x_start: 字符的左边界
    :param x_end: 字符的右边界
    :param y_top: 多边形的顶部y坐标
    :param y_bottom: 多边形的底部y坐标
    :param x_coords: 多边形的x坐标列表
    :param y_coords: 多边形的y坐标列表
    :return: 插值后的y坐标 (top_y, bottom_y)
    """
    n = len(x_coords)
    if n < 2:
        return y_top, y_bottom

    def interpolate(x, x_coords, y_coords):
        for i in range(n - 1):
            if x_coords[i] <= x <= x_coords[i + 1]:
                t = (x - x_coords[i]) / (x_coords[i + 1] - x_coords[i])
                return y_coords[i] + t * (y_coords[(i + 1) % n] - y_coords[i])
        # 如果x不在任何区间内，使用最近的点
        return y_coords[-1]

    # 计算顶部的y坐标
    top_y_start = interpolate(x_start, x_coords, y_coords)
    top_y_end = interpolate(x_end, x_coords, y_coords)
    top_y = (top_y_start + top_y_end) / 2

    # 计算底部的y坐标
    bottom_y_start = interpolate(x_start, x_coords, [y_bottom] * n)
    bottom_y_end = interpolate(x_end, x_coords, [y_bottom] * n)
    bottom_y = (bottom_y_start + bottom_y_end) / 2

    return top_y, bottom_y


def match_chinese_string(text):
    # 匹配姓名后面的名字
    # name_pattern = r"姓名：(\w+)"
    # 匹配年龄后面的数字
    age_pattern = r"(\d+) 岁"

    # 查找所有匹配项
    # name_match = re.search(name_pattern, text)
    age_match = re.search(age_pattern, text)

    # if name_match and age_match:
    if age_match:
        # name = name_match.group(1)
        age = age_match.group(1)
        # return {"name": name, "age": age}
        return age
    else:
        return None


# @retry.retry(tries=3, delay=15)
def task_callback(url, success, test_result):
    token = settings.OT_CALLBACK_API_SECRET_KEY
    # 请求头
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print(f"headers: {headers}")
    # 请求体
    data = {
        "success": success,  # 或 "-1" 取决于是否识别成功
        "test_result": test_result
    }
    print(f"data: {data}")

    # 发送请求
    response = requests.post(url, headers=headers, json=data)

    # 打印响应结果
    print(response.status_code)
    print(response.text)
    # response.raise_for_status()
    return response


def process_task(task_id):
    task = MaskingTask.objects.filter(id=task_id, delete_flag=0).first()
    if not task:
        print(f"没有发现任务")
        return

    with transaction.atomic():
        task.status = 'IN_PROGRESS'
        task.save()

    print(f"task_id: {task.task_id}")

    raw_file = task.original_file
    minio_client = get_minio_client()
    preprocess_start_time = datetime.now()
    SUCCESS = -1

    try:
        bucket_name = raw_file.bucket_name
        object_name = raw_file.object_name
        original_filename = raw_file.original_filename
        # 从 MinIO 中获取对象
        response = minio_client.get_object(bucket_name, object_name)
        # 读取对象内容到内存中
        data = response.read()

        original_file_stream = io.BytesIO(data)
        if original_filename[-3:].lower() == 'pdf':
            print('pdf!!!')
            # 将数据加载到 PDF 文档对象中
            pdf_doc = fitz.open(stream=io.BytesIO(data), filetype="pdf")
            # 遍历 PDF 的每一页
            image_list = []
            matrix = fitz.Matrix(2, 2)
            title_text = "[{}]".format(original_filename)
            ocr_text_list = []
            page_count = pdf_doc.page_count
            list_ocr_box = []
            for page_num in range(pdf_doc.page_count):
                page_text = "[Page {}]".format(page_num + 1)
                page = pdf_doc.load_page(page_num)
                # 将页面转换为图像
                pix = page.get_pixmap(matrix=matrix)
                # 创建图像字节流
                img_bytes = pix.tobytes()

                try:
                    # 调用的ocr_desensitive函数，返回3元组，只需要image
                    image, input_text, masked_text, ocr_box = ocr_desensitive(img_bytes, project_no=None)
                    ocr_text = input_text  # 原始OCR文本
                    ocr_text_list_temp = os.linesep.join([title_text, page_text, ocr_text])
                    ocr_text_list.append(ocr_text_list_temp)
                    image_list.append(image)
                    list_ocr_box.append(ocr_box)
                except:
                    pass
            formatted_list = [f"{text1}{os.linesep}{os.linesep}" for text1 in ocr_text_list]
            combined_text = "".join(formatted_list).rstrip(os.linesep)
            file_bytes = io.BytesIO()

            if image_list:
                # 取列表中的第一个 Image 对象作为基础
                first_image = image_list[0]
                # 使用 save 方法将 PDF 内容保存到 BytesIO 对象中
                first_image.save(file_bytes, save_all=True, append_images=image_list[1:], format='PDF')
                # 将文件指针移动到流的开头，以便后续读取
                file_bytes.seek(0)

                print('PDF 文件已成功保存到流中。')
            else:
                print('没有可用的图片，无法创建 PDF 文件。')
            pdf_doc.close()
        elif original_filename.lower().endswith('.jpg') or original_filename.lower().endswith('.jpeg') or original_filename.lower().endswith('.png') or original_filename.lower().endswith('.webp'):
            print('图片！！！')
            file_bytesX = data
            page_count = 1
            list_ocr_box = []
            ocr_text_list = []
            ocr_text_list_mask = []
            title_text = "[{}]".format(original_filename)
            # 调用的ocr_desensitive函数，返回3元组，只需要image
            image, input_text, masked_text, ocr_box = ocr_desensitive(file_bytesX, project_no=None)
            list_ocr_box.append(ocr_box)
            print(input_text, 111111111111111111111111111111)
            # 为了保持后续代码兼容，将返回值映射到原有变量名
            ocr_text = input_text  # 原始OCR文本
            ocr_text_list_temp = os.linesep.join([title_text, ocr_text])
            ocr_text_list.append(ocr_text_list_temp)
            formatted_list = [f"{text1}{os.linesep}{os.linesep}" for text1 in ocr_text_list]
            combined_text = "".join(formatted_list).rstrip(os.linesep)
            # file_bytes = io.BytesIO(file_bytes)
            #
            format_mapping = {
                'jpg': 'JPEG',
                'jpeg': 'JPEG',
                'png': 'PNG',

            }
            # # 获取对应的格式
            image_format = format_mapping.get(original_filename.split('.')[1].lower(), 'PNG')
            #
            if image.mode == 'RGBA':
                image = image.convert('RGB')
            file_bytes = io.BytesIO()
            # 将 Image 对象保存到 BytesIO 对象中，这里以 PNG 格式为例，可根据实际需求修改
            image.save(file_bytes, format=image_format)
            file_bytes.seek(0)
            # # 关闭 BytesIO 对象

        else:
            # print(file)
            print('文件类型不属于处理范畴！！！')

        class FileStreamWithName:
            def __init__(self, stream, filename):
                self.stream = stream
                self.filename = filename
                self.size = len(stream.getvalue())
                self.content_type = raw_file.content_type

            @property
            def name(self):
                return self.filename
        # 创建包含文件流和文件名的对象
        file = FileStreamWithName(file_bytes, original_filename)
        import uuid
        _, ext = os.path.splitext(file.name)
        object_name = f"{uuid.uuid4().hex}{ext}"
        # 保存文件
        try:
            minio_client = get_minio_client()
            # Ensure bucket exists
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)
            # Upload file to MinIO
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file_bytes,  # 直接使用 output 作为 data 参数
                length=file.size,
                part_size=1024 * 1024 * 5,
                content_type=raw_file.content_type
            )
        except Exception as e:
            # logger = logging.getLogger(__name__)
            # logger.error(e)
            raise Exception(f"文件上传失败：{e}")

        # 数据入库
        output = file_bytes

        activate_conn(connections['default'])
        with transaction.atomic():
            # 计算 hash
            hash_object = hashlib.sha256(output.getvalue())
            hash = hash_object.hexdigest()
            now = datetime.now()
            formatted_time = now.strftime("%Y%m%d%H%M%S")
            masked_file_data = {
                'original_filename': file.name,
                'bucket_name': bucket_name,
                # 'version': 'v' + str(formatted_time),
                'object_name': object_name,
                'content_type': raw_file.content_type,
                'size': file.size,
                'hash': hash,

            }
            masked_file = MaskingMaskedFile.objects.create(**masked_file_data)

            task.masked_file = masked_file
            task.end_time = datetime.now()
            task.status = MaskingTask.COMPLETED
            task.save()

        print(task_id)
        print(combined_text)
        SUCCESS = 1
        file_stream = file_bytes
        file_name = file.name
        file_type = raw_file.content_type

    except Exception as e:
        print(e)
        print('文件处理错误！！！')
        import traceback
        traceback.print_exc()

        activate_conn(connections['default'])
        with transaction.atomic():
            task.status = 'ERROR'
            task.save()

        SUCCESS = -1
        file_stream = original_file_stream
        file_name = original_filename
        file_type = raw_file.content_type
    preprocess_end_time = datetime.now()
    duration = preprocess_end_time - preprocess_start_time
    preprocess_duration = duration.total_seconds()
    try:
        task_type = task.meta.get('type')
        if task_type == '102':
            activate_conn(connections['default'])
            with transaction.atomic():
                subject_medical_info_id = task.extra.get('subject_medical_info_id')
                if subject_medical_info_id:
                    subject_medical_file = SubjectMedicalFileMasked.objects.create(**masked_file_data)
                    medical_info = SubjectMedicalInfo.objects.filter(
                        id=subject_medical_info_id).update(file_masked_id=subject_medical_file.id,
                                                           update_time=datetime.now(), ocr_status='COMPLETED',
                                                           mask_status='COMPLETED', page_count=page_count,
                                                           ocr_text=input_text, ocr_text_mask=masked_text,
                                                           ocr_box=list_ocr_box, preprocess_duration=preprocess_duration,
                                                           preprocess_end_time=preprocess_end_time,
                                                           preprocess_start_time=preprocess_start_time)
    except Exception as e:
        print(e)
        print('VISIT处理错误！！！')

    try:
        print(222222222222222222222222222)
        extra = {}
        # extra["code"] = "200"
        extra["message"] = "操作成功"
        # extra["result"] = {}
        extra["extractionNo"] = task.callback_url
        pattern = r'/(\d+)$'
        # 提取结果
        match = re.search(pattern, extra["extractionNo"])
        if match:
            extra["extractionNo"] = match.group(1)
        extra["fieldKey"] = task.meta.get('report_field_config', {}).get('contentField')
        extra["keyValue"] = {}
        extra["keyValue"]["tableRow"] = []

        print("🚀 开始调用【qwen3_32b】大模型转换 OCR 结果为 JSON...")
        input_list = task.meta.get('report_field_config', {}).get('contentField')
        output_json = {
        "姓名": {
        "value": "张三"
        },
        "性别": {
        "value": "男"
        },
        "红细胞": {
        "value": "60"
        },
        "血糖": {
        "value": "90"
        }
        }
        content = f"""
        输入文本:{combined_text}
        请根据输入的动态列表从文本里提取信息：
        例子：
        输入列表为 ["姓名", "性别", "红细胞", "血糖"] 时，输出格式应为：
        {output_json}
        输入列表为{input_list}
        """
        response = qwen3_32b(content)
        result = response.json()
        content = result.get("choices", [{}])[0].get("message", {}).get("content", "{}")

        def clean_llm_response(content):
            """清理大模型返回的内容"""
            # 使用正则去除 <think> 标签及其内容
            return re.sub(r"<think>.*?</think>", "", content, flags=re.DOTALL).strip()

        cleaned_content = clean_llm_response(content)

        print(f"📄 处理后的 JSON OCR 结果:\n{cleaned_content}")
        # 先尝试直接解析
        try:
            cleaned_content = json.loads(cleaned_content)
        except json.JSONDecodeError:
            # 如果失败，尝试修复单引号问题
            # 将单引号替换为双引号（但要小心不要替换字符串内部的单引号）
            try:
                # 使用 ast.literal_eval 安全地解析 Python 字典字符串
                import ast
                cleaned_content = ast.literal_eval(cleaned_content)
            except:
                # 如果还是失败，尝试正则替换
                # 注意：这是一个简化的方法，可能不适用于所有情况
                cleaned_content = cleaned_content.replace("'", '"')
                cleaned_content = json.loads(cleaned_content)

        # 然后继续处理
        for key, value in cleaned_content.items():
            if key in extra["fieldKey"]:
                index = extra["fieldKey"].index(key)
                value["index"] = index

        # 然后将 cleaned_content 添加到 extra 中
        extra["keyValue"].update(cleaned_content)

        if task.meta.get('report_field_config', {}).get('contentField') and task.meta.get('report_field_config', {}).get('tableField'):  # 检验单
            print(combined_text)
            ocr_json = request_llm_api1(combined_text)
            result = transform_test_results(ocr_json)
            extra["keyValue"]["tableRow"] = result
        print(extra)
        print(json.dumps(extra, ensure_ascii=False))
        json_string = json.dumps(extra, ensure_ascii=False, separators=(',', ':'))
        print(json_string)
        if task.meta.get('report_field_config', {}).get('contentField') and not task.meta.get('report_field_config', {}).get('tableField'):  # 检查单
            del extra["keyValue"]["tableRow"]
        print(extra)
        task.test_result = extra
        task.save()
    except Exception as e:
        print(e)
        print("结构化提取错误！！！")



    # 通知回调
    try:
        file_stream.seek(0)
        callback_url = task.callback_url + "?success=2"
        print(f"task: {task.task_id}, callback_url: {callback_url}")
        if callback_url:
            files = {
                "file": (file_name, file_stream, file_type)
            }
            response = task_callback(callback_url, SUCCESS, extra)
            print(f"callback: {response}")

            activate_conn(connections['default'])
            with transaction.atomic():
                task.callback_time = datetime.now()
                task.callback_status = response.status_code
                task.save()

    except Exception as e:
        print(f"回调错误：{e}")

    return True


def main():
    parser = argparse.ArgumentParser(description="OT打码任务")
    parser.add_argument('--task_id', type=int, required=True, help="task_id")
    args = parser.parse_args()
    process_task(args.task_id)


if __name__ == "__main__":
    # python -m script.external_masking_task --task_id=
    main()
