"""
Django settings for smo project.

Generated by 'django-admin startproject' using Django 4.1.5.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

import os
os.environ['OPENBLAS_NUM_THREADS'] = '2'

import socket
import pprint
from pathlib import Path

import pymysql
pymysql.install_as_MySQLdb()

from common import tools

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'ay!&f-%vizmxtrlv14k%zhhxy3)nf(76hr_n52yzm@rkz&79d9'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'corsheaders',
    'rest_framework',
    'django_extensions',
    'django_filters',
    'drf_spectacular',
    'drf_spectacular_sidecar',
    'django_comment_migrate',
    'apps.users',
    'apps.project',
    'apps.subject',
    'apps.patient',
    'apps.medical',
    'apps.medical_collection',
    'apps.system',
    'apps.ae_tracker',
    'apps.subject_medical',
    'apps.doc_autofill',
    'apps.hospital',
    'apps.ruixing_chat',
    'apps.external',
]

DCM_COMMENT_APP = [
    'users',
    'project',
    'subject',
    'patient',
    'medical',
    'medical_collection',
    'system',
    'ae_tracker',
    'subject_medical',
    'doc_autofill',
    'hospital',
    'ruixing_chat',
    'external',
]

DCM_COMMENT_KEY = 'verbose_name'

DCM_TABLE_COMMENT_KEY = 'verbose_name'

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    # 'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'smo.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'smo.wsgi.application'

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'aigc_prod',
        'HOST': '***************',
        'PORT': '3307',
        'USER': 'aigc_prod',
        'PASSWORD': 'LeI1sP4c5Z5LKnlk473wVqUdns0NchqF',
    },
    'ETMF': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'etmf_prod',
            'HOST': '***************',
            'PORT': 3307,
            'USER': 'etmf_prod',
            'PASSWORD': 'GXdHt6baHiTXMo6Vnr5iHAZhLexjT8Ou'}
}
DATABASES_ETMF = {
    'ETMF': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'etmf_prod',
        'HOST': '***************',
        'PORT': 3307,
        'USER': 'etmf_prod',
        'PASSWORD': 'GXdHt6baHiTXMo6Vnr5iHAZhLexjT8Ou'}
}

# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

# TIME_ZONE = 'UTC'
TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_URL = 'static/'

STATICFILES_DIRS = ['./static']

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# https://www.django-rest-framework.org/api-guide/settings/

REST_FRAMEWORK = {
    # 'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    # 'PAGE_SIZE': 10,
    'EXCEPTION_HANDLER': 'common.views.custom_exception_handler',
    # 'DEFAULT_PARSER_CLASSES': [
    #     'rest_framework.parsers.JSONParser',
    # ]
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        # 'common.auth.ERPSysJWTAuthentication',
    ],
    'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend'],
    # 'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'DEFAULT_SCHEMA_CLASS': 'common.schema.CustomSchema',
    'DATETIME_FORMAT': '%Y-%m-%d %H:%M:%S',  # 设置日期时间格式
    'DATE_FORMAT': '%Y-%m-%d',  # 设置日期格式
    'TIME_FORMAT': '%H:%M:%S',  # 设置时间格式
}

SPECTACULAR_SETTINGS = {
    'SWAGGER_UI_DIST': 'SIDECAR',  # shorthand to use the sidecar instead
    'SWAGGER_UI_FAVICON_HREF': 'SIDECAR',
    'REDOC_DIST': 'SIDECAR',
    'TITLE': 'SMO AI Backend API - PROD',
    'VERSION': '1.0.0',
    # OTHER SETTINGS
    'ENUM_NAME_OVERRIDES': {},
    'SORT_OPERATIONS': False,
    'SORT_OPERATION_PARAMETERS': False,
    'COMPONENT_SPLIT_REQUEST': True
}

# https://docs.djangoproject.com/zh-hans/4.1/ref/settings/#std-setting-APPEND_SLASH
APPEND_SLASH = False

STATIC_ROOT = './www/static'

UNICODE_JSON = False

STRICT_JSON = True

# CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_ALL_ORIGINS = True

CORS_ALLOW_METHODS = [
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
]

CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
    "x-access-token",
]

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
        },
    },
    "loggers": {
        "django": {"handlers": ["console"], "level": "INFO"},
        "app": {"handlers": ["console"], "level": "INFO"},
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
}

NACOS = {
    'APP_SERVICE_NAME': 'smo-ai-backend',
    'APP_DEPLOY_HOST': os.environ.get('APP_DEPLOY_HOST', socket.gethostbyname(socket.gethostname())),
    'APP_DEPLOY_PORT': os.environ.get('APP_DEPLOY_PORT', 8000),
    # 'APP_DEPLOY_HOST': socket.gethostbyname(socket.gethostname()),
    # 'APP_DEPLOY_PORT': 8000,
    'APP_SERVER_GROUP': 'DEFAULT_GROUP',
    'APP_DATA_ID': 'jeecg-prod.yaml',
    'APP_DATA_GROUP': 'DEFAULT_GROUP',
    'SERVER_ADDR': 'nacos.jeecg.svc.cluster.local,8848',
    'USERNAME': 'nacos',
    'PASSWORD': 'nacos',
    'NAMESPACE': '3d3c608e-8c5f-4be2-aadd-2dbc1055cdd9'
}

AUTH_USER_MODEL = 'users.User'

NACOS_DATABASES = tools.load_nacos_dbs(NACOS)

if 'default' in NACOS_DATABASES: 
    NACOS_DATABASES.pop('default')

DATABASES.update(NACOS_DATABASES)

print(DATABASES)

MAIL = tools.load_nacos_mail(NACOS)
print(MAIL)

CONN_MAX_AGE = 120

BPM_TO_ERP_QUERY_NAME = 'RADONDB'

DINGTALK_APP_KEY = 'dingtz6ybrnidwdhyubb'

DINGTALK_APP_SECRET = 'pAhlScW_U7ETFKJGyT_9gPG5Fvq1bj8YQUyH2_LCPQbxixhBb2pmOU5Y5pSWses2'

DINGTALK_CORP_ID = 'ding3856499baa89e2b6'

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'sys_cache',
    }
}

MINIO_ENDPOINT = 'mio.smo-clinplus.com:18081'  # MinIO 地址，如 'play.min.io'

MINIO_ACCESS_KEY = 'VoqXY9fLFba6bueITuEz'    # MinIO 访问密钥

MINIO_SECRET_KEY = 'Noec0n9pbOXI1EZbzDQsErBKIYPhOOQ5l8MjGCT9'    # MinIO 密钥

MINIO_BUCKET_NAME = 'aigc-prod'  # MinIO 存储桶名称

MINIO_SECURE = True  # 是否使用 https

# 项目列表白名单
PROJECT_WHITE_LIST = [
    'smo990001',      # 杨总
    'smo990112',      # 王月
    'smo990129',      # 陈霞
    'smo990128',      # 常婷
    'smo990138',      # 肖春媛
    'smo990328',      # 黄美玲
    'smo9911547',     # 张云飞
    'smo9915014',     # 朱倩倩
    'smo9910407',     # 司晓培
    'smo9913938',     # 宫再娟
    'smo9911147',     # 张东兴
    'smo9913054',     # 于洲
    'smo9914198',     # 李发春
    'smo9916937',     # 常海杰
    'smo9916728',     # 高国辉
    'smo991795',     # 许同
]

AIRFLOW_ENDPOINT = '**************:30436'

AIRFLOW_USERNAME = 'Admin'

AIRFLOW_PASSWORD = '9j9%V<]o/tKf$((T<|ET'

DIFY_RUIXING_CHAT_ENDPOINT = '***************'
# api_key = settings.EXTERNAL_API_KEY  # 建议放配置里
# DIFY_RUIXING_CHAT_API_KEY = 'app-sa1D4NbuoVgYXFKZjJux1oV5'

DIFY_CHAT_API_KEYS = {
    "ruixing-chat": "app-Dlhe0DloHdU5x3MRStq9vaff",
    "project-chat": "app-lZXzSte46BaDm6EMnpetlKtj",
    "ruixing-chat-ds": "app-zkTezqNZc7F4dxB5HwaNqcGU",
    "department_chat": "app-kKdvq177Kl8RFWYXvWGHmS1T",
    "ruixing-chat-qwen": "app-gT2SsCswnm5zshOyWbcrxYTy",
    "bu-chat":"app-tfBWoZiDMpA8HTXpjzpaTFxZ"
}


LLM_REQUEST_CONCURRENT_LIMIT = 4

QWEN_API_KEY = "gpustack_2c60076ccb31e83b_0c8ad932bfc7276e33bc7459b2ebbb26"

FILE_MASKING_API_SECRET_KEY = 'ju2MWB1woTEayw4r5vPgazCQRCNHKGfC'

OT_CALLBACK_API_SECRET_KEY = 'lzsopcpcwnvukaevzyi9pllaarabqv2t'

USE_MEDS_URL = "http://***************/v1/chat-messages"

MEDS_API_KEY = "Bearer app-pBNF4f0qVkMxZG0eoNxBXYjM"

AE_API_KEY = "Bearer app-4R0BPxHgl2MWbFBLWQ5iaKgq"

JAVA_PUSH_TRACKER_IP = "http://**************:30325"

# HIPAA去标识化服务配置
HIPAA_DEIDENTIFY_SERVICE_URL = "http://***************:50505"
