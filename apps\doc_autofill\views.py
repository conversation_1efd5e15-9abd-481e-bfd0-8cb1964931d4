import json
import logging
import os
import uuid
from io import BytesIO

import requests
from django.conf import settings
from django.db import transaction
from django.http import HttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.exceptions import APIException, ValidationError
from rest_framework.filters import OrderingFilter
from rest_framework.mixins import ListModelMixin, CreateModelMixin, UpdateModelMixin, DestroyModelMixin
from rest_framework.parsers import MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet

from apps.hospital.models import Hospital
from apps.project.models import Project, ProjectSite
from common import jwtManager
from common.auth import ERPSysJWTAuthentication
from common.minio_client import get_minio_client
from common.pagination import StandardResultsSetPagination
from common.serializers import FileUrlMixin
from common.utils import calculate_file_hash
from . import filters
from . import models
from . import serializers

logger = logging.getLogger('app')


class BaseAPIView(APIView):
    authentication_classes = [ERPSysJWTAuthentication]
    permission_classes = [IsAuthenticated]


class BaseListViewSet(BaseAPIView, GenericViewSet, ListModelMixin):
    filter_backends = (DjangoFilterBackend, OrderingFilter)

class BaseViewSet(BaseAPIView, GenericViewSet):
    pass
# 中心列表
class HospDocTmplCenterListViewSet(BaseListViewSet, CreateModelMixin):
    http_method_names = ['get']

    queryset = models.HospDocTmplCenter.objects.filter(delete_flag=0)
    filterset_class = filters.HospDocTmplCenterListFilter
    serializer_class = serializers.HospDocTmplCenterListSerializers
    pagination_class = StandardResultsSetPagination
    @extend_schema(summary='中心列表-获取中心列表',tags=['中心文件模板'])
    def list(self, request, format=None):
        res = super().list(request, format)
        data_list = res.data['results']
        for item in data_list:
            item['status_text'] = models.HospDocTmplCenter.StatusCodeEnum.get_status_name(item['status_code'])
        return res

class HospDocTmplCenterDetailViewSet(BaseViewSet, UpdateModelMixin):
    queryset = models.HospDocTmplCenter.objects.filter(delete_flag=0)
    serializer_class = serializers.HospDocTmplCenterSerializers
    http_method_names = ['post']

    @extend_schema(summary='中心列表-中心模板发布',tags=['中心文件模板'],
                   request=serializers.HospDocTmplCenterPublishRequestSerializers,
                   responses=serializers.HospDocTmplCenterSerializers
    )
    @action(url_path='publish', detail=True, methods=['post'])
    def publish(self, request, *args, **kwargs):
        data = request.data.copy()
        if data['status_code'] not in models.HospDocTmplCenter.StatusCodeEnum.values:
            raise SmoBizError(detail="发布状态不合法")
        # 根据主键查询数据
        doc_center = self.get_object()
        # 校验发布状态
        released_status = models.HospDocTmplCenter.StatusCodeEnum.RELEASED.value
        # if doc_center.status_code == released_status:
        #     raise SmoBizError(detail="中心模板已发布，无需再次发布")
        if doc_center.status_code == released_status:
            if data['status_code'] == released_status:
                serializer = serializers.HospDocTmplCenterSerializers(doc_center)
                return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            if data['status_code'] != released_status:
                serializer = serializers.HospDocTmplCenterSerializers(doc_center)
                return Response(serializer.data, status=status.HTTP_200_OK)
        data['update_user'] = request.sys_user.username
        data['update_name'] = request.sys_user.realname

        serializer = serializers.HospDocTmplCenterSerializers(doc_center, data=data, partial=True)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        # 更新数据 TODO 模板关联数据初始化-模板文件、项目文件
        with transaction.atomic():
            self.perform_update(serializer)
        serializer = serializers.HospDocTmplCenterSerializers(doc_center)
        return Response(serializer.data,status=status.HTTP_200_OK)


# 中心文件模板信息
class HospDocTmplInfoListViewSet(BaseListViewSet, CreateModelMixin):
    http_method_names = ['get']

    queryset = models.HospDocTmplInfo.objects.filter(delete_flag=0)
    filterset_class = filters.HospDocTmplInfoListFilter
    serializer_class = serializers.HospDocTmplInfoListSerializers
    pagination_class = StandardResultsSetPagination

    @extend_schema(summary='中心文件清单-获取中心模板列表',tags=['中心文件模板'])
    def list(self, request, format=None):
        res = super().list(request, format)
        data_list = res.data['results']
        for item in data_list:
            item['status_text'] = models.HospDocTmplInfo.StatusCodeEnum.get_status_name(item['status_code'])
            item['audit_status_text'] = models.HospDocTmplInfo.AuditStatusCodeEnum.get_audit_status_name(item['audit_status_code'])

        return res

class HospDocTmplInfoDetailViewSet(BaseViewSet, UpdateModelMixin,DestroyModelMixin):
    queryset = models.HospDocTmplInfo.objects.filter(delete_flag=0)
    serializer_class = serializers.HospDocTmplInfoSerializers
    http_method_names = ['post','delete']

    @extend_schema(summary='中心文件清单-重命名',tags=['中心文件模板'],
        request=serializers.FileNameUpdateRequestSerializers,
        responses=serializers.HospDocTmplInfoSerializers
    )
    @action(url_path='rename', detail=True, methods=['post'])
    def rename(self, request, *args, **kwargs):
        data = request.data.copy()
        data['update_user'] = request.sys_user.username
        data['update_name'] = request.sys_user.realname
        # 根据主键查询数据
        tmpl_info = self.get_object()
        # 校验状态
        if tmpl_info.audit_status_code == models.HospDocTmplInfo.AuditStatusCodeEnum.APPROVED.value:
            raise SmoBizError(detail="中心模板文件已完成审批，不允许重命名")

        serializer = serializers.HospDocTmplInfoSerializers(tmpl_info, data=data, partial=True)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # 更新数据（模板关联文件original_name同步更新）
        with transaction.atomic():
            self.perform_update(serializer)

            tmpl_file = tmpl_info.tmpl_file
            name, extension = os.path.splitext(tmpl_file.original_filename)
            file_update = data.copy()
            # 构建新的文件名，保留扩展名
            file_update['original_filename'] = data['file_name'] + extension

            if tmpl_file and tmpl_file.original_filename != file_update['original_filename']:
                serializer = serializers.HospDocTmplFileUpdateSerializer(tmpl_file, data=file_update, partial=True)
                if serializer.is_valid():
                    self.perform_update(serializer)
            tmpl_file_processed = tmpl_info.tmpl_file_processed
            if tmpl_file_processed and tmpl_file_processed.original_filename != file_update['original_filename']:
                serializer = serializers.HospDocTmplFileProcessedUpdateSerializer(tmpl_file_processed, data=file_update, partial=True)
                if serializer.is_valid():
                    self.perform_update(serializer)

        serializer = serializers.HospDocTmplInfoSerializers(tmpl_info)
        return Response(serializer.data,status=status.HTTP_200_OK)

    @extend_schema(summary='中心文件清单-删除',tags=['中心文件模板'])
    def destroy(self, request, *args, **kwargs):
        # 根据主键查询数据
        tmpl_info = self.get_object()
        # 校验状态
        if tmpl_info.audit_status_code == models.HospDocTmplInfo.AuditStatusCodeEnum.APPROVED.value:
            raise SmoBizError(detail="中心模板文件已完成审批，不允许删除")

        with transaction.atomic():
            # 更新模板文件删除状态
            tmpl_info.tmpl_file.delete_flag = 1
            tmpl_info.tmpl_file.update_user = request.sys_user.username
            tmpl_info.tmpl_file.update_name = request.sys_user.realname
            tmpl_info.tmpl_file.save()
            # 更新处理后模板文件删除状态
            tmpl_info.tmpl_file_processed.delete_flag = 1
            tmpl_info.tmpl_file_processed.update_user = request.sys_user.username
            tmpl_info.tmpl_file_processed.update_name = request.sys_user.realname
            tmpl_info.tmpl_file_processed.save()
            # 更新中心模板信息删除状态、解除文件关系
            tmpl_info.delete_flag = 1
            tmpl_info.update_user = request.sys_user.username
            tmpl_info.update_name = request.sys_user.realname
            tmpl_info.tmpl_file_id = None
            tmpl_info.tmpl_file_processed_id = None
            tmpl_info.save()
            # TODO 更新中心列表已上传文件模板量

        return Response(status=status.HTTP_200_OK)

    @extend_schema(summary='中心文件清单-审核',tags=['中心文件模板'],
                   request=serializers.HospDocTmplInfoAuditRequestSerializers
    )
    @action(url_path='audit', detail=True, methods=['post'])
    def audit(self, request, *args, **kwargs):
        data = request.data.copy()
        if data['audit_status_code'] not in models.HospDocTmplInfo.AuditStatusCodeEnum.values:
            raise SmoBizError(detail="审核状态不合法")
        # 根据主键查询数据
        tmpl_info = self.get_object()
        # 校验状态
        if tmpl_info.status_code != models.HospDocTmplInfo.StatusCodeEnum.COMPLETED.value:
            raise SmoBizError(detail="当前系统模板尚未完成，暂不允许审核")
        audit_approved_status = models.HospDocTmplInfo.AuditStatusCodeEnum.APPROVED.value
        if tmpl_info.audit_status_code == audit_approved_status:
            if data['audit_status_code'] == audit_approved_status:
                serializer = serializers.HospDocTmplInfoSerializers(tmpl_info)
                return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            if data['audit_status_code'] != audit_approved_status:
                serializer = serializers.HospDocTmplInfoSerializers(tmpl_info)
                return Response(serializer.data, status=status.HTTP_200_OK)

        data['update_user'] = request.sys_user.username
        data['update_name'] = request.sys_user.realname
        serializer = serializers.HospDocTmplInfoSerializers(tmpl_info, data=data, partial=True)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        # 处理数据
        with transaction.atomic():
            self.perform_update(serializer)
            serializer = serializers.HospDocTmplInfoSerializers(tmpl_info)
        return Response(serializer.data,status=status.HTTP_200_OK)
    @extend_schema(summary='中心文件清单-批量文件下载（空白模板/AI模板）',tags=['中心文件模板'],
                   request=serializers.HospDocTmplInfoDownloadRequestSerializers,
                   responses=serializers.HospDocTmplFileSerializer(many=True)
                   )
    @action(url_path='download', detail=False, methods=['post'])
    def download(self, request, format=None):
        data = request.data.copy()
        files = []
        if not data['tmpl_type'] or data['tmpl_type'] not in models.HospDocTmplInfo.TmplTypeEnum.values:
            raise SmoBizError(detail="请输入正确的模板类型")
        if models.HospDocTmplInfo.TmplTypeEnum.BLANK_TMPL.value == data['tmpl_type']:
            # 查询空模板信息
            tmpl_infos = (models.HospDocTmplInfo.objects.filter(delete_flag=0, hospital__hosp_id=data['hosp_id'])
                          .filter(tmpl_file__delete_flag=0))
            for tmpl_info in tmpl_infos:
                files.append(tmpl_info.tmpl_file)
        else:
            # 查询AI模板信息
            tmpl_infos = (models.HospDocTmplInfo.objects.filter(delete_flag=0, hospital__hosp_id=data['hosp_id'])
                          .filter(tmpl_file_processed__delete_flag=0))
            for tmpl_info in tmpl_infos:
                files.append(tmpl_info.tmpl_file_processed)

        if not files:
            return Response(status=status.HTTP_200_OK)
        serializer = serializers.HospDocTmplFileSerializer(files, many=True)
        return Response(serializer.data,status=status.HTTP_200_OK)

class HospDocTmplInfoUploadViewSet(BaseListViewSet):
    parser_classes = (MultiPartParser,)  # 支持文件上传
    queryset = models.HospDocTmplInfo.objects.filter(delete_flag=0)
    serializer_class = serializers.HospDocTmplInfoSerializers
    http_method_names = ['post']

    @extend_schema(summary='中心文件清单-文件上传', tags=['中心文件模板'],
                   request=serializers.HospDocTmplInfoUploadRequestSerializers,
                   responses=serializers.HospDocTmplFileSerializer
    )
    @action(url_path='upload', detail=False, methods=['post'])
    def upload(self, request, format=None):
        file = request.FILES.get('file')
        hosp_id = request.POST.get('hosp_id')
        serializer = serializers.HospDocTmplInfoUploadRequestSerializers(data=request.data)
        if not serializer.is_valid():
            raise ValidationError(serializer.errors)
        if not file:
            raise ValidationError({'file': ['未提供文件']})
        valid_extensions = ['.docx']
        file_extension = '.' + file.name.split('.')[-1].lower()
        if file_extension not in valid_extensions:
            raise ValidationError({'file': ['中心模板文件目前暂仅支持docx格式word文件']})
        # 检查数据是否存在
        hospital = Hospital.objects.filter(delete_flag=0).get(hosp_id=hosp_id)
        if hospital is None or hospital.hosp_id is None:
            raise SmoBizError(detail="未找到对应医院信息")

        # 计算文件的 SHA-256 哈希值
        base_name, ext = os.path.splitext(file.name)
        # 生成唯一的对象名称
        hash = calculate_file_hash(file)
        if models.HospDocTmplInfo.objects.filter(delete_flag=0, hospital__hosp_id=hosp_id).filter(
                tmpl_file__delete_flag=0,
                tmpl_file__hash=hash
        ).exists():
            raise SmoBizError("文件已存在")

        object_name = f"{uuid.uuid4().hex}{ext}"
        bucket_name = settings.MINIO_BUCKET_NAME

        # 保存文件
        try:
            minio_client = get_minio_client()
            # Ensure bucket exists
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)
            # Upload file to MinIO
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file,
                length=file.size,
                part_size=1024 * 1024 * 5,
                content_type=file.content_type
            )
        except Exception as e:
            logger.error(e)
            raise APIException(f"文件上传失败：{e}")

        # 数据入库
        with transaction.atomic():
            # 中心模板文件信息
            hosp_doc_tmpl_file = {
                'original_filename': file.name,
                'bucket_name': bucket_name,
                'object_name': object_name,
                'content_type': file.content_type,
                'size': file.size,
                'hash': hash,
                'create_user': request.sys_user.username,
                'create_name': request.sys_user.realname,
            }
            hosp_doc_tmpl_file = models.HospDocTmplFile.objects.create(**hosp_doc_tmpl_file)
            # 中心模板信息
            file_extension = file.name.split('.')[-1].upper()
            hosp_doc_tmpl_info = {
                'file_name': file.name,
                'file_extension': file_extension,
                'status_code': models.HospDocTmplInfo.StatusCodeEnum.NOT_CREATED.value,
                'audit_status_code': models.HospDocTmplInfo.AuditStatusCodeEnum.UNAPPROVED.value,
                'hospital': hospital,
                'tmpl_file': hosp_doc_tmpl_file,
                'create_user': request.sys_user.username,
                'create_name': request.sys_user.realname
            }
            models.HospDocTmplInfo.objects.create(**hosp_doc_tmpl_info)

            serializer = serializers.HospDocTmplFileSerializer(hosp_doc_tmpl_file)
        return Response(serializer.data, status=status.HTTP_200_OK)


# 项目中心文件列表
class ProjectSiteDocCenterListViewSet(BaseListViewSet, CreateModelMixin):
    http_method_names = ['get']

    queryset = models.ProjectSiteDocCenter.objects.filter(delete_flag=0)
    filterset_class = filters.ProjectSiteDocCenterListFilter
    serializer_class = serializers.ProjectSiteDocCenterListSerializers
    pagination_class = StandardResultsSetPagination
    @extend_schema(summary='项目列表-获取项目中心列表',tags=['中心文件模板'])
    def list(self, request, format=None):
        res = super().list(request, format)
        data_list = res.data['results']
        for item in data_list:
            item['status_text'] = models.ProjectSiteDocCenter.StatusCodeEnum.get_status_name(item['status_code'])
        return res

# 项目中心文件信息列表
class ProjectSiteDocInfoListViewSet(BaseListViewSet, CreateModelMixin):
    http_method_names = ['get']

    queryset = models.ProjectSiteDocInfo.objects.filter(delete_flag=0)
    filterset_class = filters.ProjectSiteDocInfoListFilter
    serializer_class = serializers.ProjectSiteDocInfoListSerializers
    pagination_class = StandardResultsSetPagination

    @extend_schema(summary='项目列表-获取项目文件信息列表',tags=['中心文件模板'])
    def list(self, request, format=None):
        res = super().list(request, format)
        data_list = res.data['results']
        hosp_id_set = set()
        for item in data_list:
            hosp_id_set.add(item['hosp_id'])
            item['status_text'] = models.ProjectSiteDocInfo.StatusCodeEnum.get_status_name(item['status_code'])
            item['audit_status_text'] = models.ProjectSiteDocInfo.AuditStatusCodeEnum.get_audit_status_name(item['audit_status_code'])
        if hosp_id_set.__len__() > 0:
            hosp_tmpl_list = models.HospDocTmplInfo.objects.filter(delete_flag=0, hospital__hosp_id__in=hosp_id_set)
            hosp_doc_tmpl_list = serializers.HospDocTmplInfoListSerializers(hosp_tmpl_list, many=True).data
            for hosp_doc_tmpl in hosp_doc_tmpl_list:
                for item in data_list:
                    if item['hosp_id'] == hosp_doc_tmpl['hosp_id']:
                        item['tmpl_file_processed'] = hosp_doc_tmpl['tmpl_file_processed']
                        break
        return res

class ProjectSiteDocInfoDetailViewSet(BaseViewSet, UpdateModelMixin,DestroyModelMixin):
    queryset = models.ProjectSiteDocInfo.objects.filter(delete_flag=0)
    serializer_class = serializers.ProjectSiteDocInfoSerializers
    http_method_names = ['post','delete']

    @extend_schema(summary='项目列表-文件清单-重命名', tags=['中心文件模板'],
                   request=serializers.FileNameUpdateRequestSerializers,
                   responses=serializers.ProjectSiteDocInfoSerializers
    )
    @action(url_path='rename', detail=True, methods=['post'])
    def rename(self, request, *args, **kwargs):
        data = request.data.copy()
        data['auditor_id'] = data['update_user'] = request.sys_user.username
        data['auditor_name'] = data['update_name'] = request.sys_user.realname
        # 根据主键查询数据
        doc_info = self.get_object()
        # 校验状态
        if doc_info.audit_status_code == models.ProjectSiteDocInfo.AuditStatusCodeEnum.APPROVED.value:
            raise SmoBizError(detail="中心模板文件已完成审批，不允许重命名")

        serializer = serializers.ProjectSiteDocInfoSerializers(doc_info, data=data, partial=True)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # 更新数据（模板关联文件original_name同步更新）
        with transaction.atomic():
            self.perform_update(serializer)

            site_doc_file = doc_info.doc_file
            name, extension = os.path.splitext(site_doc_file.original_filename)
            file_update = data.copy()
            # 构建新的文件名，保留扩展名
            file_update['original_filename'] = data['file_name'] + extension
            if site_doc_file and site_doc_file.original_filename != file_update['original_filename']:
                serializer = serializers.HospDocTmplFileUpdateSerializer(site_doc_file, data=file_update, partial=True)
                if serializer.is_valid():
                    self.perform_update(serializer)

        serializer = serializers.ProjectSiteDocInfoSerializers(doc_info)
        return Response(serializer.data,status=status.HTTP_200_OK)

    @extend_schema(summary='项目列表-文件清单-删除',tags=['中心文件模板'])
    def destroy(self, request, *args, **kwargs):
        # 根据主键查询数据
        doc_info = self.get_object()
        # 校验状态
        if doc_info.audit_status_code == models.ProjectSiteDocInfo.AuditStatusCodeEnum.APPROVED.value:
            raise SmoBizError(detail="项目文件已完成审批，不允许删除")

        with transaction.atomic():
            # 更新项目文件删除状态
            doc_info.doc_file.delete_flag = 1
            doc_info.doc_file.update_user = request.sys_user.username
            doc_info.doc_file.update_name = request.sys_user.realname
            doc_info.doc_file.save()
            # 更新项目文件信息删除状态、解除文件关系
            doc_info.delete_flag = 1
            doc_info.update_user = request.sys_user.username
            doc_info.update_name = request.sys_user.realname
            doc_info.doc_file_id = None
            doc_info.save()
        return Response(status=status.HTTP_200_OK)

    @extend_schema(summary='项目列表-文件清单-审核',tags=['中心文件模板'],
                   request=serializers.HospDocTmplInfoAuditRequestSerializers
    )
    @action(url_path='audit', detail=True, methods=['post'])
    def audit(self, request, *args, **kwargs):
        data = request.data.copy()
        if data['audit_status_code'] not in models.ProjectSiteDocInfo.AuditStatusCodeEnum.values:
            raise SmoBizError(detail="审核状态不合法")
        # 根据主键查询数据
        doc_info = self.get_object()
        # 校验状态
        if doc_info.status_code != models.HospDocTmplInfo.StatusCodeEnum.COMPLETED.value:
            raise SmoBizError(detail="当前项目文件尚未完成，暂不允许审核")
        audit_approved_status = models.ProjectSiteDocInfo.AuditStatusCodeEnum.APPROVED.value
        if doc_info.audit_status_code == audit_approved_status:
            if data['audit_status_code'] == audit_approved_status:
                serializer = serializers.ProjectSiteDocInfoSerializers(doc_info)
                return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            if data['audit_status_code'] != audit_approved_status and data['audit_status_code'] == doc_info.audit_status_code:
                serializer = serializers.ProjectSiteDocInfoSerializers(doc_info)
                return Response(serializer.data, status=status.HTTP_200_OK)
        data['auditor_id'] = data['update_user'] = request.sys_user.username
        data['auditor_name'] = data['update_name'] = request.sys_user.realname
        serializer = serializers.ProjectSiteDocInfoSerializers(doc_info, data=data, partial=True)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        # 处理数据
        with transaction.atomic():
            self.perform_update(serializer)
            serializer = serializers.ProjectSiteDocInfoSerializers(doc_info)
        return Response(serializer.data,status=status.HTTP_200_OK)
    @extend_schema(summary='项目列表-文件清单-批量文件下载',tags=['中心文件模板'],
                   request=serializers.ProjectSiteDocInfoDownloadRequestSerializers,
                   responses=serializers.ProjectSiteDocFileSerializer(many=True)
                   )
    @action(url_path='download', detail=False, methods=['post'])
    def download(self, request, format=None):
        data = request.data.copy()
        # 查询空模板信息
        doc_infos = (models.ProjectSiteDocInfo.objects.filter(delete_flag=0, hospital__hosp_id=data['hosp_id'],
                                                              project__project_id=data['project_id'],
                                                              project_site__project_site_id=data['project_site_id'])
                     .filter(doc_file__delete_flag=0))
        doc_files = []
        for doc_info in doc_infos:
            doc_files.append(doc_info.doc_file)
        if not doc_files:
            return Response(status=status.HTTP_200_OK)
        serializer = serializers.ProjectSiteDocFileSerializer(doc_files, many=True)
        return Response(serializer.data,status=status.HTTP_200_OK)

# 自动回填任务 （批量生成任务）
class DocAutofillTasksView(BaseViewSet, CreateModelMixin):
    queryset = models.DocAutofillTask.objects.filter(delete_flag=0)
    http_method_names = ['post']

    @extend_schema(summary='创建自动回填生成任务', tags=['中心文件模板'],
                   request=serializers.DocAutofillTaskSingleCreatRequestSerializer
    )
    def create(self, request, format=None):
        data = request.data.copy()
        category = data.get('category')
        tmpl_info_id = data.get('tmpl_info_id')
        doc_info_id = data.get('doc_info_id')
        # 参数校验
        if category not in models.DocAutofillTask.Category.values:
            raise SmoBizError(detail="任务分类不合法")
        if category == models.DocAutofillTask.Category.GEN_SYS_TMPL.value:
            if tmpl_info_id is None:
                raise SmoBizError(detail="中心模板文件信息ID不能为空")
            self.create_hosp_tmpl_autofill_task(request, tmpl_info_id)
        else:
            if doc_info_id is None:
                raise SmoBizError(detail="项目文件信息ID不能为空")
            self.create_project_site_doc_autofill_task(request, doc_info_id)

        return Response(status=status.HTTP_200_OK)
    # 中心系统模板生成任务
    @staticmethod
    def create_hosp_tmpl_autofill_task(request, tmpl_info_id):
        # 根据主键查询数据
        tmpl_info = models.HospDocTmplInfo.objects.get(id=tmpl_info_id)
        if tmpl_info is None or tmpl_info.id is None:
            raise SmoBizError(detail="未找到对应中心模板文件信息")
        # 校验状态
        if tmpl_info.status_code == models.HospDocTmplInfo.StatusCodeEnum.CREATING.value:
            raise SmoBizError(detail="正在生成中")

        hospital = tmpl_info.hospital
        if hospital is None or hospital.hosp_id is None:
            raise SmoBizError(detail="未找到对应医院信息")
        # 处理数据
        with transaction.atomic():
            # 更新中心模板信息生成状态
            tmpl_info.update_user = request.sys_user.username
            tmpl_info.update_name = request.sys_user.realname
            tmpl_info.status_code = models.HospDocTmplInfo.StatusCodeEnum.CREATING.value
            tmpl_info.save()
            # 写入doc_auto_job 生成系统模板
            category_value = models.DocAutofillTask.Category.GEN_SYS_TMPL.value
            doc_autofill_task_obj = {
                'name': category_value + '-' + hospital.hosp_id + '-' + str(tmpl_info.id) + '-' + 'task',
                'category': category_value,
                'status': models.DocAutofillTask.StatusEnum.TODO.value,
                'hospital': hospital,
                'hosp_doc_tmpl': tmpl_info,
                'create_user': request.sys_user.username,
                'create_name': request.sys_user.realname,
            }
            models.DocAutofillTask.objects.create(**doc_autofill_task_obj)

    # 项目文件生成任务
    @staticmethod
    def create_project_site_doc_autofill_task(request, doc_info_id):
        # 根据主键查询数据
        doc_info = models.ProjectSiteDocInfo.objects.get(id=doc_info_id)
        if doc_info is None or doc_info.id is None:
            raise SmoBizError(detail="未找到对应项目中心文件信息")
        # 校验状态
        if doc_info.status_code == models.ProjectSiteDocInfo.StatusCodeEnum.CREATING.value:
            raise SmoBizError(detail="正在生成中")
        # 获取医院、项目、中心信息
        hospital = doc_info.hospital
        if hospital is None or hospital.hosp_id is None:
            raise SmoBizError(detail="未找到对应医院信息")
        project = doc_info.project
        if project is None or project.project_id is None:
            raise SmoBizError(detail="未找到对应项目信息")
        project_site = doc_info.project_site
        if project_site is None or project_site.project_site_id is None:
            raise SmoBizError(detail="未找到对应项目中心信息")
        # 处理数据
        with transaction.atomic():
            # 更新中心模板信息生成状态
            doc_info.update_user = request.sys_user.username
            doc_info.update_name = request.sys_user.realname
            doc_info.status_code = models.HospDocTmplInfo.StatusCodeEnum.CREATING.value
            doc_info.save()
            # 写入doc_auto_job 生成系统模板
            category_value = models.DocAutofillTask.Category.GEN_PROJECT_FILE.value
            doc_autofill_task_obj = {
                'name': category_value + '-' + hospital.hosp_id + '-' + str(doc_info.id) + '-' + 'task',
                'category': category_value,
                'status': models.DocAutofillTask.StatusEnum.TODO.value,
                'hospital': hospital,
                'project': project,
                'project_site': project_site,
                'project_site_doc': doc_info,
                'create_user': request.sys_user.username,
                'create_name': request.sys_user.realname,
            }
            models.DocAutofillTask.objects.create(**doc_autofill_task_obj)


    @extend_schema(summary='批量创建自动回填生成任务', tags=['中心文件模板'],
                   request=serializers.DocAutofillTaskBatchCreatRequestSerializer
    )
    @action(url_path='batch_create', detail=False, methods=['post'])
    def batch_create(self, request, format=None):
        data = request.data.copy()
        category = data.get('category')
        hosp_id = data.get('hosp_id')
        project_id = data.get('project_id')
        project_site_id = data.get('project_site_id')
        # 参数校验
        if category not in models.DocAutofillTask.Category.values:
            raise SmoBizError(detail="任务分类不合法")
        # 医院信息校验
        hospital = Hospital.objects.filter(hosp_id=hosp_id, delete_flag=0).first()
        if not hospital:
            raise SmoBizError(detail="未找到对应医院信息")
        NOT_CREATED_STATUS = models.HospDocTmplInfo.StatusCodeEnum.NOT_CREATED.value
        COMPLETED_STATUS = models.HospDocTmplInfo.StatusCodeEnum.COMPLETED.value
        #  任务分类
        if category == models.DocAutofillTask.Category.GEN_SYS_TMPL.value:
            # 查询是否有还未生成任务的 hosp_doc_tmpls
            hosp_doc_tmpls = models.HospDocTmplInfo.objects.filter(hospital__hosp_id=hosp_id, delete_flag=0, status_code__in=[NOT_CREATED_STATUS, COMPLETED_STATUS])
            if hosp_doc_tmpls is None or hosp_doc_tmpls.__len__()  == 0:
                raise SmoBizError(detail="当前中心系统模板任务已生成,正在处理中不需要再次生成")
            # 数据入库
            with transaction.atomic():
                for hosp_doc_tmpl in hosp_doc_tmpls:
                    doc_autofill_task_obj = {
                        'name': category + '-' + hosp_id + '-' + str(hosp_doc_tmpl.id) + '-' + 'task',
                        'category': category,
                        'status': models.DocAutofillTask.StatusEnum.TODO.value,
                        'hospital': hospital,
                        'hosp_doc_tmpl': hosp_doc_tmpl,
                        'create_user': request.sys_user.username,
                        'create_name': request.sys_user.realname
                    }
                    # 更新hosp_doc_tmpl状态
                    hosp_doc_tmpl.update_user = request.sys_user.username
                    hosp_doc_tmpl.update_name = request.sys_user.realname
                    hosp_doc_tmpl.status_code = models.HospDocTmplInfo.StatusCodeEnum.CREATING.value
                    hosp_doc_tmpl.save()
                    # 创建任务
                    models.DocAutofillTask.objects.create(**doc_autofill_task_obj)
        else:
            if project_id is None or project_id.__len__() == 0:
                raise SmoBizError(detail="项目ID不能为空")
            if project_site_id is None or project_site_id.__len__() == 0:
                raise SmoBizError(detail="项目中心ID不能为空")
            # 项目信息校验
            project = Project.objects.filter(project_id=project_id, delete_flag=0).first()
            if not project:
                raise SmoBizError(detail="未找到对应项目信息")
            project_site = ProjectSite.objects.filter(project_site_id=project_site_id, delete_flag=0).first()
            if not project_site:
                raise SmoBizError(detail="未找到对应项目中心信息")

            # 查询是否有还未生成任务的 project_site_docs
            project_site_docs = models.ProjectSiteDocInfo.objects.filter(hospital__hosp_id=hosp_id, project__project_id=project_id, project_site__project_site_id=project_site_id,
                                                                   delete_flag=0,status_code__in=[NOT_CREATED_STATUS,COMPLETED_STATUS])
            if project_site_docs is None or project_site_docs.__len__() == 0:
                raise SmoBizError(detail="当前项目文件任务已生成,正在处理中不需要再次生成")
            # 数据入库
            with transaction.atomic():
                for project_site_doc in project_site_docs:
                    doc_autofill_task_obj = {
                        'name': category + '-' + hosp_id + '-' + str(project_site_doc.id) + '-' + 'task',
                        'category': category,
                        'status': models.DocAutofillTask.StatusEnum.TODO.value,
                        'hospital': hospital,
                        'project': project,
                        'project_site': project_site,
                        'project_site_doc': project_site_doc,
                        'create_user': request.sys_user.username,
                        'create_name': request.sys_user.realname
                    }
                    # 更新project_site_doc状态
                    project_site_doc.update_user = request.sys_user.username
                    project_site_doc.update_name = request.sys_user.realname
                    project_site_doc.status_code = models.HospDocTmplInfo.StatusCodeEnum.CREATING.value
                    project_site_doc.save()
                    # 创建任务
                    models.DocAutofillTask.objects.create(**doc_autofill_task_obj)
        return Response(status=status.HTTP_200_OK)


# 在线文档工具
class OnlyOfficeView(BaseAPIView):#
    @extend_schema(summary='在线文档-获取配置信息', tags=['在线文档工具'],
                   request=serializers.OnlyOfficeConfigsRequestSerializers,
    )
    def post(self, request, format=None):
        data = request.data.copy()
        biz_type = data['biz_type']
        file_id = data.get('file_id')

        if biz_type == models.FileBizTypeEnum.SITE_SYS_TMPL.value:
            file_obj = models.HospDocTmplFileProcessed.objects.filter(id=file_id, delete_flag=0).first()
        elif biz_type == models.FileBizTypeEnum.SITE_PROJECT_FILE.value:
            file_obj = models.ProjectSiteDocFile.objects.filter(id=file_id, delete_flag=0).first()
        else:
            raise SmoBizError(detail="业务类型错误")
        if not file_obj:
            raise SmoBizError(detail="目标文件不存在")

        json_configs = {
            "document": {
                "title": "",
                "key": "",
                "fileType":"",
                "lang": "zh-CN",
                "permissions": {
                    "comment": True,
                    "commentGroups": {
                        "edit": ["Group2", "Group1"],
                        "remove": [""],
                        "view": ""
                    },
                    "copy": True,
                    "deleteCommentAuthorOnly": False,
                    "download": True,
                    "edit": True,
                    "editCommentAuthorOnly": False,
                    "fillForms": True,
                    "modifyContentControl": True,
                    "modifyFilter": True,
                    "print": True,
                    "review": True,
                    "reviewGroups": ["Group1", "Group2", ""]
                },
                "url": ""
            },
            "editorConfig": {
                "customization":{
                    "autosave": True,
                    "forcesave": True
                },
                "lang": "zh-CN",
                "callbackUrl": "",
                "onEditing": {
                     "mode": "fast",
                     "change": True
                },
                "mode": "edit",
                "user": {
                    "group": "Group1",
                    "id": "",
                    "name": ""
                }
            }
        }
        json_configs = json_configs.copy()
        json_configs['document']['title'] = file_obj.original_filename
        json_configs['document']['key'] = biz_type + '-' + str(file_obj.id)
        json_configs['document']['fileType'] = file_obj.original_filename.split('.')[-1]
        json_configs['document']['url'] = FileUrlMixin.get_url(request,file_obj)
        json_configs['editorConfig']['callbackUrl'] = settings.ONLY_OFFICE['callbackUrl'].replace('{biz_type}',biz_type.lower().replace('_','-')).replace('{file_id}',str(file_id))
        json_configs['editorConfig']['user']['id'] = request.sys_user.username
        json_configs['editorConfig']['user']['name'] = request.sys_user.realname

        json_configs['token'] = jwtManager.encode(json_configs)

        return Response(json_configs,status=status.HTTP_200_OK)
class OnlyOfficeCallbackView(APIView):
    @extend_schema(summary='在线文档-编辑文档后回调地址', tags=['在线文档工具'],
        parameters=[
            OpenApiParameter(name='biz_type', required=True, location=OpenApiParameter.PATH,
                             description='业务类型：SITE_SYS_TMPL 中心系统模板，SITE_PROJECT_FILE 中心项目文件'),
            OpenApiParameter(name='file_id', required=True, location=OpenApiParameter.PATH,
                             description='文件id')
        ]
    )
    def post(self, request, biz_type, file_id):
        response = {}
        try:
            body = request.body
            if not body:
                raise SmoBizError(detail="body为空")
            try:
                json_body = json.loads(body)
            except Exception as e:
                raise SmoBizError(detail="body解析错误")
            print(f'=====文件编辑后获取的参数是：{json_body}')
            result_status = json_body.get('status')
            if {2,3,6,7}.__contains__(result_status):
                file_url = json_body.get('status')
                # 1. 下载远程文件
                response = requests.get(file_url, stream=True)
                response.raise_for_status()
                # 2. 将内容转为流式文件对象
                file_data = BytesIO(response.content)
                file_size = len(file_data.getvalue())
                # 3. 数据校验、保存
                if biz_type == models.FileBizTypeEnum.SITE_SYS_TMPL.value:
                    self.sys_tmpl_file_processed(request, file_data, file_size, file_id)
                elif biz_type == models.FileBizTypeEnum.SITE_PROJECT_FILE.value:
                    self.project_site_doc_file(request, file_data, file_size, file_id)
                else:
                    raise SmoBizError(detail="业务类型错误")
        except SmoBizError as e:
            response.setdefault("error", 1)
            response.setdefault("message", e.detail)
        except Exception as e:
            response.setdefault("error", 1)
            response.setdefault("message", str(e.args[0]))
        response.setdefault('error', 0)
        return HttpResponse(json.dumps(response),content_type='application/json',status=status.HTTP_200_OK if response.get('error') == 0 else status.HTTP_500_INTERNAL_SERVER_ERROR)

    # 系统模板文件信息
    def sys_tmpl_file_processed(self, request, file_data, file_size, file_id):
        # 查询AI生成系统模板信息
        tmpl_info = (models.HospDocTmplInfo.objects.filter(delete_flag=0)
                     .filter(tmpl_file_processed__id=file_id, tmpl_file_processed__delete_flag=0).first())
        if not tmpl_info or not tmpl_info.tmpl_file_processed:
            raise SmoBizError(detail="目标文件不存在")
        ext = tmpl_info.file_extension.lower()
        content_type = tmpl_info.tmpl_file_processed.content_type
        original_filename = tmpl_info.tmpl_file_processed.original_filename

        object_name,bucket_name = self.upload_file(file_data, file_size, content_type,ext)

        # 中心模板文件信息
        file_info = {
            'original_filename': original_filename,
            'bucket_name': bucket_name,
            'object_name': object_name,
            'content_type': content_type,
            'size': file_size,
            'hash': hash,
            'create_user': request.sys_user.username,
            'create_name': request.sys_user.realname,
        }
        # 数据入库 删除原文件、更新最新文件id
        with transaction.atomic():
            # 保存新文件数据
            file_processed = models.HospDocTmplFileProcessed.objects.create(**file_info)
            # 删除原文件
            tmpl_info.tmpl_file_processed.delete_flag = 1
            tmpl_info.tmpl_file_processed.update_user = request.sys_user.username
            tmpl_info.tmpl_file_processed.update_name = request.sys_user.realname
            tmpl_info.tmpl_file_processed.save()
            # 重新绑定新文件
            tmpl_info.tmpl_file_processed_id = file_processed.id
            tmpl_info.tmpl_file.update_user = request.sys_user.username
            tmpl_info.tmpl_file.update_name = request.sys_user.realname
            tmpl_info.save()
    # 项目模板文件信息
    def project_site_doc_file(self, request, file_data, file_size, file_id):
        # 查询AI项目文件信息
        doc_info = (models.ProjectSiteDocInfo.objects.filter(delete_flag=0)
                    .filter(doc_file__id=file_id, doc_file__delete_flag=0).first())
        if not doc_info or not doc_info.doc_file:
            raise SmoBizError(detail="目标文件不存在")
        ext = doc_info.file_extension.lower()
        content_type = doc_info.doc_file.content_type
        original_filename = doc_info.doc_file.original_filename

        object_name,bucket_name = self.upload_file(file_data, file_size, content_type,ext)
        # 项目文件信息
        doc_file_info = {
            'original_filename': original_filename,
            'bucket_name': bucket_name,
            'object_name': object_name,
            'content_type': content_type,
            'size': file_size,
            'hash': hash,
            'create_user': request.sys_user.username,
            'create_name': request.sys_user.realname,
        }

        # 数据入库 删除原文件、更新最新文件id
        with transaction.atomic():
            # 保存新项目文件数据
            doc_file = models.ProjectSiteDocFile.objects.create(**doc_file_info)
            # 删除原文件
            doc_info.doc_file.delete_flag = 1
            doc_info.doc_file.update_user = request.sys_user.username
            doc_info.doc_file.update_name = request.sys_user.realname
            doc_info.doc_file.save()
            # 重新绑定新文件
            doc_info.doc_file_id = doc_file.id
            doc_info.doc_file.update_user = request.sys_user.username
            doc_info.doc_file.update_name = request.sys_user.realname
            doc_info.save()

    # 上传文件
    @staticmethod
    def upload_file(file_data, file_size, content_type, ext):
        object_name = f"{uuid.uuid4().hex}{ext}"
        bucket_name = settings.MINIO_BUCKET_NAME
        # 保存文件
        try:
            minio_client = get_minio_client()
            # Ensure bucket exists
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)
            # Upload file to MinIO
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file_data,
                length=file_size,
                part_size=1024 * 1024 * 5,
                content_type=content_type
            )
        except Exception as e:
            logger.error(e)
            raise APIException(f"文件上传失败：{e}")
        return object_name,bucket_name


# 通用异常类
class SmoBizError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "业务逻辑异常"
    default_code = "business_error"

    def __init__(self, detail=None, code=None):
        if detail is not None:
            self.detail = detail
        if code is not None:
            self.status_code = code
