"""
支持DeepSeek-R1-Distill-Qwen-32B和Qwen3-32B模型调用
"""
import logging
import requests
from .logging_decorator import log_llm_invocation

logger = logging.getLogger(__name__)

# 模型配置
MODELS = {
    'DeepSeek-R1-Distill-Qwen-32B': {
        'url': 'http://192.168.230.5:48080/v1/chat/completions',
        'headers': {
            "Content-Type": "application/json",
            "Authorization": "Bearer gpustack_2c60076ccb31e83b_0c8ad932bfc7276e33bc7459b2ebbb26"
        },
        'model_name': 'deepseek-r1-distill-qwen-32b',
        'defaults': {'max_tokens': 8192, 'temperature': 0.0, 'top_p': 0.95, 'stream': False}
    },
    'Qwen3-32B': {
        'url': "http://192.168.230.5:48080/v1/chat/completions",
        'headers': {
            "Content-Type": "application/json",
            "Authorization": "Bearer gpustack_2c60076ccb31e83b_0c8ad932bfc7276e33bc7459b2ebbb26"
        },
        'model_name': 'qwen3-32b',
        'defaults': {'max_tokens': 8192, 'temperature': 0.0, 'top_p': 0.95, 'stream': False}
    }
}

def call_llm(model_name: str, content: str, timeout: int = 1000000, **kwargs):
    """
    调用大模型API并解析响应

    Args:
        model_name: 模型名称 ('DeepSeek-R1-Distill-Qwen-32B' 或 'Qwen3-32B')
        content: 用户输入内容
        timeout: 请求超时时间(秒)，默认1000000秒
        **kwargs: 模型参数 (temperature, max_tokens, top_p等)
    
    Returns:
        tuple: (content_text, full_response) - 内容字符串和完整的API响应对象
    """
    if model_name not in MODELS:
        raise ValueError(f"不支持的模型: {model_name}. 支持的模型: {list(MODELS.keys())}")

    config = MODELS[model_name]
    params = {**config['defaults'], **kwargs}

    body = {
        "model": config['model_name'],
        "messages": [{"role": "user", "content": content}],
        **params
    }

    try:
        response = requests.post(
            config['url'],
            json=body,
            headers=config['headers'],
            verify=False,
            timeout=timeout
        )
        response.raise_for_status()
        logger.info(f"{model_name} API调用成功")

        # 解析响应内容
        try:
            result = response.json()
            content_text = result['choices'][0]['message']['content']
            
            # 返回内容字符串和完整响应对象
            return content_text, result
            
        except (KeyError, IndexError, ValueError) as e:
            logger.error(f"{model_name} 响应解析失败: {e}")
            raise Exception(f"{model_name} 响应解析失败: {e}")

    except requests.exceptions.RequestException as e:
        logger.error(f"{model_name} API调用失败: {e}")
        raise Exception(f"{model_name} API调用失败: {e}")


# 便捷调用函数
@log_llm_invocation(model_name='DeepSeek-R1-Distill-Qwen-32B')
def deepseek_r1_distill_qwen_32b(content: str, **kwargs):
    return call_llm('DeepSeek-R1-Distill-Qwen-32B', content, **kwargs)


@log_llm_invocation(model_name='qwen3-32b')
def qwen3_32b(content: str, **kwargs):
    return call_llm('Qwen3-32B', content, **kwargs)

if __name__ == '__main__':
    print(deepseek_r1_distill_qwen_32b("你好，世界！", max_tokens=50, temperature=0.5,timeout = 100))
    print(qwen3_32b("你好，世界！", max_tokens=50, temperature=0.5,timeout = 100))

