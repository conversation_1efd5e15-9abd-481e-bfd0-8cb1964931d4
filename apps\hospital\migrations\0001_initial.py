# Generated by Django 4.1.5 on 2025-04-22 17:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("doc_autofill", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Hospital",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "hosp_id",
                    models.CharField(
                        db_index=True,
                        max_length=255,
                        unique=True,
                        verbose_name="医院ID（h+hosp_code）",
                    ),
                ),
                (
                    "hosp_code",
                    models.CharField(
                        db_index=True, max_length=255, unique=True, verbose_name="中心编码"
                    ),
                ),
                ("hosp_name", models.CharField(max_length=100, verbose_name="中心名称")),
                (
                    "site_leader_id",
                    models.CharField(
                        max_length=200,
                        null=True,
                        verbose_name="site leader 工号,原始数据多个存储",
                    ),
                ),
                (
                    "site_leader_name",
                    models.CharField(
                        max_length=200,
                        null=True,
                        verbose_name="site leader 名称,原始数据多个存储",
                    ),
                ),
                (
                    "doc_tmpl_center",
                    models.OneToOneField(
                        db_column="doc_tmpl_center_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="hospital",
                        to="doc_autofill.hospdoctmplcenter",
                        verbose_name="医院ID",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
