"""
医疗检验单识别系统的PocketFlow节点定义
"""
import re
import time
from typing import Dict, List, Any, Optional
from pocketflow import Node

from script.test_result_format_ae_ocr.utils import extract_times
from common.clients.llm_client import qwen3_32b
from script.test_result_format_ae_ocr.utils.data_validation import validate_test_item
from script.test_result_format_ae_ocr.utils.ocr_correction import correct_ocr_errors, OCRCorrector
from script.test_result_format_ae_ocr.models import TestItem
from script.test_result_format_ae_ocr.utils.coordinate_extraction import extract_row_coordinates
from common.utils import get_general_prompt_sql
from common.constants import *
from script.test_result_format_ae_ocr.config import LLM_PROMPTS_BASE
import logging
import json
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试从数据库获取提示词，失败时使用默认提示词
try:
    LLM_PROMPTS = get_general_prompt_sql(OCR_EXTRACTION)
    # print(LLM_PROMPTS)
    # LLM_PROMPTS = json.loads(LLM_PROMPTS)
except Exception as e:
    logger.warning(f"从数据库获取提示词失败: {e}，使用默认提示词")
    LLM_PROMPTS = LLM_PROMPTS_BASE['table_format']

class TimeUtils:
    """时间格式处理工具类 - 专门处理OCR识别中常见的无空格时间格式"""

    @staticmethod
    def attempt_time_fix(time_str):
        """修复无空格的时间格式 YYYY-MM-DDHH:MM -> YYYY-MM-DD HH:MM，支持单位数时间组件"""
        import re

        if not time_str or not time_str.strip():
            return time_str

        time_str = time_str.strip()

        # 修复无空格格式 YYYY-MM-DD[H]H:MM:SS -> YYYY-MM-DD HH:MM:SS (支持单位数小时)
        no_space_pattern_with_seconds = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2}:\d{1,2})$'
        match = re.match(no_space_pattern_with_seconds, time_str)
        if match:
            date_part, time_part = match.groups()
            # 标准化时间部分，确保都是两位数
            time_components = time_part.split(':')
            normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
            return f"{date_part} {normalized_time}"

        # 修复无空格格式 YYYY-MM-DD[H]H:MM -> YYYY-MM-DD HH:MM (支持单位数小时)
        no_space_pattern = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2})$'
        match = re.match(no_space_pattern, time_str)
        if match:
            date_part, time_part = match.groups()
            # 标准化时间部分，确保都是两位数
            time_components = time_part.split(':')
            normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
            return f"{date_part} {normalized_time}"

        return time_str

class TextPreprocessingNode(Node):
    """文本预处理节点：从OCR文本中提取各个数据段"""
    
    def prep(self, shared):
        """读取原始OCR文本"""
        return shared.get("ocr_text", "")
    
    def exec(self, ocr_text):
        """执行文本预处理"""
        if not ocr_text:
            raise ValueError("OCR文本不能为空")
        
        logger.info("开始文本预处理...")
        
        # 提取各个部分
        # patient_info_dict = extract_patient_info(ocr_text)
        # metadata_dict = extract_metadata(ocr_text)
        # 现在的提示词不适合处理只有表格的数据
        # test_section = extract_test_section(ocr_text)
        # 大模型直接处理全部的OCR文本
        # test_section = ocr_text
        times = extract_times(ocr_text)
        
        return {
            # "patient_info_dict": patient_info_dict,
            # "metadata_dict": metadata_dict,
            # "test_section": test_section,
            "times": times
        }
    
    def exec_fallback(self, prep_res, exc):
        """处理失败时的回退逻辑"""
        logger.error(f"文本预处理失败: {exc}")
        return {
            # "patient_info_dict": {},
            # "metadata_dict": {},
            # "test_section": prep_res,  # 使用原始文本作为检验数据
            "times": {}
        }

    def post(self, shared, prep_res, exec_res):
        """将预处理结果写入共享存储，优先尝试正则修复"""
        # 保存原始时间
        times = exec_res["times"]
        shared["times"] = times

        # 处理时间格式
        logger.info("开始处理时间格式...")
        processed_times = {}

        for time_type in ['collect_time', 'report_time']:
            original_time = times.get(time_type)
            if original_time and original_time.strip():
                # 尝试修复时间格式
                processed_time = TimeUtils.attempt_time_fix(original_time)
                if processed_time and processed_time.strip():
                    processed_times[time_type] = processed_time
                else:
                    processed_times[time_type] = None
                    logger.warning(f"无法处理时间格式 {time_type}: {original_time}")
            else:
                processed_times[time_type] = None
                logger.warning(f"时间字段 {time_type} 为空或无效: {original_time}")

        # 检查是否有完整的时间数据（需要采集时间和报告时间都有效）
        collect_time = processed_times.get('collect_time')
        report_time = processed_times.get('report_time')
        has_complete_times = (collect_time and collect_time.strip()) and (report_time and report_time.strip())

        # 根据处理结果决定流程
        if has_complete_times:
            # 时间处理成功，直接进入表格化流程
            shared["final_times"] = processed_times
            shared["need_llm_time_extraction"] = False
            logger.info(f"时间处理完成 - collect_time: {collect_time}, report_time: {report_time}")
            return "default"  # 直接进入表格化处理
        else:
            # 时间处理不完整，需要使用LLM
            shared["processed_times"] = processed_times  # 保存处理结果供LLM参考
            shared["need_llm_time_extraction"] = True
            logger.warning(f"时间处理不完整，将启用LLM提取 - collect_time: {collect_time}, report_time: {report_time}")
            return "need_time_extraction"  # 转到LLM时间提取


class LLMTableFormatNode(Node):
    """LLM表格化节点：使用大模型将检验数据转换为结构化表格"""

    def __init__(self, max_retries=3, wait=5, timeout=300):
        """初始化，设置重试参数和超时时间
        
        Args:
            max_retries: 最大重试次数
            wait: 重试间隔时间（秒）
            timeout: 超时时间（秒）
        """
        super().__init__(max_retries=max_retries, wait=wait)
        self.timeout = timeout

    def prep(self, shared):
        """读取检验数据文本和任务信息"""
        ocr_text = shared.get("ocr_text", "")
        task_info = shared.get("task_info", {})
        return {
            "ocr_text": ocr_text,
            "task_info": task_info
        }

    def exec(self, prep_data):
        """使用LLM进行表格化处理，支持超时控制"""
        logger.info(f"开始LLM表格化处理，超时时间: {self.timeout}秒...")

        # 从prep_data中获取OCR文本和任务信息
        ocr_text = prep_data.get("ocr_text", "")
        task_info = prep_data.get("task_info", {})

        # 构建提示词
        prompt = LLM_PROMPTS.format(ocr_text=ocr_text)
        # logger.info(prompt)
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 🎯 使用装饰器的LLM函数，直接传入字典参数
            from common.clients.llm_client import qwen3_32b
            response = qwen3_32b(
                prompt,
                timeout=self.timeout,
                temperature=0.3,
                top_p=0.5,
                category='TEST_REPORT_STRUCTURE',
                **task_info  # 🎯 直接展开字典参数，避免层层传递
            )
            
            # 记录结束时间和统计信息
            end_time = time.time()
            processing_time = end_time - start_time
            self._last_processing_time = processing_time
            
            response_length = len(response) if response else 0
            logger.info(f"LLM调用成功，响应长度: {response_length} 字符，耗时: {processing_time:.2f}秒")
            print(f"LLM响应: {response}")
            return response

        except Exception as e:
            # 检查是否为超时异常
            error_msg = str(e).lower()
            if 'timeout' in error_msg or 'timed out' in error_msg:
                logger.warning(f"LLM调用超时 (超过{self.timeout}秒)，将触发重试: {e}")
            else:
                logger.error(f"LLM调用失败: {e}")
            raise e

    def exec_fallback(self, prep_res, exc):
        """LLM调用失败时的回退处理"""
        # 检查是否为超时异常
        error_msg = str(exc).lower()
        if 'timeout' in error_msg or 'timed out' in error_msg:
            logger.error(f"LLM调用在{self.timeout}秒后超时，已达到最大重试次数: {exc}")
            # 记录超时时间
            self._last_processing_time = self.timeout
        else:
            logger.error(f"LLM调用失败: {exc}")
            # 记录失败时间（如果有的话）
            self._last_processing_time = getattr(self, '_last_processing_time', 0.0)
        # 返回空响应，让后续节点处理
        return ""
    
    def post(self, shared, prep_res, exec_res):
        """保存LLM响应和统计信息"""
        shared["llm_response"] = exec_res
        
        # 记录LLM处理统计信息
        response_length = len(exec_res) if exec_res else 0
        shared["llm_stats"] = {
            "response_length": response_length,
            "processing_time": getattr(self, '_last_processing_time', 0.0)
        }
        
        if exec_res:
            return "default"
        else:
            return "error"


class ParseResponseNode(Node):
    """解析LLM响应节点：从LLM输出中提取JSON Lines数据"""
    
    def prep(self, shared):
        """读取LLM响应"""
        return shared.get("llm_response", "")
    
    def exec(self, llm_response):
        """解析LLM响应，提取JSON Lines数据"""
        logger.info("开始解析LLM响应...")
        
        try:
            if not llm_response:
                result = []
            else:
                result = self._extract_json_lines_from_response(llm_response)
        except Exception as e:
            logger.error(f"解析过程中发生错误: {e}")
            result = []
        
        logger.info(f"解析完成，结果: {len(result)}个项目")
        return result
    
    def _extract_json_lines_from_response(self, response_text: str) -> List[Dict[str, Any]]:
        """从LLM响应中提取JSON Lines数据并转换为标准字段名的字典列表"""
        import json
        
        try:
            content = None
            
            # 策略1: 尝试查找 ```json 或 ``` 标记的内容
            match = re.search(r'```(?:json)?\s*\n(.*?)```', response_text, re.DOTALL)
            if match:
                content = match.group(1).strip()
                # logger.info("找到```json标记的内容")
            
            # 策略2: 如果没有找到```json，尝试查找####后的内容
            if not content:
                match = re.search(r'####\s*\n(.*)', response_text, re.DOTALL)
                if match:
                    content = match.group(1).strip()
                    # logger.info("找到####标记的内容")
            
            # 策略3: 如果没有####，尝试直接解析整个响应
            if not content:
                # 尝试查找JSON对象的模式
                json_pattern = r'\{[^{}]*"tc"[^{}]*\}'
                json_matches = re.findall(json_pattern, response_text)
                if json_matches:
                    content = '\n'.join(json_matches)
                    logger.info("通过模式匹配找到JSON内容")
                else:
                    # 最后尝试：直接使用整个响应文本
                    content = response_text.strip()
                    logger.info("使用整个响应文本进行解析")

            if not content:
                logger.warning("未找到任何可解析的内容。")
                return []

            # 按行分割内容，每行应该是一个JSON对象
            lines = content.split('\n')
            if not lines:
                logger.warning("内容中没有数据行。")
                return []

            # 字段名映射：从LLM输出格式到标准格式
            field_mapping = {
                'tc': 'test_code',
                'tn': 'test_name', 
                'tv': 'test_value',
                'as': 'abnormal_symbol',
                'tu': 'test_unit',
                'rf': 'reference_value'
            }

            items = []
            for i, line in enumerate(lines, start=1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue
                
                # 跳过非JSON行（如思考过程）
                if not line.startswith('{') or not line.endswith('}'):
                    continue
                
                try:
                    # 解析JSON对象
                    json_obj = json.loads(line)
                    
                    # 检查是否包含必要的字段（至少要有tc或tn）
                    if not json_obj.get('tc') and not json_obj.get('tn'):
                        logger.debug(f"跳过无效JSON行 {i}: {line}")
                        continue
                    
                    # 转换字段名并创建标准格式的数据项
                    item = {}
                    missing_fields = []
                    
                    for llm_field, std_field in field_mapping.items():
                        # 获取值，如果key不存在则为空字符串
                        value = json_obj.get(llm_field, '')
                        if not value and llm_field in ['tc', 'tn']:  # 关键字段缺失时记录
                            missing_fields.append(llm_field)
                        item[std_field] = str(value).strip() if value is not None else ''
                    
                    # 记录缺失字段的日志
                    if missing_fields:
                        logger.warning(f"第 {i} 行缺失关键字段 {missing_fields}，已设为空值")
                    
                    items.append(item)
                    logger.debug(f"解析JSON行 {i}: {item}")
                    
                except json.JSONDecodeError as e:
                    # JSON格式错误，直接跳过该行
                    logger.warning(f"第 {i} 行JSON格式错误，跳过: {line}")
                    continue
                    
                except Exception as e:
                    logger.warning(f"第 {i} 行处理失败，跳过: {line}, 错误: {e}")
                    continue
            
            logger.info(f"成功解析 {len(items)} 个JSON对象")
            return items
            
        except Exception as e:
            logger.error(f"提取或解析JSON Lines数据失败: {e}")
            return []
    
    def post(self, shared, prep_res, exec_res):
        """保存解析结果"""
        shared["raw_items"] = exec_res
        logger.info(f"解析完成，提取到 {len(exec_res)} 个检验项目")
        return "default"


class ValidateItemsNode(Node):
    """数据验证节点：验证和处理检验项目数据"""
    
    def __init__(self):
        """初始化验证节点"""
        super().__init__()
        self.ocr_corrector = OCRCorrector()  # 默认启用OCR修复
    
    def prep(self, shared):
        """读取原始项目数据和时间信息"""
        raw_items = shared.get("raw_items", [])
        # 使用最终时间
        final_times = shared.get("final_times", {})
        return {
            "raw_items": raw_items, 
            "times": final_times
        }
    
    def exec(self, input_data):
        """验证和处理检验项目"""
        raw_items = input_data["raw_items"]
        times = input_data["times"]
        
        logger.info(f"开始验证和处理 {len(raw_items)} 个检验项目...")
        
        # 第一步：OCR错误修复
        logger.info("执行OCR错误修复...")
        corrected_items = self.ocr_corrector.correct_items(raw_items)
        
        # 输出修复统计信息
        stats = self.ocr_corrector.get_correction_stats()
        if stats['total_corrections'] > 0:
            logger.info(f"OCR修复完成，总计修复 {stats['total_corrections']} 处错误")
            logger.info(f"各字段修复次数: {stats['field_corrections']}")
        else:
            logger.info("未发现需要修复的OCR错误")
        
        # 重置统计信息为下次使用做准备
        self.ocr_corrector.reset_stats()
        
        # 第二步：数据验证和标准化
        processed_items = []
        for i, item_data in enumerate(corrected_items, start=1):
            try:
                # 确保item_data是字典格式
                if not isinstance(item_data, dict):
                    logger.warning(f"第 {i} 个项目数据格式错误，跳过: {type(item_data)}")
                    continue
                
                # 验证和标准化数据
                try:
                    validated_item = validate_test_item(item_data)
                except Exception as e:
                    logger.warning(f"第 {i} 个项目验证失败，跳过: {e}")
                    continue

                # 添加时间信息
                validated_item['collect_time'] = times.get('collect_time')
                validated_item['report_time'] = times.get('report_time')
                
                # 检查并截断参考区间长度
                reference_value = validated_item.get('reference_value', '')
                if reference_value and len(reference_value) > 20:
                    original_value = reference_value
                    reference_value = reference_value[:20]
                    logger.warning(f"第 {i} 个项目参考区间字段长度超过20，已截断。原始值: '{original_value}', 截断后: '{reference_value}'")
                    validated_item['reference_value'] = reference_value

                # 创建TestItem对象（增强容错性）
                try:
                    test_item = TestItem(
                        test_code=validated_item.get('test_code', ''),
                        test_name=validated_item.get('test_name', ''),
                        test_value=validated_item.get('test_value', ''),
                        abnormal_symbol=validated_item.get('abnormal_symbol', ''),
                        test_flag=validated_item.get('test_flag', ''),
                        test_type=validated_item.get('test_type', ''),
                        test_unit=validated_item.get('test_unit', ''),
                        reference_value=validated_item.get('reference_value', ''),
                        reference_range_min=validated_item.get('reference_range_min'),
                        reference_range_max=validated_item.get('reference_range_max'),
                        collect_time=validated_item.get('collect_time'),
                        report_time=validated_item.get('report_time')
                        
                    )
                    
                    # 保留坐标信息（如果存在）
                    if 'location' in validated_item and validated_item['location']:
                        test_item.location = validated_item['location']
                        logger.debug(f"第 {i} 个项目保留坐标信息: {validated_item['location']}")
                    
                    processed_items.append(test_item)
                    logger.debug(f"第 {i} 个项目处理成功: {validated_item.get('test_code', 'N/A')} - {validated_item.get('test_name', 'N/A')}")
                    
                except Exception as e:
                    logger.warning(f"第 {i} 个项目创建TestItem对象失败: {e}, 数据: {validated_item}")
                    continue

            except Exception as e:
                logger.warning(f"第 {i} 个项目处理失败: {item_data}, 错误: {e}")
                continue
        
        logger.info(f"数据验证完成，处理 {len(raw_items)} -> {len(processed_items)} 个项目")
        return processed_items
    
    def post(self, shared, prep_res, exec_res):
        """保存处理后的检验项目"""
        shared["processed_items"] = exec_res
        logger.info(f"数据验证完成，有效项目: {len(exec_res)}")
        
        # 如果没有有效项目，记录详细信息
        if len(exec_res) == 0:
            raw_items = prep_res.get("raw_items", [])
            logger.error(f"数据验证后没有有效项目！原始项目数: {len(raw_items)}")
            if raw_items:
                logger.error(f"原始项目示例: {raw_items[0] if raw_items else 'None'}")
        
        return "default"


class GenerateResultNode(Node):
    """生成最终结果节点：组装ProcessingResult对象"""
    def prep(self, shared):
        return None
    def post(self, shared, prep_res, exec_res):
        return "default"


class ErrorHandlingNode(Node):
    """错误处理节点：当LLM调用失败时进行兜底处理"""
    
    def prep(self, shared):
        """读取原始OCR文本进行兜底处理"""
        return shared.get("ocr_text", "")
    
    def exec(self, ocr_text):
        """简单的兜底逻辑：尝试使用规则匹配提取信息"""
        logger.warning("进入错误处理模式，使用规则匹配兜底...")
        
        # 这里可以实现一些简单的规则匹配逻辑
        # 目前返回空结果，表示处理失败
        return []
    
    def post(self, shared, prep_res, exec_res):
        """保存兜底处理结果"""
        shared["error_handled"] = True
        shared["raw_items"] = exec_res
        logger.warning(f"错误处理完成，提取到 {len(exec_res)} 个项目")
        return "default" 
    

class LLMTimeExtractionNode(Node):
    """LLM时间提取节点：使用大模型从OCR文本中提取时间并合并结果"""
    
    def __init__(self, max_retries=3, wait=5, timeout=300):
        """初始化，设置重试参数和超时时间
        
        Args:
            max_retries: 最大重试次数
            wait: 重试间隔时间（秒）
            timeout: 超时时间（秒），默认100秒
        """
        super().__init__(max_retries=max_retries, wait=wait)
        self.timeout = timeout
    
    def prep(self, shared):
        """读取OCR文本和现有的正则匹配结果"""
        ocr_text = shared.get("ocr_text", "")
        if not ocr_text:
            raise ValueError("OCR文本不能为空")

        # 获取原始正则提取结果和处理结果
        regex_times = shared.get("times", {})
        processed_times = shared.get("processed_times", {})

        return {
            "ocr_text": ocr_text,
            "regex_times": regex_times,
            "processed_times": processed_times
        }

    def exec(self, prep_data):
        """使用LLM提取时间信息，支持超时控制"""
        logger.info(f"开始LLM时间提取，超时时间: {self.timeout}秒...")

        # 构建提示词
        prompt = LLM_PROMPTS_BASE['time_extraction'].format(ocr_text=prep_data["ocr_text"])

        try:
            # 使用 self.timeout 的值动态地应用超时
            # 即时创建一个被装饰器包装的函数
            # timed_call_llm = timeout_decorator.timeout(
            #     self.timeout,
            #     timeout_exception=TimeoutError,  # 确保抛出正确的超时异常
            #     use_signals=False  # 关键：使用线程模式，确保跨平台兼容性
            # )(call_llm)
            # 🎯 时间提取是内部辅助功能，不需要记录日志
            response = qwen3_32b(
                prompt, 
                temperature=0.3,
                top_p=0.5,
                timeout=self.timeout,
                enable_logging=False  # 🎯 禁用日志记录
            )


            # 调用这个带超时的函数
            # response = timed_call_llm(prompt)

            print("时间提取**********")
            print(response)

            return response

        except Exception as e:
            error_msg = str(e).lower()
            if 'timeout' in error_msg or 'timed out' in error_msg:
                logger.warning(f"LLM调用超时 (超过{self.timeout}秒)，将触发重试: {e}")
            else:
                logger.error(f"LLM调用失败: {e}")

    def exec_fallback(self, prep_res, exc):
        """LLM调用失败时的回退处理"""
        if isinstance(exc, TimeoutError):
            logger.error(f"LLM时间提取在{self.timeout}秒后超时，已达到最大重试次数")
        else:
            logger.error(f"LLM时间提取失败: {exc}")
        # 返回空响应
        return ""

    def post(self, shared, prep_res, exec_res):
        """解析LLM结果并与正则结果智能合并"""
        regex_times = prep_res["regex_times"]
        processed_times = prep_res["processed_times"]
        llm_times = self._parse_llm_time_response(exec_res)

        # 智能合并时间结果：优先使用处理后的时间，失败的部分用LLM结果
        final_times = {}

        # 合并采集时间
        collect_time, collect_source = self._merge_single_time(
            processed_times.get('collect_time'),  # 处理后结果
            llm_times.get('collect_time'),        # LLM结果
            'collect_time'
        )
        final_times['collect_time'] = collect_time

        # 合并报告时间
        report_time, report_source = self._merge_single_time(
            processed_times.get('report_time'),   # 处理后结果
            llm_times.get('report_time'),         # LLM结果
            'report_time'
        )
        final_times['report_time'] = report_time
        
        # 保存结果
        shared["final_times"] = final_times
        shared["llm_times"] = llm_times  # 保留LLM原始结果用于调试
        shared["regex_times"] = regex_times  # 保留正则原始结果用于调试
        shared["processed_times"] = processed_times  # 保留处理结果用于调试

        logger.info(f"时间合并完成: collect_time={final_times.get('collect_time')}, report_time={final_times.get('report_time')}")
        logger.info(f"时间来源 - 采集时间: {collect_source}, 报告时间: {report_source}")

        return "continue_processing"  # 返回到表格化流程

    def _merge_single_time(self, processed_time, llm_time, time_type):
        """合并单个时间字段（优先使用处理后结果），返回(最终时间, 来源)"""
        # 1. 优先使用处理后的结果（如果存在且有效）
        if processed_time and processed_time.strip():
            logger.info(f"使用处理后的{time_type}: {processed_time}")
            return processed_time, "正则处理"

        # 2. 处理失败，使用LLM结果
        if llm_time and llm_time.strip():
            logger.info(f"处理失败，使用LLM{time_type}: {llm_time}")
            return llm_time, "LLM"
        
        # 3. 两个都没有有效时间
        logger.warning(f"{time_type}无有效时间可用")
        return None, "无"
    
    def _parse_llm_time_response(self, response: str) -> Dict[str, Optional[str]]:
        """解析LLM响应中的时间信息"""
        import json
        import re
        
        times = {
            'collect_time': None,
            'report_time': None
        }
        
        if not response:
            return times
        
        try:
            # 尝试提取JSON格式的响应
            json_match = re.search(r'```json\s*\n(.*?)```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1).strip()
                data = json.loads(json_str)

                # 提取时间字段
                collect_time = data.get('collect_time', '').strip()
                report_time = data.get('report_time', '').strip()
                
                # 验证时间格式
                if collect_time and TimeUtils.is_valid_time_format(collect_time):
                    times['collect_time'] = collect_time
                
                if report_time and TimeUtils.is_valid_time_format(report_time):
                    times['report_time'] = report_time
                    
            else:
                logger.warning("LLM响应中未找到有效的JSON格式")
                
        except (json.JSONDecodeError, KeyError) as e:
            logger.warning(f"LLM时间响应解析失败: {e}")
        except Exception as e:
            logger.error(f"LLM时间响应处理异常: {e}")
            
        return times


class CoordinateExtractionNode(Node):
    """坐标提取节点：从OCR数据中提取行坐标信息"""

    def __init__(self, similarity_threshold: float = 0.6):
        """
        初始化坐标提取节点

        Args:
            similarity_threshold: 文本匹配相似度阈值
        """
        super().__init__()
        self.similarity_threshold = similarity_threshold

    def prep(self, shared):
        """读取原始测试项目和OCR数据，并进行OCR修正"""
        # 使用raw_items（来自ParseResponseNode, 没有被修改的大模型结果）
        raw_items = shared.get("raw_items", [])
        words_block_list = shared.get("words_block_list", [])
        
        # 在坐标提取前先修正OCR错误，确保test_name准确性
        ocr_corrector = OCRCorrector()
        corrected_items = ocr_corrector.correct_items(raw_items)
        
        logger.info(f"OCR修正完成，处理了 {len(corrected_items)} 个项目")
        
        return {
            "items": corrected_items,
            "words_block_list": words_block_list,
        }

    def exec(self, input_data):
        """执行坐标提取"""
        items = input_data["items"]
        words_block_list = input_data["words_block_list"]


        if not items or not words_block_list:
            logger.warning("测试项目或OCR数据为空，跳过坐标提取")
            return []

        try:
            # 🎯 输出调试信息：检查测试项目和OCR块
            logger.info(f"测试项目样例:")
            for i, item in enumerate(items[:3]):  # 只显示前3个
                # 处理原始字典格式
                logger.info(f"  项目 {i}: {item.get('test_name', 'N/A')} | {item.get('test_value', 'N/A')} | {item.get('test_unit', 'N/A')}")


            # 提取行坐标并添加到items中
            items_with_coordinates = extract_row_coordinates(
                items,
                words_block_list,
                similarity_threshold=0.3
            )

            logger.info(f"坐标提取完成，为 {len(items_with_coordinates)} 个项目添加坐标")
            return items_with_coordinates

        except Exception as e:
            logger.error(f"坐标提取失败: {e}")
            return []

    def post(self, shared, prep_res, exec_res):
        """保存坐标提取结果并更新测试项目"""
        items_with_coordinates = exec_res
        original_items = prep_res["items"]

        # 保存带有坐标的items
        shared["raw_items"] = items_with_coordinates
        
        # 🎯 添加调试日志
        logger.info(f"坐标提取节点 - 收到带坐标的项目: {len(items_with_coordinates)} 个")
        logger.info(f"坐标提取节点 - 原始项目数: {len(original_items)} 个")
        
        # 统计有多少个项目成功添加了坐标
        items_with_location = 0
        for item in items_with_coordinates:
            if "location" in item and item["location"]:
                items_with_location += 1
                logger.info(f"项目 {item.get('test_name', 'N/A')} 坐标: {item['location']}")
        
        logger.info(f"已将坐标信息添加到 {items_with_location} 个测试项目中")

        return "default"



