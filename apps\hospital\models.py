from django.db import models
from common.models import BaseModel, BaseFileModel


class Hospital(BaseModel):
    hosp_id = models.CharField(max_length=255, verbose_name="医院ID（h+hosp_code）", db_index=True, unique=True)
    hosp_code = models.Char<PERSON>ield(max_length=255, verbose_name="中心编码", db_index=True, unique=True)
    hosp_name = models.CharField(max_length=100, verbose_name="中心名称")
    site_leader_id = models.CharField(max_length=200, null=True, verbose_name="site leader 工号,原始数据多个存储")
    site_leader_name = models.Char<PERSON>ield(max_length=200, null=True, verbose_name="site leader 名称,原始数据多个存储")

    doc_tmpl_center = models.OneToOneField(
        'doc_autofill.HospDocTmplCenter',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='hospital',
        to_field='id',
        db_column='doc_tmpl_center_id',
        verbose_name="医院ID",
        db_index=True
    )

    class Meta:
        db_table = 'hospital_info'
        verbose_name = "医院信息表"
        verbose_name_plural = verbose_name