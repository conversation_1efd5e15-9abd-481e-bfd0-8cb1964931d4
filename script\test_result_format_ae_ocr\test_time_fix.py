#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间格式修复功能
"""

import sys
import os
import re

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class TimeUtils:
    """时间格式处理工具类 - 专门处理OCR识别中常见的无空格时间格式"""

    @staticmethod
    def attempt_time_fix(time_str):
        """修复无空格的时间格式 YYYY-MM-DDHH:MM -> YYYY-MM-DD HH:MM，支持单位数时间组件"""
        import re

        if not time_str or not time_str.strip():
            return time_str

        time_str = time_str.strip()

        # 修复无空格格式 YYYY-MM-DD[H]H:MM:SS -> YYYY-MM-DD HH:MM:SS (支持单位数小时)
        no_space_pattern_with_seconds = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2}:\d{1,2})$'
        match = re.match(no_space_pattern_with_seconds, time_str)
        if match:
            date_part, time_part = match.groups()
            # 标准化时间部分，确保都是两位数
            time_components = time_part.split(':')
            normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
            return f"{date_part} {normalized_time}"
        
        # 修复无空格格式 YYYY-MM-DD[H]H:MM -> YYYY-MM-DD HH:MM (支持单位数小时)
        no_space_pattern = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2})$'
        match = re.match(no_space_pattern, time_str)
        if match:
            date_part, time_part = match.groups()
            # 标准化时间部分，确保都是两位数
            time_components = time_part.split(':')
            normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
            return f"{date_part} {normalized_time}"
        
        return time_str

def test_time_fix():
    """测试时间修复功能"""
    print("=" * 60)
    print("时间格式修复测试")
    print("=" * 60)
    
    test_cases = [
        ("2024-06-278:00:15", "问题案例：单位数小时+秒"),
        ("2024-06-2708:00:15", "标准案例：双位数时间"),
        ("2024-06-279:30:45", "单位数小时"),
        ("2024-06-2710:5:30", "单位数分钟"),
        ("2024-06-2710:30:5", "单位数秒"),
        ("2024-06-279:5:5", "全部单位数"),
        ("2024-06-279:30", "无秒格式"),
        ("2024-06-2710:30", "标准无秒格式"),
        ("2024-06-27 08:00:15", "已有空格的正确格式"),
        ("invalid-time", "无效格式测试"),
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (test_case, description) in enumerate(test_cases, 1):
        result = TimeUtils.attempt_time_fix(test_case)
        
        # 检查是否修复成功（包含空格且格式正确）
        is_fixed = ' ' in result and result != test_case
        is_valid_format = re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$', result)
        
        status = "✅ 成功" if (is_fixed or result == test_case) and is_valid_format else "❌ 失败"
        if is_fixed or is_valid_format:
            success_count += 1
            
        print(f"{i:2d}. {description}")
        print(f"    输入: {test_case}")
        print(f"    输出: {result}")
        print(f"    状态: {status}")
        print()
    
    print("=" * 60)
    print(f"测试结果: {success_count}/{total_count} 成功")
    print("=" * 60)
    
    # 特别测试原始错误案例
    print("\n🎯 原始错误案例测试:")
    original_error = "2024-06-278:00:15"
    fixed_result = TimeUtils.attempt_time_fix(original_error)
    print(f"原始错误: {original_error}")
    print(f"修复结果: {fixed_result}")
    
    # 验证修复结果是否符合数据库要求的格式
    expected_format = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
    is_db_compatible = re.match(expected_format, fixed_result) is not None
    print(f"数据库兼容: {'✅ 是' if is_db_compatible else '❌ 否'}")
    
    return success_count == total_count

if __name__ == "__main__":
    success = test_time_fix()
    sys.exit(0 if success else 1)
