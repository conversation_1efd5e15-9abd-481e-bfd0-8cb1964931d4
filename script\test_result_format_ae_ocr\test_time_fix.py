#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间格式修复功能
"""

import sys
import os
import re

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class TimeUtils:
    """时间格式处理工具类 - 专门处理OCR识别中常见的无空格时间格式"""

    @staticmethod
    def attempt_time_fix(time_str):
        """
        修复无空格的时间格式 YYYY-MM-DDHH:MM -> YYYY-MM-DD HH:MM，支持单位数时间组件
        增强版本：添加Django DateTimeField兼容性验证，无法修复时返回None
        """
        import re

        if not time_str or not time_str.strip():
            return None

        time_str = time_str.strip()

        # 首先检查是否已经是有效的Django时间格式
        if TimeUtils.is_valid_django_datetime(time_str):
            return time_str

        # 尝试修复各种常见的时间格式问题
        fixed_time = None

        # 修复1: 无空格格式 YYYY-MM-DD[H]H:MM:SS -> YYYY-MM-DD HH:MM:SS (支持单位数时间组件)
        no_space_pattern_with_seconds = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2}:\d{1,2})$'
        match = re.match(no_space_pattern_with_seconds, time_str)
        if match:
            date_part, time_part = match.groups()
            # 标准化时间部分，确保都是两位数
            time_components = time_part.split(':')
            normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
            fixed_time = f"{date_part} {normalized_time}"

        # 修复2: 无空格格式 YYYY-MM-DD[H]H:MM -> YYYY-MM-DD HH:MM (支持单位数时间组件)
        if not fixed_time:
            no_space_pattern = r'^(\d{4}-\d{2}-\d{2})(\d{1,2}:\d{1,2})$'
            match = re.match(no_space_pattern, time_str)
            if match:
                date_part, time_part = match.groups()
                # 标准化时间部分，确保都是两位数
                time_components = time_part.split(':')
                normalized_time = ':'.join([comp.zfill(2) for comp in time_components])
                fixed_time = f"{date_part} {normalized_time}"

        # 修复3: 处理其他常见格式问题（可以根据需要扩展）
        if not fixed_time:
            # 尝试处理中文字符或其他分隔符
            cleaned = re.sub(r'[年月日时分秒]', '', time_str)
            cleaned = re.sub(r'[：]', ':', cleaned)
            cleaned = re.sub(r'\s+', ' ', cleaned)
            if cleaned != time_str:
                # 递归调用自己处理清理后的字符串
                return TimeUtils.attempt_time_fix(cleaned)

        # 验证修复结果
        if fixed_time and TimeUtils.is_valid_django_datetime(fixed_time):
            return fixed_time

        # 无法修复，返回None
        return None

    @staticmethod
    def is_valid_django_datetime(time_str):
        """验证时间字符串是否符合Django DateTimeField要求"""
        if not time_str or not isinstance(time_str, str):
            return False

        try:
            # 使用标准库的datetime来验证，模拟Django的行为
            from datetime import datetime
            # 尝试常见的时间格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M',
                '%Y-%m-%d',
            ]

            for fmt in formats:
                try:
                    datetime.strptime(time_str, fmt)
                    return True
                except ValueError:
                    continue
            return False
        except Exception:
            return False

    @staticmethod
    def validate_and_fix_datetime(time_str, field_name="time"):
        """
        验证并修复时间字符串，确保符合Django DateTimeField要求

        Args:
            time_str: 待验证的时间字符串
            field_name: 字段名称，用于日志记录

        Returns:
            str or None: 有效的时间字符串或None
        """
        if not time_str or not str(time_str).strip():
            return None

        time_str = str(time_str).strip()

        # 首先检查是否已经是有效格式
        if TimeUtils.is_valid_django_datetime(time_str):
            return time_str

        # 尝试修复
        fixed_time = TimeUtils.attempt_time_fix(time_str)

        if fixed_time and TimeUtils.is_valid_django_datetime(fixed_time):
            return fixed_time

        # 修复失败，返回None
        return None

def test_time_fix():
    """测试时间修复功能"""
    print("=" * 60)
    print("时间格式修复测试")
    print("=" * 60)
    
    test_cases = [
        ("2024-06-278:00:15", "问题案例：单位数小时+秒"),
        ("2024-06-2708:00:15", "标准案例：双位数时间"),
        ("2024-06-279:30:45", "单位数小时"),
        ("2024-06-2710:5:30", "单位数分钟"),
        ("2024-06-2710:30:5", "单位数秒"),
        ("2024-06-279:5:5", "全部单位数"),
        ("2024-06-279:30", "无秒格式"),
        ("2024-06-2710:30", "标准无秒格式"),
        ("2024-06-27 08:00:15", "已有空格的正确格式"),
        ("invalid-time", "无效格式测试"),
        ("2024年06月27日08时00分15秒", "中文格式测试"),
        ("2024-06-27：08：00：15", "中文冒号测试"),
        ("", "空字符串测试"),
        (None, "None值测试"),
        ("2024-13-45 25:70:80", "无效日期时间测试"),
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (test_case, description) in enumerate(test_cases, 1):
        result = TimeUtils.attempt_time_fix(test_case)

        # 检查结果状态
        if result is None:
            # 对于无效输入，返回None是正确的
            is_success = test_case in [None, "", "invalid-time", "2024-13-45 25:70:80"]
            status = "✅ 成功（正确返回None）" if is_success else "❌ 失败（应该修复但返回了None）"
        else:
            # 对于有效输出，检查格式是否正确
            is_valid_format = TimeUtils.is_valid_django_datetime(result)
            status = "✅ 成功" if is_valid_format else "❌ 失败（格式仍然无效）"
            is_success = is_valid_format

        if is_success:
            success_count += 1

        print(f"{i:2d}. {description}")
        print(f"    输入: {test_case}")
        print(f"    输出: {result}")
        print(f"    状态: {status}")
        print()
    
    print("=" * 60)
    print(f"测试结果: {success_count}/{total_count} 成功")
    print("=" * 60)
    
    # 特别测试原始错误案例
    print("\n🎯 原始错误案例测试:")
    original_error = "2024-06-278:00:15"
    fixed_result = TimeUtils.attempt_time_fix(original_error)
    print(f"原始错误: {original_error}")
    print(f"修复结果: {fixed_result}")

    # 验证修复结果是否符合数据库要求的格式
    if fixed_result:
        is_db_compatible = TimeUtils.is_valid_django_datetime(fixed_result)
        print(f"数据库兼容: {'✅ 是' if is_db_compatible else '❌ 否'}")
    else:
        print("数据库兼容: ❌ 修复失败，返回None")

    # 测试validate_and_fix_datetime方法
    print("\n🔧 validate_and_fix_datetime 方法测试:")
    test_validate_cases = [
        "2024-06-278:00:15",
        "2024-06-27 08:00:15",
        "invalid-time",
        None
    ]

    for case in test_validate_cases:
        result = TimeUtils.validate_and_fix_datetime(case, "test_field")
        print(f"  输入: {case} -> 输出: {result}")

    return success_count >= (total_count * 0.8)  # 80%成功率即可

if __name__ == "__main__":
    success = test_time_fix()
    sys.exit(0 if success else 1)
