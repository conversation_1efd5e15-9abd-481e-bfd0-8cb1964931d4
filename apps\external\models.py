from django.db import models
from common.models import BaseModel, BaseFileModel


class MaskingOriginalFile(BaseModel, BaseFileModel):
    """外部系统文件"""

    class Meta:
        verbose_name = "外部系统文件"
        verbose_name_plural = "外部系统文件"
        db_table = 'external_masking_original_file'


class MaskingMaskedFile(BaseModel, BaseFileModel):
    """外部系统脱敏文件"""

    class Meta:
        verbose_name = "外部系统脱敏文件"
        verbose_name_plural = "外部系统脱敏文件"
        db_table = 'external_masking_masked_file'


class MaskingTask(BaseModel):
    """
    外部文件脱敏任务
    """
    TODO = 'TODO'
    IN_PROGRESS = 'IN_PROGRESS'
    COMPLETED = 'COMPLETED'
    CANCELLED = 'CANCELLED'
    ERROR = 'ERROR'
    STATUS_CHOICES = [(TODO, '待办'), (IN_PROGRESS, '进行中'), (COMPLETED, '已完成'), (CANCELLED, '已取消'), (ERROR, '执行错误')]

    task_id = models.CharField(max_length=255, verbose_name="任务ID", unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=TODO, verbose_name="任务状态")
    note = models.CharField(max_length=255, null=True, blank=True, verbose_name="备注")
    start_time = models.DateTimeField(auto_now_add=True, verbose_name="开始时间")
    end_time = models.DateTimeField(null=True, verbose_name="结束时间")
    meta = models.JSONField(null=True, verbose_name="附加的业务参数")
    extra = models.JSONField(null=True, verbose_name="内部业务参数")
    callback_url = models.CharField(max_length=512, null=True, verbose_name="回调URL")
    callback_time = models.DateTimeField(null=True, verbose_name="回调时间")
    callback_status = models.CharField(max_length=20, null=True, verbose_name="回调状态")
    test_result = models.JSONField(null=True, verbose_name="结构化提取结果")

    original_file = models.OneToOneField(
        MaskingOriginalFile,
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_medical_info',
        to_field='id',
        db_column='original_file_id',
        verbose_name="外部系统文件ID",
        db_index=True
    )

    masked_file = models.OneToOneField(
        MaskingMaskedFile,
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_medical_info',
        to_field='id',
        db_column='masked_file_id',
        verbose_name="外部系统脱敏文件",
        db_index=True,
        null=True
    )

    class Meta:
        db_table = 'external_masking_task'
        verbose_name = "外部文件脱敏任务"
        verbose_name_plural = verbose_name
