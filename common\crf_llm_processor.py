import io
import os
import re
import uuid
import base64
import json
import math
import time
import subprocess
from openpyxl.utils.dataframe import dataframe_to_rows
from pendulum import instance
import retry
from copy import deepcopy
from django.db import transaction, connection
import requests
from PIL import Image
import fitz  # PyMuPDF库用于处理PDF
import concurrent.futures
from pathlib import Path
import pandas as pd
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from openpyxl import Workbook
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.styles import Pattern<PERSON>ill, Alignment, Font
from openpyxl.utils import get_column_letter
from django.conf import settings
from common.utils import get_general_prompt_sql, get_project_prompt_info
from common.ocr_tools import hw_ocr_general_text, format_ocr_text, extract_text_from_folder
from common.llm_tools import (
    deepseek_r1_llama_70b, deepseek_r1_qwen_32b, get_json_from_ai_reply,
    pangu_v35_128k, pangu_reasoner, get_think_text_from_ai_reply, get_reasoning_content_from_ai_reply,qwen3_32b,get_findall_json_from_ai_reply,qwen3_8b
)
from common.prompts import CRF_RXTRACT_PROMPT, CRF_RESULT_COMPARE_PROMPT, CRF_RESULT_COMPARE_PROMPT_2
from datetime import datetime
from apps.medical_collection.models import MedicalCollectionTask,MedicalCollectionCrfResult,MedicalCollectionFile
from apps.system.models import ModelInvocationLog

def activate_conn(connection):
    try:
        connection.connect()
        if not connection.is_usable():
            connection.close()
            connection.connect()
    except:
        pass
    return connection


# 定义条件对应的背景色
red_fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")      # 浅红色
yellow_fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")   # 浅黄色

# 定义对齐方式：默认中心对齐和左对齐
center_alignment = Alignment(horizontal="center", vertical="center")
left_alignment = Alignment(horizontal="left", vertical="center")
wrap_left_alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)

# 字体设置
bold_font = Font(bold=True)
gray_font = Font(color="808080")  # 50%灰色


# 将目标结果转换为可编辑的json格式
def transform_data_back(input_data):
    output = []

    for item in input_data:
        title = item["title"]
        is_handwritten = "手写体" in title
        if title.startswith("【") or title.startswith("("):
            pass  # 开头是括号就不取了
        else:
            # 先处理中括号，再处理小括号
            title = re.sub(r"【.*?】", "", title)  # 移除中括号及其中内容
            title = re.sub(r"（.*?）", "", title) 
        font = True if is_handwritten else False

        new_item = {
            "title": title,
            "font": font,
            "fields": []
        }

        for field in item["fields"]:
            key = field["key"]
            value_b = field.get("value_a", "")
            value_c = field.get("value_b", "")

            # 判断 check 字段
            check = True if "不一致" in field["check"] or "需人工审核" in field["check"] else False

            # 构造 res_history
            res_history = [
                {"key": key, "value": value_b},
                {"key": key, "value": value_c}
            ]

            # 构建新 field
            new_field = {
                "key": key,
                "value": value_b,  # 默认取 value_b
                "res_history": res_history
            }

            # 只有当 check 是 red 时才添加
            new_field["check"] = check


            new_field['modify'] = False

            new_item["fields"].append(new_field)

        output.append(new_item)

    return json.dumps(output,ensure_ascii=False)


def crf_json_to_md_text(data):
    # 转换为 Markdown 格式
    markdown_lines = []

    for section in data:
        markdown_lines.append(f"## {section['title']}")
        for field in section['fields']:
            key = field.get("key", "").strip()
            value = field.get("desc", "").strip()
            markdown_lines.append(f"- {key}：{value}")
        markdown_lines.append("")  # 空行分隔段落

    # 输出 Markdown 文本
    markdown_text = "\n".join(markdown_lines)
    return markdown_text

# # 将excel转为json格式
# def crf_file_to_json(filename):
#     df = pd.read_excel(filename)

#     columns = df.columns.tolist()

#     # 遍历表头，从第二个元素开始
#     for i in range(1, len(columns)):
#         if 'Unnamed' in columns[i]:
#             columns[i] = columns[i - 1]

#     # 更新表头
#     df.columns = columns

#     temp_result = {}
#     df = df.fillna("")
#     for col_index, col in enumerate(df.columns):
#         key = str(df.iloc[0, col_index])
#         try:
#             desc = str(df.iloc[1, col_index])
#         except:
#             desc = ''
#         # value = str(df.iloc[1, col_index])
#         field = {
#             "key": key,
#             "desc": desc,
#             "value": '',
#             "source": ''
#         }
#         if col in temp_result:
#             temp_result[col]['fields'].append(field)
#         else:
#             temp_result[col] = {
#                 "title": col,
#                 "fields": [field]
#             }
#     result = list(temp_result.values())
#     return result

def convert_to_json_format(data):
    """
    将数据库查询结果转换为指定的 JSON 格式。
    
    :param data: 数据库查询结果，每个元素是一个包含 (source_desc, edc_field, edc_field_desc) 的元组
    :return: 转换后的 JSON 格式数据
    """
    result = []
    current_title = None
    current_group = {}

    for source_desc, edc_field, edc_field_desc in data:
        if source_desc != current_title:
            if current_title is not None:
                result.append(current_group)
            current_title = source_desc
            current_group = {
                "title": current_title,
                "fields": []
            }

        field = {
            "key": edc_field,
            "desc": edc_field_desc if edc_field_desc is not None else "", 
            "value": "",
            "source": ""
        }
        current_group["fields"].append(field)

    if current_title is not None:
        result.append(current_group)

    return result


def crf_json_to_file(data, output_filename):
    columns = []
    values = []

    # 遍历 JSON 数据
    for item in data:
        title = item["title"]
        for field in item["fields"]:
            key = field["key"]
            value = field["value"]
            columns.append(title)
            values.extend([key, value])

    # 分割值列表为两行
    row1 = values[::2]
    row2 = values[1::2]

    df = pd.DataFrame([row1, row2], columns=columns)
    df.to_excel(output_filename, index=False)
    # 加载内存中的 Excel 文件
    wb = load_workbook(output_filename)
    ws = wb.active
    # 开始合并单元格操作
    start_col = 1
    current_col_name = ws.cell(row=1, column=start_col).value
    for col in range(2, len(columns) + 1):
        col_name = ws.cell(row=1, column=col).value
        if col_name == current_col_name:
            continue
        else:
            if col - start_col > 1:
                start_letter = get_column_letter(start_col)
                end_letter = get_column_letter(col - 1)
                ws.merge_cells(f'{start_letter}1:{end_letter}1')
            start_col = col
            current_col_name = col_name

    # 处理最后一组相同列名的情况
    if len(columns) - start_col + 1 > 1:
        start_letter = get_column_letter(start_col)
        end_letter = get_column_letter(len(columns))
        ws.merge_cells(f'{start_letter}1:{end_letter}1')

    wb.save(output_filename)
    return output_filename


def extract_task(ocr_text, crf_json, task_id, page_count=0):
    row_crf_json = deepcopy(crf_json)
    crf_desc = crf_json_to_md_text(crf_json)

    for c in crf_json:
        for f in c['fields']:
            if 'desc' in f:
                f.pop('desc')

    prompt = CRF_RXTRACT_PROMPT.render(
        crf_desc=crf_desc,
        ocr_text=ocr_text,
        crf_json=json.dumps(crf_json, indent=4, ensure_ascii=False)
    )

    print('*' * 100)
    print('提取Prompt')
    print(prompt)
    print('*' * 100)

    start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    model_configs = [
        {'name': 'DeepSeek-R1-Distill-Qwen-32B', 'func': deepseek_r1_qwen_32b, 'key': 'a'},
        {'name': 'qwen3-32b', 'func': qwen3_32b, 'key': 'b'}
    ]

    model_results = {}

    # 定义一个带重试的模型调用函数
    def call_model_with_retry(model_func, model_key, model_name, tries=3, delay=1):
        attempt = 0
        while attempt < tries:
            try:
                response = model_func(prompt)
                print(f"{model_name} 调用成功")
                print(response.text)

                output_text = response.json()
                tokens = output_text.get("usage", {})

                think_text = get_think_text_from_ai_reply(response)
                # 构建日志数据
                activate_conn(connection)

                task = MedicalCollectionTask.objects.filter(id=task_id, delete_flag=0).first()
                result_log = {
                    'task_id': task_id,
                    'create_user': task.create_user if task else None,
                    'create_name': task.create_name if task else None,
                    'category': 'CRF_GENERATION',
                    'model_name': model_name,
                    'start_time': start_time,
                    'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'input_text': prompt,
                    'think_text': think_text,
                    'output_text': output_text,
                    'prompt_tokens': tokens.get('prompt_tokens', 0),
                    'completion_tokens': tokens.get('completion_tokens', 0),
                    'business_id': task.patient_id if task else None,
                    'page_count': page_count  # 添加页数字段
                }

                # 插入日志
                if model_key == 'b':
                    with transaction.atomic():
                        ModelInvocationLog.objects.create(**result_log)
                else:
                    ModelInvocationLog.objects.create(**result_log)

                # 解析结果
                llm_data = get_findall_json_from_ai_reply(response)
                for group in llm_data:
                    if model_key == 'a':
                        group['think_content'] = think_text
                    elif model_key == 'b':
                        group['think_content'] = think_text

                return {
                    'data': llm_data,
                    'think_text': think_text,
                    'success': True
                }

            except Exception as e:
                import traceback
                traceback.print_exc()
                attempt += 1
                print(f"[{model_name}] 第 {attempt} 次调用失败: {e}")
                if attempt >= tries:
                    return {
                        'data': None,
                        'think_text': '',
                        'success': False,
                        'error': str(e)
                    }
                time.sleep(delay)

    # 并发执行所有模型
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future_to_model = {}
        futures = []

        for config in model_configs:
            future = executor.submit(call_model_with_retry, config['func'], config['key'], config['name'])
            future_to_model[future] = config
            futures.append(future)

        # 处理结果
        for future in concurrent.futures.as_completed(futures):
            config = future_to_model[future]
            model_key = config['key']
            model_name = config['name']

            try:
                result = future.result()
                model_results[model_key] = result
                print(f"{model_name} 数据解析{'成功' if result['success'] else '失败'}")
            except Exception as e:
                print(f"处理 {model_name} 结果时出错: {e}")
                model_results[model_key] = {
                    'data': None,
                    'think_text': '',
                    'success': False,
                    'error': str(e)
                }

    # 创建映射表
    llm_a_group_map = {}
    llm_a_field_map = {}
    llm_b_group_map = {}
    llm_b_field_map = {}


    # 处理模型A结果 (DeepSeek)
    if model_results.get('a', {}).get('success', False):
        llm_a_data = model_results['a']['data']
        for g_seq, group in enumerate(llm_a_data):
            llm_a_group_map[g_seq] = group
            for f_seq, field in enumerate(group['fields']):
                llm_a_field_map["-".join([str(g_seq), str(f_seq)])] = field


    # 处理模型b结果 (Qwen3)
    if model_results.get('b', {}).get('success', False):
        llm_b_data = model_results['b']['data']
        for g_seq, group in enumerate(llm_b_data):
            llm_b_group_map[g_seq] = group
            for f_seq, field in enumerate(group['fields']):
                llm_b_field_map["-".join([str(g_seq), str(f_seq)])] = field

    # 合并结果到原始结构
    for g_seq, group in enumerate(row_crf_json):
        group['think_content'] = llm_a_group_map.get(g_seq, {}).get('think_content', '')
        group['qwen3_think_content'] = llm_b_group_map.get(g_seq, {}).get('think_content', '')

        for f_seq, field in enumerate(group['fields']):
            f_key = "-".join([str(g_seq), str(f_seq)])

            field['value_a'] = llm_a_field_map.get(f_key, {}).get('value', '') or ''
            field['source_a'] = llm_a_field_map.get(f_key, {}).get('source', '') or ''

            field['value_b'] = llm_b_field_map.get(f_key, {}).get('value', '') or ''
            field['source_b'] = llm_b_field_map.get(f_key, {}).get('source', '') or ''

    # 打印摘要
    print("=" * 50)
    print("模型执行摘要:")
    for key, config in zip(['a', 'b'], model_configs):
        status = "成功" if model_results.get(key, {}).get('success') else f"失败 - {model_results.get(key, {}).get('error', '未知错误')}"
        print(f"{config['name']}: {status}")
    print("=" * 50)

    return row_crf_json


def process_extract_task(ocr_text_dict: dict, crf_json, task_id, page_counts=None):
    full_ocr_text = "".join(ocr_text_dict.values())[:80000]  # 强制截断

    result = []
    tasks = []
    import time
    now_extract = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=settings.LLM_REQUEST_CONCURRENT_LIMIT) as executor:
        for part_crf_json in crf_json:
            title = part_crf_json['title']
            # 初始化 matched_texts 列表
            matched_texts = []

            # 创建 title 的小写版本以进行匹配
            lower_title = str.lower(title)
            # 遍历 ocr_text_dict 中的所有 key 和 value
            for k, v in ocr_text_dict.items():
                # 如果当前 key 的小写形式在 title 的小写版本中出现
                if str.lower(k) in lower_title:
                    # 记录匹配的 key 及其位置（用于排序）
                    start_index = lower_title.index(str.lower(k))
                    matched_texts.append((start_index, v))

            # 按照 key 在 title 中的位置排序
            matched_texts.sort(key=lambda x: x[0])

            # 提取排序后的值，并合并成最终的 ocr_text
            ocr_text = '\n'.join([v for idx, v in matched_texts])
            if ocr_text == '':
                ocr_text = full_ocr_text

            # 计算当前 OCR 文本对应的总页数
            current_page_count = 0
            if matched_texts:
                # 如果有匹配的文本，则计算匹配文件的总页数
                for start_index, text in matched_texts:
                    # 通过 key 找到对应的页数
                    for key, value in ocr_text_dict.items():
                        if value == text and page_counts and key in page_counts:
                            current_page_count += page_counts[key]
            else:
                # 如果没有匹配的文本，则使用所有文件的总页数
                if page_counts:
                    current_page_count = sum(page_counts.values())

            part_crf_json = [deepcopy(part_crf_json)]
            activate_conn(connection)
            future = executor.submit(extract_task, ocr_text, part_crf_json, task_id, current_page_count)
            tasks.append((future, part_crf_json))

        for future, part_crf_json in tasks:
            try:
                data = future.result()
                if not data:
                    raise Exception("No data returned from extract_task")
            except Exception as e:
                print(f"Error: {e}")
                row_part_crf_json = deepcopy(part_crf_json)
                for g_seq, group in enumerate(row_part_crf_json):
                    group['think_content'] = ''
                    group['qwen3_think_content'] = ''

                for g_seq, group in enumerate(row_part_crf_json):
                    for f_seq, field in enumerate(group['fields']):
                        field.pop('value', None)
                        field.pop('source', None)
                        field.update({
                            'value_a': '',
                            'source_a': '',
                            'value_b': '',
                            'source_b': '',

                        })
                data = row_part_crf_json

            result.extend(data)
    print("生成时间：",time.time()-now_extract)

    return result

@retry.retry(tries=3, delay=1)
def compare_task(crf_result, task_id):
    crf_result = deepcopy(crf_result)  # 避免污染原始数据

    # 分离需比对字段和无需比对字段
    need_compare_groups = []
    no_compare_fields_map = {}

    for g_seq, group in enumerate(crf_result):
        need_compare_fields = []
        no_compare_fields_map[g_seq] = []

        for f_seq, field in enumerate(group['fields']):
            if field['value_a'] == field['value_b']:
                # 无需比对字段
                no_compare_fields_map[g_seq].append({
                    'key': field['key'],
                    'index': f_seq,
                    'check': '一致',
                    'note': ''
                })
            elif field['value_a'].replace(' ', '') == field['value_b'].replace(' ', ''):
                # 无需比对字段
                no_compare_fields_map[g_seq].append({
                    'key': field['key'],
                    'index': f_seq,
                    'check': '一致',
                    'note': ''
                })
            elif (field['value_a'] in ['', None] and field['value_b'] == '未提供') or \
                (field['value_b'] in ['', None] and field['value_a'] == '未提供'):
                no_compare_fields_map[g_seq].append({
                    'key': field['key'],
                    'index': f_seq,
                    'check': '一致',
                    'note': ''
                })
            elif (field['value_a'] in ['', None] and field['value_b'] == '无') or \
                (field['value_b'] in ['', None] and field['value_a'] == '无'):
                no_compare_fields_map[g_seq].append({
                    'key': field['key'],
                    'index': f_seq,
                    'check': '一致',
                    'note': ''
                })
            else:
                # 需比对字段
                need_compare_fields.append({
                    'key': field['key'],
                    'value_a': field['value_a'],
                    'value_b': field['value_b']
                })

        if need_compare_fields:
            need_compare_groups.append({
                'group_index': g_seq,
                'group_title': group['title'],
                'fields': need_compare_fields
            })

    # 如果无需比对字段，直接返回默认值
    if not need_compare_groups:
        for g_seq, group in enumerate(crf_result):
            for f in group['fields']:
                f['check'] = '一致'
                f['note'] = ''
        return crf_result
    trans_need_compare_groups = [
    {
        'title': group['group_title'],
        'fields': group['fields']
    }
    for group in need_compare_groups
]
    # 构建 prompt
    start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    prompt = CRF_RESULT_COMPARE_PROMPT.render(
        crf_result=json.dumps(trans_need_compare_groups, indent=4, ensure_ascii=False)
    )

    # 调用模型
    response = qwen3_8b(prompt + "\nno_think")
    data = get_json_from_ai_reply(response)
    end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    try:
        output_text = response.json()
        tokens = output_text.get("usage", {})
        think_text = get_think_text_from_ai_reply(response)
    except:
        output_text = {}
        tokens = {'prompt_tokens': 0, 'completion_tokens': 0}
        think_text = ''

    # 日志记录
    activate_conn(connection)
    task = MedicalCollectionTask.objects.filter(id=task_id, delete_flag=0).first()

    log_data = {
        'task_id': task_id,
        'create_user': task.create_user if task else None,
        'create_name': task.create_name if task else None,
        'category': 'CRF_GENERATION_COMPARE',  # 新增分类
        'model_name': 'qwen3-8b',
        'start_time': start_time,
        'end_time': end_time,
        'input_text': prompt,
        'think_text': think_text,
        'output_text': json.dumps(output_text, ensure_ascii=False),
        'prompt_tokens': tokens.get('prompt_tokens', 0),
        'completion_tokens': tokens.get('completion_tokens', 0),
        'business_id': task.patient_id if task else None
    }

    # 存入数据库
    ModelInvocationLog.objects.create(**log_data)
    if data:
        restored_data = []
        for original_group in need_compare_groups:
            g_seq = original_group['group_index']
            title = original_group['group_title']
            
            matched = next((item for item in data if item.get('title') == title), None)
            
            if matched:
                restored_data.append({
                    'group_index': g_seq,
                    'group_title': title,
                    'fields': matched.get('fields', [])
                })
            else:
                restored_data.append({
                    'group_index': g_seq,
                    'group_title': title,
                    'fields': []
                })
        data = restored_data

    # 日志记录（略）

    # 填充无需比对字段
    for g_seq, fields_info in no_compare_fields_map.items():
        for info in fields_info:
            idx = info['index']
            key = info['key']
            crf_result[g_seq]['fields'][idx]['key'] = key
            crf_result[g_seq]['fields'][idx]['check'] = '一致'
            crf_result[g_seq]['fields'][idx]['note'] = ''

    # 合并模型输出
    if data:
        for group_data in data:
            g_seq = group_data['group_index']
            fields_data = group_data['fields']

            for f_data in fields_data:
                key = f_data['key']
                check = f_data.get('check', '')
                note = f_data.get('note', '')

                found = False
                for field in crf_result[g_seq]['fields']:
                    if field['key'] == key:
                        field['check'] = check
                        field['note'] = note
                        found = True
                        break
                if not found:
                    print(f"⚠️ 未找到字段：{key}，模型输出可能缺失")


    return crf_result
def process_compare_task(crf_result, task_id):
    result = []
    tasks = []
    compare_start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=settings.LLM_REQUEST_CONCURRENT_LIMIT) as executor:
        for part_crf_result in crf_result:
            future = executor.submit(compare_task, [deepcopy(part_crf_result)], task_id)
            tasks.append((future, part_crf_result))

        for future, part_crf_result in tasks:
            try:
                data = future.result()
                if not data:
                    raise Exception("No data returned from compare_task")
            except Exception as e:
                print(f"Error in CRF compare task: {e}")
                for field in part_crf_result['fields']:
                    field['check'] = '需人工复核'
                    field['note'] = '大模型调用失败'
                data = [part_crf_result]

            result.extend(data)
    compare_end_time = time.time()
    print("对比所用时长：", compare_end_time - compare_start_time)
    return result


def set_dimensions(sheet):
    # 统一设置所有工作表的列宽和行高
    # 设置所有列宽为30
    max_col = sheet.max_column
    for col in range(1, max_col + 1):
        col_letter = get_column_letter(col)
        sheet.column_dimensions[col_letter].width = 30
    # 设置所有行高为20，Sheet1后续再对特定行修改
    for row in range(1, sheet.max_row + 1):
        sheet.row_dimensions[row].height = 20


# 错误格式转换
def type_transfer(data):
    if type(data) != str:
        value = str(data)
    else:
        value = data
    return value
# 关键字符换行
def key_transfer(data):
        processed_data = data.replace("##++##", "\n")
        return processed_data
def write_crf_data_to_sheet(sheet: Worksheet, data: list) -> None:
    current_col = 1  # 当前起始列

    # 遍历每个顶层记录，写入 Sheet1
    for record in data:
        fields = record["fields"]
        num_fields = len(fields)
        start_col = current_col
        end_col = current_col + num_fields - 1

        # 第一行：合并单元格写入 title，居中且加粗
        sheet.merge_cells(start_row=1, start_column=start_col, end_row=1, end_column=end_col)
        title_cell = sheet.cell(row=1, column=start_col, value=record["title"])
        title_cell.alignment = center_alignment
        title_cell.font = bold_font

        # 如果 title 包含 "关键词"，则整列区域填充红色背景
        if "涉及手写体" in record.get("title", ""):
            for col in range(start_col, end_col + 1):
                for row in [4,6]:  # 覆盖所有行（1到12）
                    cell = sheet.cell(row=row, column=col)
                    cell.fill = yellow_fill

        # 遍历每个字段
        for i, field in enumerate(fields):
            col = current_col + i
            # 第2行：key，加粗，居中
            cell_key = sheet.cell(row=2, column=col, value=field.get("key", ""))
            cell_key.alignment = center_alignment
            cell_key.font = bold_font

            # 第3行：desc，左对齐，灰色字体
            cell_desc = sheet.cell(row=3, column=col, value=field.get("desc", ""))
            cell_desc.alignment = left_alignment
            cell_desc.font = gray_font

            # 第4行：value_a，居中；根据 check 值设置背景色
            value = type_transfer(field.get("value_a", ""))
            value = key_transfer(value)
            cell_value_a = sheet.cell(row=4, column=col, value=value)
            cell_value_a.alignment = center_alignment

            # 第5行：source_a，左对齐，灰色字体
            cell_source_a = sheet.cell(row=5, column=col, value=field.get("source_a", ""))
            cell_source_a.alignment = left_alignment
            cell_source_a.font = gray_font

            # 第6行：value_b，居中；根据 check 值设置背景色
            value = type_transfer(field.get("value_b", ""))
            value = key_transfer(value)
            cell_value_b = sheet.cell(row=6, column=col, value=value)
            cell_value_b.alignment = center_alignment

            # 第7行：source_b，左对齐，灰色字体
            cell_source_b = sheet.cell(row=7, column=col, value=field.get("source_b", ""))
            cell_source_b.alignment = left_alignment
            cell_source_b.font = gray_font

       

            # 第8行：check，左对齐，同时根据 check 值设置背景色
            cell_check = sheet.cell(row=8, column=col, value=field.get("check", ""))
            cell_check.alignment = center_alignment  # 此行可保持居中，也可以修改为 left_alignment，根据需要自行调整
            # 根据 check 设置背景色（同时对第4、6、8行处理）
            check_value = field.get("check", "")
            if check_value == "不一致":
                cell_value_a.fill = red_fill
                cell_value_b.fill = red_fill
                cell_check.fill = red_fill
            elif check_value == "需人工复核":
                cell_value_a.fill = yellow_fill
                cell_value_b.fill = yellow_fill
                cell_check.fill = yellow_fill

            # 第9行：note，左对齐
            cell_note = sheet.cell(row=9, column=col, value=field.get("note", ""))
            cell_note.alignment = left_alignment

        # 第10行：think_content，合并单元格，左对齐，自动换行
        sheet.merge_cells(start_row=10, start_column=start_col, end_row=10, end_column=end_col)
        cell_think = sheet.cell(row=10, column=start_col, value=record.get("think_content", ""))
        cell_think.alignment = wrap_left_alignment
        cell_think.font = gray_font


        # 第12行：reasoning_content，合并单元格，左对齐，自动换行
        sheet.merge_cells(start_row=11, start_column=start_col, end_row=11, end_column=end_col)
        cell_reasoning = sheet.cell(row=11, column=start_col, value=record.get("qwen3_think_content", ""))
        cell_reasoning.alignment = wrap_left_alignment
        cell_reasoning.font = gray_font

        # 更新下一组数据的起始列
        current_col = end_col + 1

    set_dimensions(sheet)

    # 将 Sheet1 中第 9、10、11 行行高改为 50
    for row in [10, 11]:
        sheet.row_dimensions[row].height = 50

    # 首行行高 30
    sheet.row_dimensions[1].height = 30

def generate_crf_file(ocr_text_dict: dict, crf_header_content, output_filename, task_id, page_counts=None):
    crf_json = convert_to_json_format(crf_header_content)
    print(crf_json)
    crf_data = process_extract_task(ocr_text_dict, crf_json, task_id, page_counts)
    print(crf_data)
    print(json.dumps(crf_data, ensure_ascii=False))

    crf_result = deepcopy(crf_data)
    for g_seq, group in enumerate(crf_result):

        group.pop('think_content')
        group.pop('qwen3_think_content')
        for f_seq, field in enumerate(group['fields']):
            if 'desc' in field:
                field.pop('desc')
            if 'source_a' in field:
                field.pop('source_a')
            if 'source_b' in field:
                field.pop('source_b')


    crf_result = process_compare_task(crf_result, task_id)
    print(crf_result)
    print(json.dumps(crf_result, ensure_ascii=False))

    llm_field_map = {}

    for g_seq, group in enumerate(crf_result):
        for f_seq, field in enumerate(group['fields']):
            llm_field_map["-".join([str(g_seq), str(f_seq)])] = field

    for g_seq, group in enumerate(crf_data):
        for f_seq, field in enumerate(group['fields']):
            f_key = "-".join([str(g_seq), str(f_seq)])

            field['check'] = llm_field_map.get(f_key, {}).get('check', '')
            field['note'] = llm_field_map.get(f_key, {}).get('note', '')

    print(crf_data)
    print(json.dumps(crf_data, ensure_ascii=False))
    crf_res = transform_data_back(crf_data)
    activate_conn(connection)
    task = MedicalCollectionTask.objects.filter(id=task_id, delete_flag=0).first()

    medical_collection_task_id = task_id
    subject_visit_id = task.subject_visit_id
    MedicalCollectionCrfResult.objects.create(
        medical_collection_task_id=medical_collection_task_id,
        result_text=crf_res,
        patient_id=task.patient_id,
        project_id=task.project_id,
        project_site_id=task.project_site_id,
        subject_id=task.subject_id,
        subject_visit_id=subject_visit_id,
        create_user=task.create_user if task else None,
        create_name=task.create_name if task else None,
    )

    wb = Workbook()
    sheet = wb.active
    sheet.title = "CRF"
    write_crf_data_to_sheet(sheet, crf_data)
    activate_conn(connection)
    print("查询日志的分割线**********************************")
    # 查询模型调用记录表日志数据 版本内有对比模型时注意：需要追加category 类型 CRF_GENERATION_COMPARE
    llm_log_data = ModelInvocationLog.objects.filter(
    task_id=task_id,
    delete_flag=0,
    category__in=["CRF_GENERATION", "CRF_GENERATION_COMPARE"]
)
    print("查询：",task_id)
    print("查询日志的分割线**********************************")
    # 转换为 DataFrame
    df = pd.DataFrame.from_records(llm_log_data.values())
    sheet2 = wb.create_sheet(f"任务{task_id}模型日志")
    # 使用 openpyxl 工具将 DataFrame 写入工作表
    for r_idx, row in enumerate(dataframe_to_rows(df, index=False, header=True), 1):
        for c_idx, value in enumerate(row, 1):
            sheet2.cell(row=r_idx, column=c_idx, value=value)

    wb.save(output_filename)

    print(output_filename)


if __name__ == "__main__":
    # python -m common.crf_llm_processor
    medical_file_folder = r"c:\Users\<USER>\Desktop\7506-33003\data"
    ocr_text = extract_text_from_folder(medical_file_folder)

    print('=' * 100)
    print(ocr_text)

    import time
    # time.sleep(30)

    generate_crf_file(
        ocr_text,
        r'c:\Users\<USER>\Desktop\7506-33003\crf\SMO-2022-7506-CN011002-CRF-体格检查.xlsx',
        r'./tmp/7506-33003-体格检查2.xlsx'
    )
    pass

    # srf_json = crf_file_to_json(
    #     r'c:\Users\<USER>\Desktop\SMO-2023-8590-CHN013013\crf\SMO-2023-8590-CHN013013-CRF - 3行.xlsx')
    # print(json.dumps(srf_json, ensure_ascii=False))

    # print(crf_json_to_md_text(srf_json))
