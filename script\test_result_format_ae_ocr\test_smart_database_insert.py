#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能数据库插入异常处理机制
验证时间格式错误的自动修复和降级插入策略
"""

import sys
import os

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_time_error_detection():
    """测试时间格式错误检测功能"""
    print("=" * 80)
    print("时间格式错误检测测试")
    print("=" * 80)
    
    # 模拟智能异常处理函数
    def is_time_format_error(error_message):
        """检测异常信息是否为时间格式相关错误"""
        time_error_keywords = [
            'invalid format',
            'YYYY-MM-DD HH:MM',
            'DateTimeField',
            'datetime',
            'time data',
            'does not match format',
            'strptime',
            'time format',
            'invalid literal for int()',
            'unconverted data remains'
        ]
        
        error_str = str(error_message).lower()
        return any(keyword.lower() in error_str for keyword in time_error_keywords)
    
    # 测试用例
    test_cases = [
        # 时间格式错误
        ('"2024-06-278:00:15" value has an invalid format. It must be in YYYY-MM-DD HH:MM[:ss[.uuuuuu]][TZ] format.', True, "Django时间格式错误"),
        ('time data "2024-06-278:00:15" does not match format "%Y-%m-%d %H:%M:%S"', True, "strptime格式错误"),
        ('DateTimeField received a non-datetime value', True, "DateTimeField错误"),
        ('invalid literal for int() with base 10: "2024-06-278:00:15"', True, "整数转换错误"),
        
        # 非时间格式错误
        ('IntegrityError: UNIQUE constraint failed', False, "唯一约束错误"),
        ('OperationalError: database is locked', False, "数据库锁定错误"),
        ('ValidationError: This field is required', False, "必填字段错误"),
        ('KeyError: "test_name"', False, "键错误"),
    ]
    
    print("🔧 测试时间格式错误检测:")
    print("-" * 60)
    
    success_count = 0
    for i, (error_msg, expected, description) in enumerate(test_cases, 1):
        result = is_time_format_error(error_msg)
        status = "✅ 成功" if result == expected else "❌ 失败"
        
        if result == expected:
            success_count += 1
        
        print(f"{i:2d}. {description}")
        print(f"    错误信息: {error_msg[:60]}...")
        print(f"    期望结果: {expected}")
        print(f"    实际结果: {result}")
        print(f"    状态: {status}")
        print()
    
    success_rate = (success_count / len(test_cases)) * 100
    print(f"检测准确率: {success_count}/{len(test_cases)} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 时间格式错误检测功能正常！")
        return True
    else:
        print("⚠️  时间格式错误检测需要优化")
        return False

def test_time_field_fixing():
    """测试时间字段修复功能"""
    print("\n" + "=" * 80)
    print("时间字段修复测试")
    print("=" * 80)
    
    def fix_time_fields_in_record(record_dict):
        """修复记录中的时间字段，将无效时间设置为None"""
        fixed_record = record_dict.copy()
        time_fixed = False
        
        # 需要检查的时间字段
        time_fields = ['collect_time', 'report_time']
        
        for field in time_fields:
            if field in fixed_record and fixed_record[field] is not None:
                original_value = fixed_record[field]
                # 将时间字段设置为None
                fixed_record[field] = None
                time_fixed = True
                print(f"    时间字段修复: {field} = '{original_value}' -> None")
        
        return fixed_record, time_fixed
    
    # 测试用例
    test_records = [
        {
            "name": "包含错误时间的记录",
            "record": {
                "test_name": "血糖",
                "test_value": "5.6",
                "collect_time": "2024-06-278:00:15",
                "report_time": "2024-06-27 10:30:45",
                "test_unit": "mmol/L"
            },
            "expected_fixes": 1
        },
        {
            "name": "两个时间都有问题的记录",
            "record": {
                "test_name": "血压",
                "test_value": "120/80",
                "collect_time": "2024-06-278:00:15",
                "report_time": "2024-06-2711:45:20",
                "test_unit": "mmHg"
            },
            "expected_fixes": 2
        },
        {
            "name": "时间为None的记录",
            "record": {
                "test_name": "心率",
                "test_value": "72",
                "collect_time": None,
                "report_time": None,
                "test_unit": "次/分"
            },
            "expected_fixes": 0
        },
        {
            "name": "没有时间字段的记录",
            "record": {
                "test_name": "体温",
                "test_value": "36.5",
                "test_unit": "℃"
            },
            "expected_fixes": 0
        }
    ]
    
    print("🔧 测试时间字段修复:")
    print("-" * 60)
    
    success_count = 0
    for i, test_case in enumerate(test_records, 1):
        print(f"{i}. {test_case['name']}")
        print(f"   原始记录: {test_case['record']}")
        
        fixed_record, time_fixed = fix_time_fields_in_record(test_case['record'])
        actual_fixes = 1 if time_fixed else 0
        
        print(f"   修复后记录: {fixed_record}")
        print(f"   期望修复数: {test_case['expected_fixes']}")
        print(f"   实际修复: {'是' if time_fixed else '否'}")
        
        # 验证修复结果
        success = True
        if test_case['expected_fixes'] > 0:
            # 应该有修复
            if not time_fixed:
                success = False
            # 检查时间字段是否都被设置为None
            for field in ['collect_time', 'report_time']:
                if field in fixed_record and fixed_record[field] is not None:
                    success = False
        else:
            # 不应该有修复
            if time_fixed:
                success = False
        
        if success:
            success_count += 1
            print(f"   状态: ✅ 成功")
        else:
            print(f"   状态: ❌ 失败")
        print()
    
    success_rate = (success_count / len(test_records)) * 100
    print(f"修复功能准确率: {success_count}/{len(test_records)} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 时间字段修复功能正常！")
        return True
    else:
        print("⚠️  时间字段修复功能需要优化")
        return False

def test_smart_insert_strategy():
    """测试智能插入策略"""
    print("\n" + "=" * 80)
    print("智能插入策略测试")
    print("=" * 80)
    
    print("📋 智能插入策略流程:")
    print("-" * 60)
    
    strategy_steps = [
        "1. 🚀 优先尝试批量插入 (TestResult.objects.bulk_create)",
        "2. 🔍 批量插入失败时，检测异常类型",
        "3. 🔧 如果是时间格式错误，启动降级插入策略",
        "4. 📝 逐条处理记录，单独插入",
        "5. 🛠️  单条插入失败时，修复时间字段后重试",
        "6. 📊 记录详细的成功/失败/修复统计信息",
        "7. 🎯 确保核心业务数据不丢失"
    ]
    
    for step in strategy_steps:
        print(f"   {step}")
    
    print("\n💡 关键特性:")
    print("   - 异常捕获与分类：识别时间格式相关异常")
    print("   - 降级插入策略：批量失败时自动切换到单条插入")
    print("   - 时间字段修复：自动将无效时间设置为None")
    print("   - 数据完整性保证：确保核心业务数据能够入库")
    print("   - 详细日志记录：便于问题追踪和数据修复")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始智能数据库插入异常处理机制测试\n")
    
    # 运行时间错误检测测试
    detection_success = test_time_error_detection()
    
    # 运行时间字段修复测试
    fixing_success = test_time_field_fixing()
    
    # 运行智能插入策略测试
    strategy_success = test_smart_insert_strategy()
    
    # 总结
    print("\n" + "=" * 80)
    print("智能异常处理机制测试总结")
    print("=" * 80)
    
    if detection_success and fixing_success and strategy_success:
        print("🎉 所有测试通过！智能异常处理机制实现成功")
        print("✅ 时间错误检测: 通过")
        print("✅ 时间字段修复: 通过")
        print("✅ 智能插入策略: 通过")
        print("\n💡 关键改进:")
        print("  - 自动识别和处理时间格式错误")
        print("  - 批量插入失败时自动降级到单条插入")
        print("  - 保证核心业务数据不因时间格式问题丢失")
        print("  - 提供详细的错误统计和日志记录")
        print("  - 避免整批数据因单个时间格式错误而失败")
        return True
    else:
        print("⚠️  部分测试失败:")
        print(f"  时间错误检测: {'✅ 通过' if detection_success else '❌ 失败'}")
        print(f"  时间字段修复: {'✅ 通过' if fixing_success else '❌ 失败'}")
        print(f"  智能插入策略: {'✅ 通过' if strategy_success else '❌ 失败'}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
