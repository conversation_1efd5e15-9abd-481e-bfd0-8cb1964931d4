import os
import json
import ast
import math
import base64
import requests
import re
import pandas as pd
from concurrent.futures import ThreadPoolExecutor
from sqlalchemy import text
from PIL import Image, ImageDraw
import requests
import re
import base64
import ast
import time
import json
from common.minio_client import get_minio_client
from common.tools import sql_to_df
import io
import fitz
from datetime import datetime, timedelta
import base64
import argparse

import os
import django
from django.db import connections
from django.conf import settings
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

django.setup()
from sqlalchemy import create_engine
from sqlalchemy.pool import NullPool
from common.tools import get_db_engin_url
from common.llm_tools import deepseek_r1_qwen_32b, qwen3_32b
from django.db import transaction
from apps.medical.models import MedicalFileMasked
from apps.ae_tracker.models import TestR<PERSON>ult, AeTrackerTask, TestOcrResult
from common.utils import get_general_prompt_sql
from common.constants import *

db_erp = create_engine(get_db_engin_url('default'), poolclass=NullPool)


def close_db_connection():
    # 关闭数据库连接
    db_erp.dispose()
    print("Database connection closed")


def getBase64(data):
    if isinstance(data, str):
        # 如果传入的是文件路径
        with open(data, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode()
    elif isinstance(data, bytes):
        # 如果传入的是字节流
        encoded_string = base64.b64encode(data).decode()
    else:
        raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")
    return encoded_string


def getOcrResult(url, payload, headers):
    """通过 API 获取 OCR 结果"""
    response = requests.post(url, headers=headers, data=payload, verify=False)
    return response.text


def getKeyWords(ocrResult):
    """提取 OCR 结果中的文本内容"""
    text = ""
    try:
        ocr_dict = ast.literal_eval(ocrResult)
        text_list = []
        for result in ocr_dict["result"]["words_block_list"]:
            text_list.append(result["words"])
            text += result["words"] + "\n"
        print(text_list)
    except Exception as e:
        print(f"❌ OCR结果解析失败: {e}")
    return text.strip()


def ocr_main(imgpath):
    """执行图片 OCR 识别并返回识别到的文本内容"""
    try:
        # 读取图片数据
        if isinstance(imgpath, str):
            with open(imgpath, "rb") as image_file:
                image_bytes = image_file.read()
        elif isinstance(imgpath, bytes):
            image_bytes = imgpath
        else:
            raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")
        
        # 使用统一的OCR服务，启用角度矫正（对应原来的detect_direction=True）
        from common.clients.ocr_service import process_ocr
        result = process_ocr(image_bytes, use_correction=True)
        
        # 直接使用统一OCR服务返回的markdown_text作为完整文本
        text = result.get("markdown_text", "")
        
        print(f"📝 提取的文本长度: {len(text)}")
        return text.strip()
        
    except Exception as e:
        print(f"❌ 图片 {imgpath} OCR识别失败: {e}")
        return ""


# 调用 LLM 处理 OCR 结果转换为 JSON
def request_llm_api1(ocr_text, model_type):
    """将 OCR 结果传给大模型，转换为标准的 JSON 格式"""

    headers = {
        "Content-Type": "application/json",
        "Auth-Username": "mauser",
        "Auth-Password": "Prs@123456"
    }
    dict_result = {'溶血': '实验检查显示大量红细胞膜破裂为特征的疾病。', '溶血性尿毒症综合征': '血栓性微血管病的一种形式，伴随肾衰竭、溶血性贫血和严重血小板降低。',
                   '淋巴结痛': '淋巴结有显著不适感为特征的疾病。', '高铁血红蛋白症': '以实验室报告结果指示血液中高铁血红蛋白升高的疾病。',
                   '血栓性血小板减少性紫癜': '存在微血管病性溶血性贫血，血小板减少性紫癜，发热，肾功能异常和神经系统异常（如', '主动脉瓣疾病': '主动脉瓣膜功能或者瓣膜结构缺陷为特征的疾病。',
                   '心脏停搏': '无心电活动的节律异常为特征的疾病。典型症状', '房颤': '多个兴奋折返的存在，产生的不规则P波的心律失常，伴有不规则心室舒缩反应为特征的疾病。心律异常起源于心室以上传导系统。',
                   '心房扑动': '心房节律性收缩，心率达200～300次/分钟为特征的疾病。心律异常起源于心房。', '完全性房室传导阻滞': '心房电冲动不能通过房室结传递到心室的心律失常为特征的疾病。',
                   'I度房室传导阻滞': '电冲动通过房室结的时间超过0.2秒，延长的PR间期超过200毫秒。', '心跳骤停': '心脏泵血功能停止。',
                   '胸痛—心源性': '因心肌氧供不足导致的胸骨下不适，如心绞痛。', '传导障碍': '心脏传导系统因素导致的传导异常。', '发绀': '以皮肤和/或粘膜颜色为变为蓝色的疾病。',
                   '心力衰竭': '定义', '左心室收缩功能不全': '左心室不能产生足够的射血量。', '二尖瓣疾病': '二尖瓣功能异常或结构缺陷。',
                   'MobitzⅡ型房室传导阻滞': '心房冲动中断之前，出现相对规则PR间期的节律异常，是房室结和心室之间的心房电冲动传导间歇性中断的结果。',
                   'MobitzI型房室传导阻滞': '心房冲动中断之前，导致进行性PR间期延长，是房室结和心室之间的心房电冲动传导间歇性中断所致。',
                   '心肌梗死': '由于血流中断，导致相应供血区域的心肌出严重坏死。', '心肌炎': '心脏肌肉组织发生炎症反应。', '心悸': '由心脏无规律和/或强有力的搏动引起的不适感。',
                   '阵发性房性心动过速': '节律异常特点为心房收缩突然开始和突然中止，心房收缩每分钟150～250次，节律异常源于心房。', '心包积液': '心包囊中出现液体聚集，通常由炎症所致。',
                   '心包填塞': '心包膜中血液或组织液聚集导致心包内压力增大的疾病。', '心包炎': '心包膜（具有心脏保护作用）出现炎症反应。', '肺动脉瓣疾病': '肺动脉瓣功能或结构出现障碍。',
                   '限制性心肌病': '心肌变硬，失去弹性，心室血液充盈受限。', '右心室功能不全': '右侧心室功能受损，射血分数和右心室壁能动性降低。',
                   '病窦综合征': '心动过缓和心动过速周期性交替出现的节律障碍，伴有晕厥、疲乏和头晕。', '窦性心动过缓': '起源于窦房结，心率低于60次/分钟的节律异常。',
                   '窦性心动过速': '起源于窦房结，心率高于100次/分钟的节律异常。', '室上性心动过速': '起源于心室传导系统以上，心率高于100次/分钟的节律异常。',
                   '三尖瓣疾病': '三尖瓣的功能或结构出现异常。', '室性心律失常': '起源于心室的心律失常为特征的疾病。',
                   '室颤': '由于心肌纤维快速重复的激发激动，导致不规则的QRS波群以及不协调心室收缩的心律失常为特征的疾病。',
                   '室性心动过速': '起源于希氏束分叉处以下，心率超过100次/分钟的心脏节律异常为特征的疾病。', '耳痛': '耳部出现明显的不适感。', '外耳痛': '耳廓部位出现明显的不适感。',
                   '听力损伤': '耳部结构受损，引起对声音的感知或理解出现部分性或完全性丧失。', '中耳炎': '发生于中耳的感染性疾病。',
                   '耳鸣': '耳中出现噪音为特征的疾病，如铃声，嗡嗡声，吼叫声或碎裂声。', '眩晕': '感觉头晕目眩，不平稳，眼花，旋转，摇摆。', '前庭病': '以头晕，失衡，恶心和视觉异常为特征的疾病。',
                   '肾上腺功能减退': '肾上腺皮质不能产生足够的皮质醇激素，有些病例中也包括醛固酮激素产生不足；可能由阿狄森氏病和原发性肾上腺皮质功能不全等肾上腺皮质功能异常原因引起。',
                   '类库欣（Cushing）综合征': '出现与Cushing病或综合征相似的症状和体征', '青春期性发育迟缓': '性成熟发育异常迟缓。', '生长过速': '生长发育超过年龄相适应的预期值。',
                   '甲状旁腺功能亢进': '甲状旁腺产生过多的甲状旁腺激素导致的异常，可引起高钙血症（血钙水平异常增高）。',
                   '甲状腺功能亢进症': '体内甲状腺激素水平过高所致疾病；通常是甲状腺机能亢进或甲状腺激素使用过量所致。', '甲状旁腺功能减退': '甲状旁腺产生甲状旁腺激素减少所致的疾病。',
                   '垂体炎': '疾病特征为垂体炎症或细胞浸润。', '垂体功能低下': '疾病特征为垂体产生激素减少。', '甲状腺功能减退症': '甲状腺产生甲状腺激素减少所致的疾病。',
                   '性早熟': '第二性征过早发育；<8岁女性和<9岁男性开始出现性成熟。', '睾酮缺乏症': '女性或青春期前的男性发生不相称的雄性化。',
                   '男性化': '女性或青春期前的男性发生不相称的雄性化。', '视力模糊': '视力出现不清晰影像或模糊影像。',
                   '白内障': '单侧或双侧眼部晶状体出现部分性或完全性不透明，结果导致视敏度降低，如不经过治疗最终导致失明。', '角膜溃疡': '角膜上皮组织出现缺损，与角膜和前房的炎症细胞相关。',
                   '干眼症': '角膜和眼结膜干燥。', '眼外肌麻痹': '眼外肌不完全性麻痹。', '眼痛': '眼部出现明显的不适感。', '眼睑功能障碍': '眼部出现明显的不适感。',
                   '闪光': '短暂的或突然的亮光。', '飞蚊症': '眼前有飘动的斑点。斑点是玻璃体或晶状体中不透明细胞碎片的影像。', '青光眼': '房水流出受阻，导致眼球内压力增高。',
                   '角膜炎': '眼角膜发生炎症反应。', '夜盲症': '在暗光下，看不清物体。', '视神经病变': '视神经（第二对颅脑神经）出现异常。', '视乳头水肿': '视神经盘周围出现肿胀。',
                   '眶周水肿': '疾病特征为脸部眼眶周围由于过多液体蓄积而肿胀。', '畏光': '眼睛怕见光和对光回避的状态。', '视网膜脱离': '视网膜内孔层与视网膜色素膜上皮层间出现分离。',
                   '视网膜撕裂': '视网膜和玻璃体分离，视网膜出现小的撕裂。症状包括', '视网膜血管病症': '视网膜病理性血管形成，对视力产生不利。', '视网膜病变': '视网膜出现异常的疾病。',
                   '巩膜病': '巩膜出现异常的疾病。', '葡萄膜炎': '眼葡萄膜的炎症。', '视野缺损': '疾病特征为视力下降。', '玻璃体出血': '玻璃体出血。',
                   '流泪': '疾病特征为眼睛多泪；可由于泪液产生过多或鼻泪管损伤所致。', '腹胀': '腹部膨隆胀大。', '腹痛': '腹部出现显著不适感。', '肛裂': '疾病特征为肛门表面撕裂。',
                   '肛瘘': '肛管开口处与肛门周围皮肤相通所形成异常通道。', '肛门出血': '肛门出现出血。', '肛门粘膜炎': '疾病特征为肛门黏膜溃疡或炎症。',
                   '肛门坏死': '肛门区域出现组织坏死的过程。', '肛门痛': '肛门区出现明显的不适感。', '肛门狭窄': '肛管管腔变窄。', '肛门溃疡': '疾病特征为肛管黏膜表面有局灶性糜烂性病变。',
                   '腹水': '腹腔中出现浆液或血液的积聚。', '嗳气': '嘴里排出气体伴响声很大。', '胃胀': '主观的腹部胀满不适感。', '盲肠出血': '盲肠出现出血。',
                   '唇炎': '唇部发生炎症。', '乳糜性腹水': '疾病特征为腹腔内牛奶样液体聚积。', '结肠炎': '结肠出现炎症反应。', '结肠瘘': '大肠肠管与其他脏器或解剖部位之间形成异常通道。',
                   '结肠出血': '结肠出现出血。', '结肠梗阻': '结肠中发生肠内容物正常流向受阻的疾病。', '结肠穿孔': '结肠壁出现破裂为特征的疾病。', '结肠狭窄': '结肠官腔变窄。',
                   '结肠溃疡': '疾病特征为结肠黏膜表面有局灶性糜烂性病变。', '便秘': '出现无规律的和次数稀少的排便或难于排便。', '龋齿': '牙齿被龋蚀，变软，颜色改变和/或多孔。',
                   '腹泻': '疾病特征为便次增加和/或稀便或水样便。', '口干': '口腔唾液流量减少导致的异常。', '〸二指肠瘘': '〸二指肠与其他器官或解剖部位之间形成的异常通道。',
                   '〸二指肠出血': '〸二指肠出血为特征的疾病。', '〸二指肠梗阻': '胃的内容物流向〸二指肠的过程受阻。', '〸二指肠穿孔': '〸二指肠管壁破裂。',
                   '〸二指肠狭窄': '〸二指肠管腔变窄、变小。', '〸二指肠溃疡': '〸二指肠壁粘膜表面出现的局限性侵蚀性病变。', '消化不良': '消化受损引起胃部不适（通常胃部疼痛感），症状包括',
                   '吞咽困难': '吞咽出现困难。', '小肠结肠炎': '小肠和大肠出现炎症。', '肠膀胱瘘': '肠与膀胱之间形成的异常通道。', '食管瘘': '食管与其他脏器或解剖部位间形成的异常通道。',
                   '食管出血': '食管出血为特征的疾病。', '食管坏死': '食管壁发生坏死的病理过程。', '食管梗阻': '食道中发生内容物正常流向受阻的疾病。', '食管痛': '食管区域出现显著不适感。',
                   '食管穿孔': '食管壁破裂。', '食管狭窄': '食管管腔变窄。', '食管溃疡': '食管壁粘膜表面出现的局限性侵蚀性病变。', '食管静脉曲张出血': '食管静脉曲张出现出血为特征的疾病。',
                   '食道炎': '食管壁出现炎症。', '大便失禁': '不能控制粪便从直肠排出为特征的疾病。', '肠胃胀气': '以从下消化道排出过多的气体为特征的一种疾病。',
                   '胃瘘': '胃与其他脏器或解剖部位之间形成的异常通道。', '胃出血': '胃壁出现出血为特征的疾病。', '胃坏死': '胃壁出现坏死病理过程。', '胃穿孔': '胃管壁破裂为特征的疾病。',
                   '胃狭窄': '胃管腔变窄。', '胃溃疡': '胃黏膜表面出现局限的侵蚀性病变。', '胃炎': '胃出现炎症。',
                   '胃食管返流病': '胃和/或〸二指肠内容物返流入远端食管的疾病。为慢性病，通常由食道下端括约肌功能异常引起，可以导致食管粘膜损伤。症状包括',
                   '胃肠道瘘': '胃肠系统任一部位与其他脏器或解剖部位之间形成的异常通道。', '胃肠道痛': '胃肠道出现明显的不适感。',
                   '胃肌软瘫': '胃壁肌肉发生不完全性麻痹，延缓胃内容物至小肠的排空过程。', '龈痛': '齿龈区域出现显著的不适感。', '痔出血': '痔出现出血为特征的疾病。',
                   '回肠瘘': '回肠与其他脏器或解剖部位之间形成的异常通道。', '回肠出血': '回肠壁出现出血为特征的疾病。', '回肠梗阻': '回肠中发生肠内容物正常流向受阻的疾病。',
                   '回肠穿孔': '回肠管壁破裂为特征的疾病。', '回肠狭窄': '回肠管腔变窄。', '回肠溃疡': '回肠黏膜表面出现局限的侵蚀性损害。', '肠梗阻': '回肠不能运送肠内容物为特征的疾病。',
                   '肠腔内出血': '腹腔出现出血为特征的疾病。', '空肠瘘': '空肠与其他脏器或解剖部位之间形成的异常通道。', '空肠出血': '空肠壁出现出血为特征的疾病。',
                   '空肠梗阻': '空肠中发生肠内容物正常流向受阻的疾病。', '空肠穿孔': '空肠管壁出现破裂为特征的疾病。', '空肠狭窄': '空肠管腔变窄。',
                   '空肠溃疡': '空肠粘膜表面出现局限的侵蚀性病变。', '唇痛': '唇部出现显著的不适感。', '下消化道出血': '下消化道（小肠，大肠和肛门）出现出血为特征的疾病。',
                   '吸收障碍': '营养物质在小肠中不能充分吸收。症状包括', '口腔粘膜炎': '口腔黏膜出现溃疡或者炎症。', '恶心': '以反胃和/或急需呕吐为特征的疾病。',
                   '胃梗阻': '胃内容物正常流向受阻的疾病。', '口腔瘘': '口腔与其他脏器或解剖部位之间形成的异常通道。', '口腔感觉迟钝': '唇，舌或者整个口腔的灼烧感或者刺痛感。',
                   '口腔内出血': '口腔出现出血为特征的疾病。', '口腔痛': '口，舌或唇出现明显的不适感。', '胰管狭窄': '胰腺导管管腔变窄。',
                   '胰腺瘘': '胰腺与其他脏器或解剖部位之间形成的异常通道。', '胰腺出血': '胰腺出现出血为特征的疾病。', '胰腺坏死': '胰腺发生坏死。', '胰腺炎': '胰腺出现新的炎症反应。',
                   '牙周病': '牙周围牙龈组织出现疾病。', '腹膜坏死': '腹膜发生坏死。', '直肠炎': '直肠发生炎症。', '直肠裂': '一种以直肠粘膜或直肠内壁裂伤为特征的疾病。',
                   '直肠瘘': '直肠与其他脏器或解剖部位之间形成的异常通道。', '直肠出血': '直肠壁和肛门出现出血为特征的疾病。', '直肠粘膜炎': '一种以直肠粘膜溃疡或炎症反应为特征的疾病。',
                   '直肠坏死': '直肠壁发生坏死。', '直肠梗阻': '直肠中发生肠内容物正常流向受阻的疾病。', '直肠痛': '直肠部位出现显著不适感。', '直肠穿孔': '直肠管壁出现破裂为特征的疾病。',
                   '直肠狭窄': '直肠管腔变窄。', '直肠溃疡': '一种以直肠粘膜表面局限的坏死性糜烂性炎症病变为特征的疾病。', '腹膜后出血': '腹膜后部位出现出血为特征的疾病。',
                   '唾腺炎': '唾腺导管出现炎症。', '唾腺瘘': '唾腺与其他脏器或解剖部位之间形成的异常通道。', '小肠粘膜炎': '一种以小肠粘膜溃疡或炎症反应为特征的疾病。',
                   '小肠梗阻': '一种以小肠内容物正常流向受阻为特征的疾病。', '小肠穿孔': '小肠管壁出现破裂为特征的疾病。', '小肠狭窄': '小肠管腔变窄。',
                   '小肠溃疡': '一种以小肠粘膜表面局限的坏死性糜烂性病变为特征的疾病。', '胃痛': '发生在胃部的显著不适感。', '牙发育障碍': '牙齿发育中出现的病理过程。',
                   '牙变色': '牙齿的色泽或者色调改变。', '牙痛': '发生在牙齿的显著不适感。', '盲肠炎': '一种在中性粒细胞减少的患者中以坏死性小肠结肠炎为特征的疾病。',
                   '上消化道出血': '上消化道（口腔，咽，食管和胃）出现出血为特征的疾病。', '内脏动脉缺血': '由于内脏（肠系膜）动脉狭窄或堵塞，导致血流供应下降。',
                   '呕吐': '胃内容物经口吐出的一种反射动作。', '寒战': '一种生理性的以发冷为主要表现的功能失调，通常在发热出汗后出现出该反应。', '新生儿死亡': '新生儿出生后28天内死亡。',
                   '死亡NOS': '不属于CTCAE中任一第5级不良事件的死亡', '疾病进展': '患者的死亡是由于其疾病进展而导致的，但该死亡无法归属于CTCAE中的任一第5级不良事件。',
                   '面部水肿': '面部组织出现过多的液体聚集，产生肿胀。', '四肢水肿': '上肢或下肢部位出现液体过多聚集，引起肿胀。', '躯体浮肿': '身体躯干出现过多液体聚集，产生肿胀。',
                   '面部疼痛': '面部出现明显不适感。', '疲劳': '全身处于无力状态，不易鼓起精神完成日常工作。', '流行性感冒样症状': '出现一系列类似流感患者的临床症状的疾病。症状包括',
                   '步态障碍': '行走困难。', '全身性水肿': '一种以在全身各组织（包括皮肤）出现液体聚积为特征的疾病。',
                   '输液部位渗漏': '一种以输液时液体渗漏至输液部位为特征的异常改变，其症状体征包括硬结、红斑、水肿、灼热感和输注部位的明显不适。',
                   '注射部位反应': '注射部位出现剧烈的不良反应（通常为免疫反应）。', '局限性水肿': '特定的解剖区域出现过多的液体聚集，引起肿胀。', '全身乏力': '全身不舒适感，身体不佳的状态。',
                   '多器官功能衰竭': '肺部，肝脏，肾脏和凝血机制出现进行性恶化的疾病。', '颈部水肿': '颈部液体过多聚集，发生肿胀。', '非心源性胸痛': '一种以非心源性胸部显著不适感为特征的疾病。',
                   '疼痛': '显著不适感、痛苦或剧痛。', '猝死NOS': '不属于CTCAE中任一第5级不良事件的非预期死亡。', '疫苗接种部位淋巴结肿大': '一种以接种疫苗后淋巴结肿大为特征的疾病。',
                   '胆管狭窄': '胆管内腔变窄。', '胆管瘘': '胆管与其他脏器或解剖部位之间形成的异常通道。',
                   '布加（Budd-Chiari）综合征': '一种以肝静脉梗阻导致腹痛、腹水和肝脏肿大为特征的综合征。', '胆囊炎': '胆囊出现炎症为特征的疾病。可能与胆囊结石相关。',
                   '胆囊瘘': '胆囊与其他脏器或解剖部位之间形成的异常通道。', '胆囊坏死': '胆囊发生坏死为特征的疾病。', '胆囊梗阻': '胆囊中正常内容物流向受阻的疾病。',
                   '胆囊痛': '胆囊部位出现显著的不适感。', '胆囊穿孔': '胆囊壁发生破裂为特征的疾病。',
                   '肝功能衰竭': '一种以肝脏不能对某种药物进行代谢为特征的疾病。实验室检查结果显示血浆中氨、胆红素、乳酸脱氢酶、碱性磷酸酶、转氨酶水平异常和/或凝血酶原时间（INR）延长。药物导致的肝损伤（DILI）需符合Hy’s法则。',
                   '肝出血': '肝脏出现出血。', '肝坏死': '肝脏实质发生坏死过程。', '肝脏痛': '肝区出现显著不适感。', '胆管穿孔': '肝外和肝内胆管管壁出现破裂。',
                   '门静脉高压': '门静脉系统出现血压增高。', '门脉血栓症': '门静脉出现血栓形成（血凝块）。', '肝窦阻塞综合征': '由于肝脏血管发炎和/或阻塞引起重度肝损的一种疾病。',
                   '变态反应': '接触抗原后引起机体局部或者全身性的不良反应。',
                   '过敏反应': '肥大细胞释放的组胺和组胺样物质导致的急性炎症反应，引起机体超敏免疫反应。临床上表现为呼吸困难，头晕，低血压，发绀和意识丧失，可能导致死亡。',
                   '自身免疫性疾病': '由于个体的体液和细胞对自身组织成分产生的免疫反应，导致一个或多个脏器的功能丧失或组织破坏。',
                   '细胞因子释放综合征': '出现发热、呼吸急促、恶心、头痛、心动过速、低血压、皮疹和/或缺氧的一种疾病，由细胞释放的细胞因子引起。',
                   '血清病': '动物血清中的外源性蛋白导致的迟发型超敏反应。一般在外源性抗原刺激后的6～21天发病。临床症状', '腹部感染': '腹腔发生感染。',
                   '肛门直肠感染': '发生于肛区和直肠区域的感染。', '阑尾炎': '由致病体引起的阑尾急性炎症。',
                   '穿孔性阑尾炎': '致病体引起的急性阑尾炎症，出现坏疽，引起阑尾管壁破裂。阑尾穿孔后阑尾中炎性和细菌成分漏入腹腔。', '感染性动脉炎': '动脉出现感染。',
                   '菌血症': '血液中存在细菌的疾病。', '胆道感染': '胆道出现感染。', '膀胱感染': '膀胱出现感染。', '骨感染': '骨出现感染。', '乳房感染': '乳房出现感染。',
                   '支气管感染': '支气管出现感染。', '导管相关感染': '继发于导管使用出现感染。', '盲肠感染': '盲肠出现感染。', '感染性宫颈炎': '子宫颈出现感染。',
                   '结膜炎': '眼结膜炎症、肿胀和发红。', '感染性结膜炎': '眼结膜出现感染，临床症状', '角膜感染': '角膜出现感染。', '脑神经感染': '颅脑神经发生感染。',
                   '巨细胞病毒感染再激活': '巨细胞病毒（CMV）复活的一种病症。', '医疗器械相关的感染': '医疗器械使用过程中出现感染。', '〸二指肠感染': '〸二指肠出现感染。',
                   '感染性脑炎': '脑组织出现感染。', '感染性脑脊髓炎': '脑组织和脊髓组织出现感染。', '感染性心内膜炎': '心脏心内膜层出现感染。', '眼内炎': '眼睛内部结构出现感染。',
                   '感染性小肠结肠炎': '小肠和大肠出现感染。', '埃-巴二氏病毒感染再激活': '埃-巴二氏病毒感染复活的疾病。', '食道感染': '食道出现感染。', '眼感染': '眼部出现感染。',
                   '真菌血症': '血液出现真菌感染。', '胆囊感染': '胆囊出现感染。', '牙龈感染': '牙龈出现感染。', '肝脏感染': '肝脏出现感染。',
                   '乙型肝炎再激活': '乙型肝炎病毒再激活的疾病。', '病毒性肝炎': '发生于肝脏实质的病毒感染性疾病。', '单纯疱疹再激活': '单纯疱疹病毒再激活的疾病。',
                   '感染性肌炎': '骨骼肌出现感染。', '关节感染': '发生于关节的感染性疾病。', '肾脏感染': '发生于肾脏的感染性疾病。', '喉炎': '发生于喉部的感染性疾病。',
                   '口唇感染': '发生于唇部的感染性疾病。', '肺感染': '发生于肺部的感染性疾病，包括肺炎。', '淋巴结感染': '发生于淋巴结的感染性疾病。', '纵隔感染': '发生于纵隔的感染性疾病。',
                   '脑膜炎': '发生于脑和/或脊髓脑膜的急性感染性疾病。', '粘膜感染': '发生于组织粘膜表面的感染性疾病。', '脊髓炎': '脊髓炎症相关的疾病，症状包括',
                   '指甲感染': '发生于指甲的感染性疾病。', '外耳炎': '外耳和耳道发生感染，诱因包括', '卵巢感染': '发生于卵巢的感染性疾病。', '胰腺感染': '发生于胰腺的感染性疾病。',
                   '甲沟炎': '发生于指甲周围软组织的感染性疾病。', '骨盆腔感染': '发生于骨盆腔的感染性疾病。', '阴茎感染': '发生于阴茎的感染性疾病。', '眼眶感染': '发生于眼眶的感染性疾病。',
                   '外周神经感染': '发生于外周神经的感染性疾病。', '腹膜感染': '发生于腹膜的感染性疾病。', '咽炎': '发生于咽喉的炎症。', '感染性静脉炎': '发生于静脉的感染。临床症状包括',
                   '胸膜感染': '发生于胸膜的感染性疾病。', '前列腺感染': '发生于前列腺的感染性疾病。', '脓疱性疹': '局限的，凸起脓性的皮肤病损。', '感染性鼻炎': '发生于鼻粘膜的感染性疾病。',
                   '唾液腺感染': '发生于唾液腺的感染性疾病。', '阴囊感染': '发生于阴囊的感染性疾病。', '败血症': '循环血液中病原微生物引起快速的、进行性的全身性反应，可以导致休克。',
                   '带状疱疹': '带状疱疹病毒再激活引起的一种疾病。', '鼻窦炎': '发生于鼻窦粘膜的感染性疾病。', '皮肤感染': '发生于皮肤的感染性疾病，例如蜂窝组织炎。',
                   '小肠感染': '发生于小肠的感染性疾病。', '软组织感染': '发生于软组织的感染性疾病。', '脾感染': '发生于脾脏的感染性疾病。',
                   '造口感染': '造口（经手术构造的开口于体表的通道）出现感染。', '鹅口疮': '疑似念珠菌感染引起的一种口腔黏膜表面疾病。', '牙齿感染': '发生于牙齿的感染性疾病。',
                   '气管炎': '发生于气管的感染性疾病。', '上呼吸道感染': '上呼吸道（鼻，鼻旁窦，咽，喉或气管）出现感染。', '尿道感染': '发生于尿道的感染性疾病。',
                   '尿路感染': '尿路的感染，通常发生在膀胱和尿道。', '子宫感染': '子宫内膜出现感染。可以蔓延至子宫肌层和子宫旁组织。', '阴道感染': '发生于阴道的感染性疾病。',
                   'Viremia病毒血症': '血液中存在病毒为特征的一种疾病。', '外阴部感染': '发生于外阴部的感染性疾病。', '伤口感染': '发生于伤口的感染性疾病。',
                   '踝部骨折': '踝骨的连续性中断，踝关节受损。症状', '主动脉损伤': '出现主动脉损伤。', '动脉损伤': '动脉出现受损。',
                   '胆管吻合部瘘': '胆道吻合口处破裂，导致胆汁外漏（两个独立的解剖结构间的手术连接）。', '膀胱吻合部瘘': '膀胱吻合口处破裂，导致尿液外漏（两个独立的解剖结构间的手术连接）。',
                   '擦伤': '软组织或者骨组织损伤，导致血液渗漏进入周围软组织。',
                   '烧伤': '由于机体暴露化学品，直接热源，电流，火焰和辐射导致解剖部位的完整性受到破坏。损伤的程度与暴露的时间和强度以及直至治疗的时间有关。',
                   '放射性皮肤炎': '暴露在生物学有效水平的电离辐射下，引起的皮肤炎症反应。', '食管吻合部瘘': '食管吻合口的断裂，出现内容物漏出（两个独立的解剖结构间的手术连接）。',
                   '跌落': '突发的向下运动，通常导致损伤。', '输卵管吻合部瘘': '输卵管吻合口的断裂，出现内容物漏出（两个独立的解剖结构间的手术连接）。', '输卵管穿孔': '输卵管壁破裂。',
                   '骨折': '骨的连续性中断出现的挫伤。', '胃吻合部瘘': '胃吻合口的断裂，出现内容物漏出（两个独立的解剖结构间的手术连接）。',
                   '胃肠吻合部瘘': '胃肠道吻合口的断裂，出现内容物漏出（两个独立的解剖结构间的手术连接）。', '胃肠道造口坏死': '胃肠道造口出现进行性坏死',
                   '髂骨骨折': '股骨头，股骨颈，转子间或转子下端的骨连续性中断，髋骨损伤。', '输液相关反应': '因药理学或生物物质输注引发的不良反应。', '颈动脉损伤': '颈动脉受损。',
                   '下腔静脉损伤': '下腔静脉受损。', '颈静脉损伤': '颈静脉受损。', '上腔静脉损伤': '上腔静脉出现损伤。', '肠道造口瘘': '内容物从肠道造口处漏出（位于体表的手术造口）。',
                   '肠道造口梗阻': '肠造口内容物的正常流动受阻。', '肠道造口部位出血': '肠造口出血。', '术中动脉损伤': '手术过程中出现的动脉受损。',
                   '术中乳房损伤': '手术过程中出现的乳房实质损伤。', '术中心脏损伤': '手术过程中出现的心脏受损。', '术中耳损伤': '手术过程中出现的耳部损伤。',
                   '术中内分泌系统损伤': '手术过程中出现内分泌腺损伤。', '术中胃肠道损伤': '手术过程中出现的胃肠道系统受损。', '术中头颈部损伤': '手术过程中出现的头颈部受损。',
                   '术中出血': '术中出现不可控的出血', '术中肝胆损伤': '手术过程中出现肝实质和/或胆道损伤。', '术中骨骼肌损伤': '手术过程中出现骨骼肌系统损伤。',
                   '术中神经损伤': '手术过程中出现神经系统损伤。', '术中眼损伤': '手术过程中出现眼部损伤。', '术中肾脏损伤': '手术过程中出现肾脏损伤。',
                   '术中生殖系统损伤': '手术过程中出现生殖器官损伤。', '术中呼吸系统损伤': '手术过程中出现呼吸系统损伤。', '术中脾损伤': '手术过程中出现脾脏损伤。',
                   '术中尿路损伤': '手术过程中出现泌尿系统损伤。', '术中静脉损伤': '手术过程中出现静脉损伤。', '肾吻合口瘘': '由于肾脏吻合口的断裂，出现尿液漏出（两个独立的解剖结构间的手术连接）。',
                   '大肠吻合口瘘': '由于大肠吻合口的断裂，出现内容物漏出（两个独立的解剖结构间的手术连接）。', '胰腺吻合口瘘': '由于胰腺吻合口的断裂，出现内容物漏出（两个独立的解剖结构间的手术连接）。',
                   '咽吻合口瘘': '由于咽部吻合口的断裂，出现内容物漏出（两个独立的解剖结构间的手术连接）。', '术后出血': '以出血为特征的术后紊乱。', '术后胸部处理并发症': '胸部手术后出现新的异常。',
                   '肠造口脱垂': '肠造口（体表手术造口）从腹壁表面突出。', '人造尿道口脱垂': '人造尿道口发生移位。',
                   '放射治疗回忆反应（皮肤用药）': '经放疗后，药物特别是化疗药物数周或数月的治疗引起的急性皮肤炎性反应。炎症反应局限于既往经放疗的皮肤，停药后症状消失。',
                   '直肠吻合口瘘': '直肠吻合口（两个独立解剖结构的手术连接）破裂发生内容物漏出。', '血清肿': '血清在组织中聚集，形成肿瘤组织样外形。',
                   '小肠吻合口瘘': '小肠吻合口（两个独立解剖结构的手术连接）破裂，发生内容物漏出。', '精索吻合口瘘': '精索吻合口（两个独立解剖结构的手术连接）破裂，发生内容物漏出。',
                   '脊柱骨折': '脊椎骨的连续性中断，出现脊柱挫伤。', '胃肠道造口狭窄': '胃肠道造口（体表手术造口）出现狭窄。',
                   '吻合口溃疡': '胃肠造口手术后，吻合口附近的空肠粘膜表面出现局限的糜烂性病变。', '气管出血': '以气管出血为主要特征的紊乱。', '气管梗阻': '以气管阻塞为主要特征的紊乱。',
                   '气管造口部位出血': '以气管造瘘部位出血为主要特征的紊乱。', '输尿管吻合口瘘': '输卵管吻合口（两个独立解剖结构的手术连接）破裂，发生漏出。',
                   '尿道吻合口瘘': '尿道吻合口（两个独立解剖结构的手术连接）破裂，发生漏出。', '人工尿道口瘘': '内容物由人工尿道造口漏出。', '人工尿道口阻塞': '以泌尿道阻塞为特征的紊乱。',
                   '人工尿道口出血': '以泌尿道出血为特征的紊乱。', '人工尿道口狭窄': '人工尿道口变窄。', '子宫吻合口瘘': '子宫吻合口（两个独立解剖结构的手术连接）破裂，发生漏出。',
                   '子宫穿孔': '子宫壁出现破裂。', '接种疫苗的并发症': '注射抗原性质的物质后激活免疫系统反应的一种紊乱。', '阴道吻合口瘘': '阴道吻合口（两个独立解剖结构的手术连接）破裂，发生漏出。',
                   '输精管吻合口瘘': '输精管吻合口（两个独立解剖结构的手术连接）破裂，发生漏出。', '血管通路并发症': '血管开口部位出现新的异常。', '静脉损伤': '静脉出现损伤。',
                   '创口并发症': '现有创口部位出现新的异常。', '创口裂开': '手术创口边缘出现分裂。', '腕骨骨折': '腕骨的连续性发生中断，腕关节挫伤。',
                   '血抗利尿激素异常': '实验室检查结果显示，血液样本中抗利尿激素水平异常。', '血促皮质激素降低': '实验室检查结果显示，血液样本中皮质激素水平降低。',
                   '血促性腺激素异常': '实验室检查结果显示，血液样本中促性腺激素水平异常。', '血催乳素异常': '实验室检查结果显示，血液样本中催乳素水平异常。',
                   '射血分数降低': '心室收缩时射出血量占心室收缩前心室内血液总量的百分比。', '心电图T波异常': '非特异性T段改变特征性疾病',
                   '生长激素分泌异常': '血液样本实验室检查结果显示，生长激素水平出现异常。', '淋巴细胞计数增高': '实验室检查结果显示，血液、体液和骨髓中淋巴细胞计数异常升高。',
                   '促甲状腺激素增加': '一种以促甲状腺激素增加为指征的疾病', '尿量减少': '实验室检查结果显示，与以往相比，排尿量减少。',
                   '肺活量异常': '肺功能检测显示，与预测值相比，肺活量（肺最大吸气后，最大呼出气体量）出现异常。', '乙醇不耐受': '乙醇引起不良作用的敏感性增加。不良作用包括', '厌食症': '食欲减退。',
                   '脱水': '体内过多的水分丢失，通常由重度腹泻、呕吐或出汗所致。', '葡萄糖耐受不良': '机体不能完全代谢葡萄糖为特点的疾病。',
                   '高血糖症': '实验室检查结果显示，血糖浓度升高，通常为糖尿病或葡萄糖耐受不良的指标。', '高血脂症': '以实验室检查结果显示血液中脂类浓度的升高为指征的疾病',
                   '低磷血症': '实验室检查结果显示，血液中磷酸盐的浓度低。', '铁超负荷': '铁在组织内累积。', '肥胖': '体内脂肪过多。',
                   '肿瘤溶解综合症': '自发的或治疗相关的肿瘤细胞溶解引起代谢异常疾病。', '腹部软组织坏死': '腹壁软组织出现坏死的疾病。', '关节痛': '关节部位明显不适感。',
                   '关节炎': '关节部位出现炎症反应。', '缺血性坏死': '血供中断后，骨组织出现坏死性改变，通常发生于长骨的骺端，骨坏死性改变可以引起骨组织结构破坏。',
                   '背痛': '一种以背部明显不适感为指征的疾病。', '骨痛': '一种以骨骼明显不适感为指征的疾病。', '臀部痛': '一种以臀部明显不适感为指征的疾病。',
                   '胸壁坏死': '一种以胸壁软组织（包括乳房）发生坏死为指征的疾病。', '胸壁痛': '一种以胸壁处明显不适感为指征的疾病。', '外生骨疣': '非肿瘤性的骨组织过度生长。',
                   '深部结缔组织纤维化': '深部结缔组织出现纤维变。', '胁腹痛': '一种以肋骨和臀部之间机体侧面明显不适感为指征的疾病。', '全身性肌无力': '多个解剖部位的肌肉张力减弱。',
                   '生长抑制': '机体生长低于预期状态。', '头部软组织坏死': '头部软组织出现坏死过程的疾病。', '关节积液': '关节内过多的液体聚积，通常由关节炎症所致。',
                   '关节活动度低下': '任何关节的灵活性出现降低。', '颈椎关节活动度低下': '颈椎关节灵活性降低。', '腰椎关节活动度低下': '腰椎关节的灵活性降低。',
                   '脊柱后凸': '脊柱胸部段出现异常弯曲的状态。', '脊柱前凸': '脊柱腰椎部分出现异常弯曲的状态。', '肌肉痉挛': '一种以由肌肉或肌群发出的明显痉挛为特点的非正常状态。',
                   '下肢肌无力': '下肢肌肉强度减弱的疾病。', '躯体肌无力': '机体躯干部位的肌肉强度减弱的疾病。', '上肢肌无力': '上肢肌肉的强度出现降低。',
                   '骨骼肌畸形': '肌肉骨骼系统出现畸形。', '肌痛': '一块或一组肌肉出现明显不适感。', '肌炎': '骨骼肌出现炎症反应的疾病。', '颈部痛': '一种以颈部明显不适感为特征的疾病。',
                   '颈部软组织坏死': '颈部软组织出现坏死的疾病。', '骨坏死': '由于血液供应中断导致的骨组织坏死性改变。骨组织的坏死性改变导致骨结构的塌陷和破坏通常会影响长骨的骨骺。',
                   '颚骨骨坏死': '颚骨出现坏死的疾病。', '骨质疏松症': '骨量较少，伴有骨皮质的厚度降低和骨松质骨小梁数量减少（但化学结构不变），可以引起骨折发生。',
                   '四肢痛': '一种以上肢或下肢明显不适感觉为特征的疾病。', '骨盆软组织坏死': '骨盆软组织出现坏死的疾病。',
                   '横纹肌溶解': '一种以肌肉组织的分解导致肌纤维内容物释放到血流中为特征的疾病。', '肩袖损伤': '一种以肩部回旋肌腱群损伤为特征的疾病。', '脊柱侧凸': '脊柱侧凸、畸形。',
                   '下肢软组织坏死': '下肢软组织出现坏死的疾病。', '上肢软组织坏死': '上肢软组织出现坏死的疾病。', '浅表软组织纤维化': '浅表软组织出现纤维变。',
                   '牙关紧闭': '由于咀嚼肌涉及的关节活动度降低，导致不能完全张口。', '肢体不等长': '下肢或上肢长度存在不一致的现象。', '肿瘤化疗引起的白血病': '由化疗药物致突变效应引起的白血病。',
                   '骨髓增生异常综合征': '骨髓不能产生正常血液细胞为特征的疾病。', '皮肤乳头状瘤（疣）': '一种以皮肤存在一个或多个小疣状突起为特质的疾病。',
                   '治疗相关的恶性肿瘤': '最可能是由由先前存在的恶性肿瘤的治疗手段所导致的恶性肿瘤。', '肿瘤出血': '一种以肿瘤部位出血为特征的疾病。',
                   '癌痛': '一种以肿瘤明显不适为特征的疾病，其可能压迫神经，阻塞血管，发炎或因转移而破裂。', '外展神经病变': '一种以外展神经功能障碍为特征的疾病（第六颅神经）',
                   '副神经病变': '一种以副神经功能障碍为特征的疾病（第〸一颅神经）。', '听神经病变NOS': '一种以听神经功能障碍为特征的疾病（第八颅神经）。',
                   '静坐不能': '静坐时感觉不舒服或坐立不安的，这是某些精神类药物的副作用。', '失忆': '由系统性和广泛性丢失记忆为特征的疾病。', '嗅觉异常': '一种以嗅觉感受改变为特征的疾病。',
                   '失音': '不能发音。它是由于声带受伤所致或是功能性的（精神性）。', '蛛网膜炎': '蛛网膜炎症和邻近蛛网膜下腔的炎症。',
                   '共济失调': '由于肌肉活动缺乏协调性导致自主活动障碍或不能进行自主活动。', '臂丛神经受损': '臂丛区域性的感觉异常，显著的不适和肌无力，手臂或手活动受限。',
                   '中枢神经系统坏死': '大脑或脊髓坏死。', '脑脊液外漏': '脑脊液外漏至周围组织。', '认知障碍': '认知功能显著改变。', '注意力集中障碍': '注意力集中能力的衰退。',
                   '意识水平降低': '认知能力和反应能力下降。', '构音障碍': '由于发音肌肉不能协调完好，所以只能缓慢和含糊不清的发音。', '感觉迟钝': '由于感官知觉的扭曲，导致了异常或不愉悦的感觉。',
                   '味觉障碍': '对食物味觉感知异常，它有可能与嗅觉功能降低相关。', '言语障碍': '经常由于大脑损伤，而导致言语功能障碍。', '大脑水肿': '由于大脑内过多液体蓄积而导致的肿胀。',
                   '脑病': '大脑的病理学变化导致的障碍。', '椎体束外疾病': '以反常的，重复性的，非自主性肌肉活动，疯狂的语言，坐立不安为特征的疾病。', '面部肌肉无力': '定义',
                   '面部神经功能障碍': '一种以面神经功能障碍为特征的疾病（第七颅神经）。', '舌咽神经功能障碍': '一种以舌咽神经功能障碍为特征的疾病（第九颅神经）。',
                   '格林巴利综症（急性感染性多发性神经根神经炎）': '一种以身体免疫系统攻击周围神经系统导致上行麻痹为特征的疾病。', '头痛': '头部不同区域出现明显不适感，不限于神经分布区域。',
                   '脑积水': '由于脑室中脑脊液异常增多引起的疾病。', '嗜睡': '白天睡眠时间过长为特征的疾病。', '舌下神经损伤': '由于舌下神经（第〸二颅神经）功能损伤引起的疾病。',
                   '颅内出血': '颅内出血为特征的疾病。', '脑血管缺血': '由于神经受损引起脑血管被血栓或栓塞堵住引起脑供血不足。', '昏睡': '以精神或身体无活力为特点的意识减弱。',
                   '脑白质病': '星状细胞增生溶解反应伴随多个脑部区域无炎症性的坏疽。', '记忆受损': '记忆功能的减退。', '假性脑脊膜炎': '由于脑膜刺激引起的颈部僵硬，头痛，畏光等症状。',
                   '不自主运动': '不受控制或没有意义的活动。', '重症肌无力': '表现为任意骨骼肌无力和快速疲劳的疾病。', '神经痛': '一根神经或多根神经丛刺激性的疼痛。',
                   '眼球震颤': '眼球不由自主的活动。', '动眼神经病变': '由动眼神经病变（第3对颅神经）引起的疾病。', '嗅觉神经障碍': '由嗅觉神经（第1对颅神经）功能障碍引起的疾病。',
                   '感觉异常': '由感觉神经障碍导致皮肤麻痹，使压迫、使变冷、使变热。', '外周运动神经障碍': '由外周运动神经受损或功能障碍引起的疾病。',
                   '外周感觉神经障碍': '由外周感觉神经受损或功能障碍引起的疾病。', '幻觉痛': '已被切除肢体或脏器所在的部位出现明显的不适感。', '晕厥先兆': '出现头昏眼花的症状，有可能出现晕厥。',
                   '锥体束综合征': '脊髓的皮质脊髓（锥体）束功能出现失常。症状包括', '脊神经根炎': '由于神经根炎症引起的疾病。患者由于神经根连接部脊髓受到压迫，神经通路产生明显的放射性不适。',
                   '复发性喉部神经痉挛': '由于再发性喉部神经痉挛引起的疾病。',
                   '可逆的后部脑白质病综合征': '由于影像学发现后部脑白质病变引起的头痛，神经状态改变，视觉障碍，痉挛。可能由于高血压脑病，子痫，免疫抑制剂或激素制剂使用，可能是急性或亚急性反复症状。亦称脑后部可逆性脑病综合征（PRES）。',
                   '癫痫发作': '由于大脑或脑干发出的指令，引起突然的，不自主的肌肉收缩。', '嗜睡症': '过多的睡眠或者困倦引起的。',
                   '痉挛状态': '由于不随意肌肉活动增多而影响自主活动，影响走路，活动和语言表达。', '脊髓压迫症': '以脊髓受压迫为特征的疾病。',
                   '中风': '因动脉阻塞（血栓形成或栓塞）引起脑供血减少或缺乏进而导致神经损伤的疾病', '晕厥': '由于脑供血不足导致自发的意识丧失。', '腱反射减弱': '以深腱反射低于正常为特征的疾病。',
                   '短暂性脑缺血发作': '一过性的脑血管事件（不超过24小时），但没有脑血管器质性伤害。', '震颤': '全身或某个肢体不由自主的颤动。',
                   '三叉神经障碍': '三叉神经（第5对颅神经）功能障碍引起的疾病。', '滑车神经障碍': '滑车神经（第4对颅神经）功能障碍引起的疾病。',
                   '迷走神经障碍': '由于迷走神经障碍（第10对颅神经）引起的疾病。', '血管迷走神经反应': '突然的血压下降，心动过缓，外周血管舒张导致意识丧失，它是由于刺激迷走神经引起。',
                   '胚胎生长迟缓': '由于胚胎生长抑制，导致胚胎无法生长至正常大小。', '妊娠丢失': '宫内死亡。', '早产': '在妊娠正常结束前分娩出存活的婴儿，一般在20周至37周之间。',
                   '焦虑不安': '易激惹的和精神紧张等不愉快感引起的烦躁不安状态。', '性快感缺失': '不能达到性高潮。',
                   '焦虑': '在无明显的刺激的情况下，感到危险和恐惧，伴随烦躁不安、紧张、心动过速和呼吸困难。', '意识模糊': '缺少清晰、有条理的想法和行为。',
                   '延迟性高潮': '由于性功能障碍引起的高潮延迟。', '谵妄': '急性而突然的出现意识混乱，错觉，行为失常，注意迟钝，烦躁和幻觉的状态。通常，是可逆性的。',
                   '妄想': '虽然与证据和常识相悖，仍坚持与现实不符的错误的个人信念。', '抑郁症': '悲痛或不开心。', '欣快': '相对某些事件和刺激，出现不相称的、过度的愉悦感。',
                   '幻觉': '在没有外界刺激情况下，有错误的感觉。', '失眠症': '很难入睡和/或保持睡眠状态。', '易激惹': '对刺激或生理激发（疼痛，惊恐，药物，情绪状态或医疗条件）出现异常反应。',
                   '性欲降低': '性欲降低引起的疾病。', '性欲增强': '性欲增强引起的疾病。', '躁狂': '精神激动，表现为活动过度，行为错乱，情绪高涨。',
                   '人格改变': '个人行为和思考方法显著改变。', '精神病': '以个性改变，正常功能损害，脱离现实为特点的疾病。可能为精神分裂症，双相型障碍，脑瘤等的表现。',
                   '烦躁不安': '不能休息，放松或保持安静。', '自杀观念': '存在结束自我生命的观念。', '自杀企图': '采取行为想结束自己生命。',
                   '急性肾损伤': '肾功能急性受损（2 周内）引起的疾病，常分为肾前型（血流较少），肾型（肾脏损伤），肾后型（出路堵塞）。', '膀胱穿孔': '膀胱壁破裂。',
                   '膀胱痉挛': '膀胱壁突然而不自主性的收缩。', '慢性肾脏疾病': '肾功能渐进性的、（通常）永久性的的减退，直至肾衰竭。', '非传染性膀胱炎': '膀胱炎症并不是由于尿路感染引起的。',
                   '排尿困难': '以排尿疼痛为特征的疾病。', '尿糖': '实验室检查结果显示尿糖的疾病。', '血尿': '实验室检查有血尿的疾病。', '血红蛋白尿': '实验室检查发现尿液中有游离血红蛋白。',
                   '肾病综合征': '表现为重度水肿、蛋白尿和低白蛋白血症等症状的疾病；肾功能不全的指征。', '尿结石': '肾盂内形成结石。',
                   '肾绞痛': '突发性的，严重的放射至腹股沟区域疼痛，通常是结石/肾结石引起的。', '肾出血': '肾脏出血。', '尿瘘': '泌尿系统和其他脏器或解剖部位之间存在的异常通道。',
                   '尿频': '排尿时间间隔短。', '尿失禁': '尿液从膀胱排出不受自主意识控制。', '尿潴留': '由于不能排尿，引起膀胱内尿液积累过多。',
                   '尿路堵塞': '尿道内中发生内容物正常流向受阻的疾病。', '尿道疼痛': '尿道部位出现显著不适感。', '尿急': '突然而强烈的排尿欲望。', '尿液变色': '尿液颜色改变。',
                   '闭经': '月经异常停止至少连续3个月经周期。', '无精子症': '实验室检查发现精液中完全无精子。', '乳房萎缩': '乳房发育低下。', '乳房疼痛': '乳房部位出现显著不适感。',
                   '痛经': '月经期间腹部异常疼痛。', '女性性交困难': '性交疼痛或困难。', '射精障碍': '射精困难，包括早泄，延迟，逆行和疼痛性射精。',
                   '勃起障碍': '性行为中，勃起难以持久或不能反复勃起。', '输卵管堵塞': '输卵管中发生内容物正常流向受阻的疾病。', '获得性女性化': '由于外部因素，男性出现女性第二性征发育。',
                   '生殖器水肿': '生殖器内水分过多引起肿胀。', '男子乳腺发育': '男子乳房过度发育。', '输卵管积血': '输卵管内有血液沉积。',
                   '月经失调': '相对于基线来说，月经周期或者月经持续时间发生改变。', '泌乳障碍': '乳汁分泌困难，不仅局限于妊娠女性，男性也会出现。', '月经过多': '经期经血过多。',
                   '乳头畸形': '乳头变形引起的疾病。', '精子减少症': '精液中精子数目减少。', '卵巢出血': '卵巢出血。', '卵巢破裂': '卵巢组织撕裂或破裂。',
                   '排卵疼痛': '月经周期之间，在排卵过程中出现一侧腹部显著不适感。', '骨盆底肌无力': '骨盆底肌肉强度减退。', '骨盆疼痛': '骨盆部位出现显著不适感。',
                   '阴茎疼痛': '阴茎部位出现显著不适感。', '会阴疼痛': '生殖器官和肛门之间区域出现显著不适感。',
                   '过早绝经': '卵巢功能过早衰竭，症状包括潮热，夜间盗汗，情绪不稳及性欲减退。实验室检查提示促黄体生成素（LH）及卵泡刺激素（FSH）升高。', '前列腺出血': '前列腺出现出血的疾病。',
                   '前列腺堵塞': '继发于前列腺扩大所导致尿道受压，引起排尿困难（用力排尿、排尿速度减慢、膀胱排空不完全）。', '前列腺疼痛': '前列腺部位出现显著不适感。',
                   '阴囊疼痛': '阴囊区域出现显著不适感。', '精索出血': '精索出现出血。', '精索堵塞': '精索内容物正常流向受阻。', '睾丸疾病': '睾丸外观异常或功能异常。',
                   '睾丸出血': '睾丸部位出现出血。', '睾丸疼痛': '睾丸部位出现显著不适感。', '子宫瘘': '子宫与其他脏器或解剖部位间异常通道。', '子宫出血': '子宫出现出血的疾病。',
                   '子宫堵塞': '子宫开口受阻。', '子宫疼痛': '子宫部位出现显著不适感。', '阴道分泌物': '指阴道分泌物。宫颈腺分泌的粘液由阴道自然排除，特别是在分娩期间。',
                   '阴道干燥': '阴道内发痒或干燥等不适感。', '阴道瘘': '阴道与其他脏器或解剖部位之间异常的通道。', '阴道炎症': '阴道出现炎症反应，症状可能包括发红、水肿、显著不适和阴道排出物增多。',
                   '阴道堵塞': '阴道腔阻塞。', '阴道疼痛': '阴道部位出现显著不适感。', '阴道穿孔': '阴道壁刺破。', '阴道狭窄': '阴道狭窄。',
                   '成人呼吸窘迫综合征': '无肺部基础疾病下，出现渐进的、危及生命的呼吸困难，通常由重度创伤和手术所致。',
                   '变应性鼻炎': '鼻粘膜炎症，由lgE介导的外部变应原应答引起。这些炎症也可能涉及鼻窦，眼睛，中耳和咽部。症状包括打喷嚏，鼻充血，鼻溢液和鼻痒。', '呼吸暂停': '呼吸中断。',
                   '误吸': '由于固体或液体吸入肺部引起的疾病。', '肺不张': '部分或全部肺部萎陷。', '气管瘘': '气管与其他器官或解剖部位间出现的异常通道。',
                   '支气管堵塞': '支气管被堵塞，主要是支气管分泌物和渗出液所致。', '支气管狭窄': '支气管狭窄引起的。', '支气管瘘': '支气管与胸腔之间的异常通道。',
                   '支气管出血': '支气管壁和/或肺实质出血。', '支气管痉挛': '支气管壁平滑肌突然收缩。', '乳糜胸': '由于胸腔内淋巴液积累引起乳液状胸腔积液（体液收集异常）。',
                   '咳嗽': '突然，反复，痉挛性的胸腔收缩，导致肺部气体剧烈释出，并常伴特征性的声音。', '呼吸困难': '呼吸出现困难。', '鼻出血': '鼻子出血。',
                   '呃逆': '由于不由自主的打开或关闭声门而引起的喉间频频作声，由横膈膜痉挛引起。', '声嘶': '喉头发出的粗糙的、刺耳的声音。', '缺氧': '机体内氧气水平降低。',
                   '喉水肿': '喉部由于积累过多体液而引起的水肿。', '喉瘘': '喉部与其他脏器或解剖部位之间形成的异常通道。', '喉出血': '喉部出血。', '喉部炎症': '喉部出现发炎。',
                   '喉部粘膜炎': '喉部粘膜发生溃疡或炎症。', '喉部梗阻': '喉部气道堵塞。', '喉部狭窄': '喉部气道变窄。', '咽喉部感觉迟钝': '咽喉部持久的不适感。',
                   '喉痉挛': '声带肌肉阵发性痉挛收缩。', '纵隔出血': '纵隔出现出血。', '鼻塞': '由于粘膜水肿导致鼻腔堵塞。', '口咽疼痛': '口咽中明显不适的感觉。',
                   '咽瘘': '咽部与其他器官或解剖部位间异常通道。', '咽部出血': '咽部出血。', '咽部粘膜炎': '涉及咽部粘膜的溃疡或炎症。', '咽部坏死': '发生在咽部的坏死过程。',
                   '咽部狭窄': '咽部气道变窄。', '咽喉疼痛': '咽喉区域出现显著不适感。.', '胸腔积液': '胸腔内体液增加。症状包括呼吸短促，咳嗽，胸部明显不适。', '胸腔出血': '胸腔内出血。',
                   '胸膜痛': '胸膜出现明显的不适感。', '肺炎': '炎症局灶性或弥散性影响肺实质。', '气胸': '胸腔内异常气体，导致肺衰竭。',
                   '后鼻滴涕': '由于鼻腔或喉咙后部粘液分泌物过多，导致喉痛和/或咳嗽。', '排痰性咳嗽': '咳嗽时咳出痰液。', '肺水肿': '由于肺部组织体液蓄积导致气体交换紊乱，可能导致呼吸衰竭。',
                   '肺部纤维化': '由于肺部组织不断被结缔组织替代，导致进行性呼吸困难，呼吸衰竭或右心衰竭。', '肺瘘': '肺部与其他器官或解剖部位间的异常通道。',
                   '肺动脉高压': '由于肺部或心脏疾病导致肺循环内血压升高。', '呼吸衰竭': '呼吸系统气体交换受损导致低氧血和组织内氧气含量降低，可能伴动脉血中二氧化碳水平升高。',
                   '维甲酸综合征': '最初由全反式维甲酸治疗导致的体重增加，呼吸困难，胸腔和心包积液，白细胞增多和/或肾脏衰竭。', '鼻溢': '鼻子排出过量粘液分泌物。', '鼻窦疾病': '鼻窦介入性疾病。',
                   '鼻窦疼痛': '始于上颌窦，集中于脸部，眼睛间或上颌牙间的明显不适。', '睡眠呼吸暂停': '睡眠中出现短期呼吸中断。', '打喷嚏': '鼻腔内空气不自觉排出。',
                   '喉痛': '喉部出现显著不适感。', '喘鸣': '由喉部或上呼吸道堵塞引起高分贝呼吸声。', '气管粘膜炎': '气管粘膜炎症或溃疡。', '气管狭窄': '气管变窄。',
                   '声音改变': '说话声音和/或速度改变。', '哮喘': '呼吸时出现高分贝口哨声，由呼吸道狭窄或闭塞引起。', '脱发': '在一定年龄的个人中，机体特定部位毛发密度较正常状态减少。',
                   '体臭': '机体内细菌生长导致体味异常。', '大疱性皮炎': '皮肤发生以充盈大疱为特点的炎症。', '皮肤干燥': '皮肤变薄、变暗，但是毛孔一般正常，纹理呈纸样薄纹理。',
                   '湿疹': '皮肤发痒、发红、发炎、硬结、变厚、有鳞和/或起水泡。', '多形性红斑': '以苍白区域周围呈粉红色包围圈为特点的靶病灶。',
                   '红皮病': '皮肤有炎性红斑和脱落，炎症涉及到超过90%体表面积。', '脂肪萎缩症': '脂肪组织出现萎缩为特征的疾病。', '发色改变': '头发颜色改变或正常色素的丢失的疾病。',
                   '毛发质地异常': '毛发质地改变的疾病。', '多毛症': '对于特定年龄和种族，在一个特定身体部位，毛发长度和密度增加，超过可接受范围。', '多汗症': '出汗过多为特征的疾病',
                   '角化过度症': '皮肤外层增厚为特征的疾病。', '少汗症': '出汗减少。', '脂肥大': '多次皮下胰岛素注射部位出现皮下脂肪组织过度增生。',
                   '指甲改变': '疾患特征为指甲发生病理改变。', '甲变色': '指甲板颜色改变。', '指甲丢失': '指甲的全部或一部分丢失。', '指甲隆起': '指甲水平或垂直隆起。',
                   '皮肤疼痛': '疾患特征为皮肤出现显著不适感。', '手足综合征': '手掌和脚底出现发红，明显不适，肿胀和麻刺感。', '光敏感性': '皮肤对光的敏感性增加。',
                   '瘙痒症': '剧烈的瘙痒感为特征的疾病。', '紫癜': '皮肤和粘膜的出血部位。新的损伤部位为微红色，原来的损伤部位为暗紫色，最后变为棕黄色。',
                   '痤疮样皮疹': '出现丘疹和脓包为特征的疾病，主要出现在面部，头皮，上胸部和背部。',
                   '斑丘疹': '出现斑疹（扁平）和丘疹（突起）。也称为麻疹，是最常见的皮肤不良事件之一，常常影响上半身，向心性发展，伴有瘙痒。', '头皮痛': '疾患特征为头顶部和头背部皮肤出现显著不适感。',
                   '皮肤萎缩': '表皮和真皮层出现退化和变薄。', '皮肤色素沉着过多': '由于过多黑色素的沉积，导致皮肤变黑。', '皮肤色素减减退': '皮肤色素的丢失(例如白化病)。',
                   '皮肤硬结': '皮肤区域变硬。', '皮肤溃疡': '疾患特征为皮肤出现局限的、糜烂性病灶。',
                   'Stevens—Johnson综合征': '小于10%的整个皮肤从真皮分离。该综合征考虑是皮肤和粘膜过敏引起。', '皮下气肿': '疾患特征为皮下组织出现气肿。',
                   '毛细血管扩张': '小血管局部扩张导致皮肤或粘膜红色变。', '中毒性表皮坏死': '超过30%面积的皮肤从真皮脱落，该综合征考虑是皮肤和粘膜过敏引起。',
                   '荨麻疹': '以中央为白色的，周边是红色区域为特征的瘙痒性皮疹。', '动脉血栓栓塞症': '在动脉发生的由于血栓形成堵塞动脉血管的疾患。',
                   '毛细血管渗漏综合征': '血管内液体渗漏至血管外。该综合征见于发生休克，低流量状态，缺血再灌注损伤，毒血症，用药或中毒后处于广泛的毛细血管渗漏状态的患者中。可能导致全身性水肿和多器官功能衰竭。',
                   '潮红': '本障碍的主要特征为皮肤特别是面部、颈部和胸部皮肤的偶发性潮红。', '血肿': '由于血管壁破裂，导致血液凝集在一个器官、部位或组织。',
                   '潮热': '短暂性的不舒服的强烈身体发热感，潮红，有时伴有出冷汗。', '高血压': '病理性的血压升高。', '低血压': '在特定环境下，血压低于正常下限。',
                   '淋巴漏': '淋巴液泄漏至周围组织或体腔。', '淋巴水肿': '过多的淋巴液集中在组织里导致组织肿胀。', '淋巴囊肿': '包含淋巴液的囊性损伤。',
                   '外周缺血': '四肢血液循环受损为特征的疾病。', '静脉炎': '静脉壁发生炎症。', '血栓性浅静脉炎': '四肢浅静脉发生血栓和炎症。',
                   '上腔静脉综合征': '上腔静脉内血流堵塞，体征和症状包括肿胀和脸部，颈部和上臂发绀，咳嗽，端坐呼吸和头痛。', '血栓栓塞事件': '血栓通过血流流至远端，堵塞血管。',
                   '血管炎': '血管壁发生炎症为特征的疾病。'}
    example_json = """
        [
            {
            "test_name": "咳嗽",
            "collect_time": "2025-06-12 08:11:12",
            "report_time": ""
            },
            {
            "test_name": "敏性皮炎(可能)",
            "collect_time": "2025-04-12 06:11:12",
            "report_time": ""
            },
            {
            "test_name": "血压高",
            "collect_time": "2025-05-02 06:15:18",
            "report_time": "2025-05-02 16:10:08"
            },
            {
            "test_name": "双肺呼吸音粗",
            "collect_time": "",
            "report_time": ""
            },
            {
            "test_name": "胸部CT示双肺感染",
            "collect_time": "2025-08-18 07:41:22",
            "report_time": "2025-08-18 17:49:28"
            }
        ]  
        """
    content = f"""
    【OCR识别结果】
    {ocr_text}
    以上【OCR识别结果】是一个医生对患者当前状态的记录，作为一名专业医疗人员，我需要您帮助我完成以下的任务
    【任务目标】从原始病历中精准提取所有诊断、异常症状、可疑诊断、可疑异常症状以及医疗相关不良事件等信息，严格依据病历内容，杜绝任何推断或改写。按以下思路进行整理，并以并以json格式返回：
    1、识别疾病或症状名称：全面、准确找出病史中涉及的所有疾病、症状或健康状况名称，涵盖医学术语、俗语、缩写等各种表述形式。
    2、关联描述信息：针对每个识别出的疾病或症状，提取并记录相关描述信息，包括但不限于起始时间、严重程度变化、持续时长、结束时间、治疗过程、复发情况等。
    3、时间线梳理：依据病史中的时间线索，梳理每个疾病或症状的开始、发展、结束顺序，以便清晰呈现病情发展态势。
    4、影响因素分析：留意病史中提及的可能影响疾病发展的因素，如生活习惯、环境变化、家族病史等，并将其与对应的疾病或症状建立关联。
    5、时间信息提取：针对每个识别出的疾病或症状，提取并记录相关的采集时间（collect_time）和报告时间（report_time），采集时间指该疾病或症状被发现、检测到的时间，报告时间指该疾病或症状被记录、报告的时间，若病历中未提及则留空。

    一、诊断提取规则
    确诊诊断：检索所有明确诊断表述，并标注原文位置。
    [示例1]急性髓系白血病(MRD阳性，伴MR遗传学改变)：病史提供，诊断明确；@诊断及诊断依据
    疑似诊断
    标记含 "？"" 待排 ""不除外" 等不确定表述的诊断。
    [示例1]敏性皮炎(可能)
    
    
    二、异常症状 / 体征提取细则
    主诉异常（患者自述）
    [示例1]咳嗽、咳黄痰@住院治疗
    查体异常（医护人员记录）
    [示例1]双肺呼吸音粗@查体
    生命体征异常
    [示例1]BP：139/97mmHg@查体
    
    影像检查
    区分客观发现与诊断结论。
    [示例1]双侧胸腔积液@现病史
    
    [示例1]胸部CT示双肺感染@现病史
    实验室检查
    [示例]血常规：WBC 6.33 Hb 83 PLT 115@现病史
    
    
    三、示例输出模板
    {example_json}
    
    "注意":输出的json模版需要包括 ```json```
    
    **校验清单**  
    ✅ 每个提取项必须有原始文本支撑  
    [示例]1、膀胱出血@现病史
    ✅ 影像描述需区分「客观发现」与「诊断结论」
    """
    temp = get_general_prompt_sql(OCR_EXTRACTION_TEXT)
    content = temp.format(ocr_text=ocr_text, example_json=example_json)
    logger.info(content)
    if model_type == 'DeepSeek-R1-Distill-Qwen-32B':
        print("🚀 开始调用【Deepseek-distil-Qwen32B】大模型转换 OCR 结果为 JSON...")
        response = deepseek_r1_qwen_32b(content)
    if model_type == 'qwen3-32b':
        print("🚀 开始调用【qwen3_32b】大模型转换 OCR 结果为 JSON...")
        response = qwen3_32b(content)
    result = response.json()
    inputText = ocr_text
    outputText = result
    think = result["choices"][0]["message"]["content"]
    start_index = think.find("<think>") + len("<think>")
    end_index = think.find("</think>")
    think = think[start_index:end_index]
    generated_tokens = result.get('usage', {})
    # 打印大模型返回的完整JSON
    # print(f"📄 大模型返回的 OCR JSON:\n{json.dumps(result, indent=4)}")

    content = result.get("choices", [{}])[0].get("message", {}).get("content", "{}")
    cleaned_content = clean_llm_response(content)

    print(f"📄 处理后的 JSON OCR 结果:\n{cleaned_content}")
    pattern = r'```json(.*?)```'
    match = re.search(pattern, cleaned_content, re.DOTALL)
    cleaned_content = {"test_results": []}
    if match:
        cleaned_content = match.group(1)
    cleaned_content = json.loads(cleaned_content)
    return cleaned_content, inputText, outputText, think, generated_tokens


# 清理大模型返回的内容，去除 <think> 标签内的内容
def clean_llm_response(content):
    """清理大模型返回的内容"""
    # 使用正则去除 <think> 标签及其内容
    return re.sub(r"<think>.*?</think>", "", content, flags=re.DOTALL).strip()


def process_images_in_directory(images_directory):
    """遍历指定文件夹中的所有图片文件并进行 OCR 识别"""
    ocr_texts = []
    print("🔍 开始进行 OCR 识别...")

    for filename in sorted(os.listdir(images_directory)):
        image_path = os.path.join(images_directory, filename)

        if image_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
            ocr_result = ocr_main(image_path)
            char_count = len(ocr_result)
            print(f"📄 图片 {filename} 识别完成，字符数: {char_count}")

            if ocr_result:
                ocr_texts.append(f"## {filename}\n{ocr_result}\n")

    print("✅ OCR 识别全部完成！")
    return "\n".join(ocr_texts)


def main(images_directory):
    """主函数：获取 OCR 识别并将结果转换为 JSON"""
    ocr_text = process_images_in_directory(images_directory)
    if ocr_text:
        ocr_json = request_llm_api1(ocr_text)
        print("OCR 转换为 JSON 后的结果：")
        print(ocr_json)
    else:
        print("⚠️ 没有获取到 OCR 识别结果")


def hw_ocr_general_text(
    image_path=None,
    image_url=None,
    detect_direction=True,
    quick_mode=False,
    character_mode=False,
    language="zh",
    single_orientation_mode=True,
    pdf_page_number=1
):
    """
    调用华为 OCR API 进行文字识别。
    :param project_id: 项目 ID
    :param token: 认证 Token
    :param image_path: 本地图片路径（与 image_url 二选一）
    :param image_url: 远程图片 URL（与 image_path 二选一）
    :param detect_direction: 是否校正图片倾斜角度
    :param quick_mode: 是否开启快速模式
    :param character_mode: 是否开启单字符模式
    :param language: 语言选择（默认为中英文）
    :param single_orientation_mode: 是否开启单朝向模式
    :param pdf_page_number: 指定 PDF 识别的页码
    :param endpoint: OCR API 终端节点
    :return: 识别结果
    """
    try:
        if image_path:
            # 处理图片路径或字节数据
            if isinstance(image_path, str):
                with open(image_path, "rb") as file:
                    image_bytes = file.read()
            else:
                image_bytes = image_path
        elif image_url:
            # 处理远程图片URL
            response = requests.get(image_url)
            response.raise_for_status()
            image_bytes = response.content
        else:
            raise ValueError("image_path 或 image_url 其中之一必须提供")

        # 使用统一OCR服务，映射detect_direction参数到use_correction
        use_correction = detect_direction  # 映射参数
        from common.clients.ocr_service import process_ocr
        result = process_ocr(image_bytes, use_correction=use_correction)
        
        # 返回原始OCR结果格式，保持向后兼容
        return result["ocr_result"]
        
    except Exception as e:
        print(f"❌ OCR服务调用失败: {e}")
        raise


def format_ocr_text(result, image_path):
    """处理 OCR 识别结果，返回格式化的文本"""
    def rotate_point(point, Cx, Cy, theta):
        """计算旋转后的坐标"""
        x, y = point

        # 平移到中心
        x_prime = x - Cx
        y_prime = y - Cy

        # 应用旋转公式
        x_double_prime = x_prime * math.cos(theta) + y_prime * math.sin(theta)
        y_double_prime = -x_prime * math.sin(theta) + y_prime * math.cos(theta)

        # 平移回原坐标系
        x_rot = x_double_prime + Cx
        y_rot = y_double_prime + Cy

        return [x_rot, y_rot]

    # 提取带位置信息的文本块
    text_blocks = []
    if "result" in result and "words_block_list" in result["result"]:
        ocr_result = result["result"]
        direction = ocr_result["direction"]

        if direction:
            print(f"direction: {direction}")
            # image = Image.open(image_path)
            image_file = io.BytesIO(image_path)
            # 用 Image.open 打开文件对象
            image = Image.open(image_file)
            # 获取图片宽高
            W, H = image.size
            Cx, Cy = W / 2, H / 2  # 中心点

            # 顺时针旋转角度（弧度制）
            theta = math.radians(direction)

            for box in ocr_result["words_block_list"]:
                location = []
                for point in box['location']:
                    # 旋转单个点
                    rotated_point = rotate_point(point, Cx, Cy, theta)
                    location.append(rotated_point)
                box['location'] = location

        # 处理文本块
        for block in result["result"]["words_block_list"]:
            if "words" in block and "location" in block:
                text = block["words"]
                location = block["location"]

                # 计算文本块中心点坐标
                center_x = sum(point[0] for point in location) / 4
                center_y = sum(point[1] for point in location) / 4

                # 计算高度 (使用左上角和左下角的y坐标差)
                # 这里我们使用中心点高度作为参考
                min_y = min([p[1] for p in location])
                max_y = max([p[1] for p in location])
                height = max_y - min_y

                text_blocks.append({
                    "text": text,
                    "center_y": center_y,
                    "center_x": center_x,
                    "height": height if height > 0 else 1  # 确保高度为正
                })

    if not text_blocks:
        return ""

    # 基于文本块高度分析确定行高容差
    heights = [block["height"] for block in text_blocks]

    if not heights:
        return ""

    # avg_height = sum(heights) / len(heights)

    # 使用中位数可能比平均值更稳健
    heights.sort()
    median_height = heights[len(heights) // 2]

    # 根据中位数计算行高容差
    line_height_tolerance = median_height * 0.7  # 可以根据实际情况调整系数

    # 使用确定的line_height_tolerance分组文本块
    rows = {}

    for block in text_blocks:
        # 查找最近的行
        found_row = False
        for row_y in rows.keys():
            if abs(block["center_y"] - row_y) < line_height_tolerance:
                rows[row_y].append(block)
                found_row = True
                break

        # 如果没有找到匹配的行，创建新行
        if not found_row:
            rows[block["center_y"]] = [block]

    # 对每一行按x坐标排序，然后将行按y坐标排序
    formatted_text = ""
    for row_y in sorted(rows.keys()):
        row_blocks = sorted(rows[row_y], key=lambda b: b["center_x"])
        line_text = " ".join([block["text"] for block in row_blocks])
        formatted_text += line_text + "\n"

    return formatted_text.strip()


def process_row(task_id):
    print('开始处理下面的row了！！！')
    task = AeTrackerTask.objects.filter(id=task_id, category=AeTrackerTask.OCR_EXTRACTION, delete_flag=0).first()
    if not task:
        print(f"没有发现任务")
        return
    subject_id = task.subject_id
    subject_item_id = task.subject_item_id
    sql = f"""
        select 
        file_id 
        from subject_medical_info
        where delete_flag = 0 and ocr_time is not null 
        and subject_id = '{subject_id}'
        and subject_item_id = '{subject_item_id}'
    """
    #
    # df1 = pd.read_sql_query(sql, db_erp)
    df1 = sql_to_df('default', sql)

    id_set = df1['file_id'].tolist()
    if not id_set:
        print("未找到符合条件的 file_id，无法继续查询。")
        df2 = pd.DataFrame()
    else:
        id_str = ', '.join(map(str, id_set))
        sql2 = f"""
            SELECT
                *
            FROM
                subject_medical_file
            WHERE
                id IN ({id_str})
        """
        # df2 = pd.read_sql_query(sql2, db_erp)
        df2 = sql_to_df('default', sql2)

    params = {
        'which_need_update_id': task_id,
        'mask_status': 'IN_PROGRESS'
    }
    sql = text(
        f"""update ae_tracker_task set status=:mask_status where id=:which_need_update_id""")
    # print(sql)q
    with db_erp.begin() as conn:
        conn.execute(sql, params)
    conn.close()
    print(task_id)
    try:
        minio_client = get_minio_client()
        test_result_ocr_instances = []
        test_report_list = []
        test_result_list = []
        ocr_text_list = []
        for index, row in df2.iterrows():
            try:
                ocr_json = {"test_results": []}
                bucket_name = row['bucket_name']
                object_name = row['object_name']
                original_filename = row['original_filename']
                # 从 MinIO 中获取对象
                response = minio_client.get_object(bucket_name, object_name)
                # 读取对象内容到内存中
                data = response.read()
                both_conditions_false = True
                if original_filename[-3:].lower() == 'pdf':
                    print('pdf!!!')
                    pdf_doc = fitz.open(stream=io.BytesIO(data), filetype="pdf")
                    # 遍历 PDF 的每一页
                    ocr_texts = []
                    for page_num in range(pdf_doc.page_count):
                        page = pdf_doc.load_page(page_num)
                        # 将页面转换为图像
                        pix = page.get_pixmap()
                        # 创建图像字节流
                        file_bytesX = pix.tobytes()
                        ocr_text = ocr_main(file_bytesX)
                        # result = hw_ocr_general_text(image_path=file_bytesX)
                        # ocr_text = format_ocr_text(result, image_path=file_bytesX)
                        if ocr_text:
                            ocr_texts.append(ocr_text)
                    ocr_text = '\n'.join(ocr_texts)
                    both_conditions_false = False
                if original_filename.lower().endswith('.jpg') or original_filename.lower().endswith(
                        '.jpeg') or original_filename.lower().endswith('.png') or original_filename.lower().endswith(
                    '.webp'):
                    print('图片！！！')
                    file_bytesX = data
                    ocr_text = ocr_main(file_bytesX)
                    # result = hw_ocr_general_text(image_path=file_bytesX)
                    # ocr_text = format_ocr_text(result, image_path=file_bytesX)

                    both_conditions_false = False
                if both_conditions_false:
                    ocr_text = ''
                    print(row)
                    print('文件类型不属于处理范畴！！！')
                    continue

                # result = {}
                # result['subject_id'] = subject_id
                # result['subject_item_id'] = subject_item_id
                # result['ocr_text'] = ocr_text
                # test_result_ocr_instances.append(TestOcrResult(**result))
                ocr_text_list.append(ocr_text)

                ocr_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                which_need_update_id = row['id']
                params = {'ocr_time': ocr_time,
                          'which_need_update_id': which_need_update_id,
                          # 'mask_status': 'COMPLETED'
                          }
                sql = text(
                    f"""update subject_medical_info set ocr_time =:ocr_time where id=:which_need_update_id""")
                with db_erp.begin() as conn:
                    conn.execute(sql, params)
                conn.close()
            except Exception as e:
                print(e)
                print('文件处理错误！！！')
                print(row)

        result = {}
        result['subject_id'] = subject_id
        result['subject_item_id'] = subject_item_id
        separator = '\n\n' + '*' * 80 + '\n\n'
        result['ocr_text'] = separator.join(ocr_text_list)

        test_result_ocr_instances.append(TestOcrResult(**result))
        print(test_result_ocr_instances)
        TestOcrResult.objects.bulk_create(test_result_ocr_instances)

        # params = {
        #     'subject_id': subject_id,
        #     'subject_item_id': subject_item_id,
        #     'ae_ai_current_step': 2
        # }
        # sql = text(
        #     f"""update subject_item_info set ae_ai_current_step=:ae_ai_current_step where subject_id=:subject_id and subject_item_id=:subject_item_id and ae_ai_current_step <= 2""")
        # # print(sql)q
        # with db_erp.begin() as conn:
        #     conn.execute(sql, params)
        # conn.close()

        params = {
            'which_need_update_id': task_id,
            'mask_status': 'COMPLETED',
            'end_time': datetime.now()
        }
        sql = text(
            f"""update ae_tracker_task set status=:mask_status,end_time=:end_time where id=:which_need_update_id""")
        # print(sql)q
        with db_erp.begin() as conn:
            conn.execute(sql, params)
        conn.close()
        print(task_id)
        return True
    except Exception as e:
        print(e)
        params = {
            'which_need_update_id': task_id,
            'mask_status': 'ERROR',
            'end_time': datetime.now()
        }
        sql = text(
            f"""update ae_tracker_task set status=:mask_status,end_time=:end_time where id=:which_need_update_id""")
        # print(sql)q
        with db_erp.begin() as conn:
            conn.execute(sql, params)
        conn.close()
        print(task_id)
        return False


# def main():
#     sql = f"""
#                 select
#                 id,subject_id,subject_item_id
#                 from ae_tracker_task
#                 where delete_flag = 0 and (status = 'TODO' or status = 'IN_PROGRESS') and category = 'OCR_EXTRACTION'
#             """
#     df_task = pd.read_sql_query(sql, db_erp)
#
#     with ThreadPoolExecutor(max_workers=4) as executor:
#         futures = [executor.submit(process_row, row) for _, row in df_task.iterrows()]
#         for future in futures:
#             future.result()


def main():
    parser = argparse.ArgumentParser(description="AE病史-OCR任务")
    parser.add_argument('--task_id', type=int, required=True, help="task_id")
    args = parser.parse_args()
    process_row(args.task_id)


if __name__ == "__main__":
    main()
    close_db_connection()

    # image_path = r'C:\Users\<USER>\Desktop\检验单2.webp'
    # with open(image_path, 'rb') as file:
    #     # 读取文件内容到字节流
    #     file_bytesX = file.read()


    # result = hw_ocr_general_text(image_path=file_bytesX)
    # print(result)
    # ocr_text = format_ocr_text(result, image_path=file_bytesX)
    # print(ocr_text)

    # ocr_text = ocr_main(file_bytesX)
    # print(ocr_text)
    #
    # if ocr_text:
    #     ocr_json = request_llm_api1(ocr_text)
    #     print(ocr_json)

