import os
import json
import ast
import math
import base64
import requests
import re
import pandas as pd
from concurrent.futures import ThreadPoolExecutor
from sqlalchemy import text
from PIL import Image, ImageDraw
import requests
import re
import base64
import ast
import time
import json
from common.minio_client import get_minio_client
from common.tools import sql_to_df, collect_time_choice
import io
import fitz
from datetime import datetime, timedelta
import base64
import argparse

import os
import django
from django.db import connections
from django.conf import settings

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

# python -m script.test_result_format --task_id=1843

django.setup()
from sqlalchemy import create_engine
from sqlalchemy.pool import NullPool
from common.tools import get_db_engin_url
from django.db import transaction
from apps.medical.models import MedicalFileMasked
from apps.ae_tracker.models import TestResult, AeTrackerTask
from apps.subject.models import SubjectItem
from apps.system.models import ModelInvocationLog

# 导入新的test_result_format处理功能
from script.test_result_format_ae_ocr.main import process_medical_ocr

db_erp = create_engine(get_db_engin_url('default'), poolclass=NullPool)


def is_time_format_error(error_message):
    """
    检测异常信息是否为时间格式相关错误

    Args:
        error_message: 异常信息字符串

    Returns:
        bool: 是否为时间格式错误
    """
    time_error_keywords = [
        'invalid format',
        'YYYY-MM-DD HH:MM',
        'DateTimeField',
        'datetime',
        'time data',
        'does not match format',
        'strptime',
        'time format',
        'invalid literal for int()',
        'unconverted data remains'
    ]

    error_str = str(error_message).lower()
    return any(keyword.lower() in error_str for keyword in time_error_keywords)


def fix_time_fields_in_record(record_dict):
    """
    修复记录中的时间字段，将无效时间设置为None

    Args:
        record_dict: 记录字典

    Returns:
        tuple: (修复后的记录字典, 是否进行了时间修复)
    """
    fixed_record = record_dict.copy()
    time_fixed = False

    # 需要检查的时间字段
    time_fields = ['collect_time', 'report_time']

    for field in time_fields:
        if field in fixed_record and fixed_record[field] is not None:
            original_value = fixed_record[field]
            # 将时间字段设置为None
            fixed_record[field] = None
            time_fixed = True
            print(f"时间字段修复: {field} = '{original_value}' -> None")

    return fixed_record, time_fixed


def smart_database_insert(test_result_instances, row_info=None):
    """
    智能数据库插入机制：优先批量插入，失败时降级到单条插入

    Args:
        test_result_instances: TestResult实例列表
        row_info: 行信息，用于日志记录

    Returns:
        tuple: (成功数量, 错误数量, 时间修复数量)
    """
    if not test_result_instances:
        return 0, 0, 0

    success_count = 0
    error_count = 0
    time_fixed_count = 0

    try:
        # 第一步：尝试批量插入
        print(f"尝试批量插入 {len(test_result_instances)} 条记录...")
        TestResult.objects.bulk_create(test_result_instances)
        success_count = len(test_result_instances)
        print(f"✅ 批量插入成功: {success_count} 条记录")
        return success_count, error_count, time_fixed_count

    except Exception as bulk_error:
        print(f"❌ 批量插入失败: {bulk_error}")

        # 第二步：检查是否为时间格式错误
        if is_time_format_error(str(bulk_error)):
            print("🔧 检测到时间格式错误，启动降级插入策略...")

            # 第三步：降级到单条插入，逐条处理
            for i, instance in enumerate(test_result_instances):
                try:
                    # 尝试插入单条记录
                    TestResult.objects.create(**instance.__dict__)
                    success_count += 1

                except Exception as single_error:
                    if is_time_format_error(str(single_error)):
                        # 时间格式错误：修复时间字段后重试
                        try:
                            print(f"🔧 记录 {i+1} 时间格式错误，修复时间字段...")

                            # 获取实例的字典表示
                            instance_dict = {}
                            for field in instance._meta.fields:
                                field_name = field.name
                                if hasattr(instance, field_name):
                                    instance_dict[field_name] = getattr(instance, field_name)

                            # 修复时间字段
                            fixed_dict, was_time_fixed = fix_time_fields_in_record(instance_dict)

                            if was_time_fixed:
                                time_fixed_count += 1

                            # 重新创建实例并插入
                            TestResult.objects.create(**fixed_dict)
                            success_count += 1
                            print(f"✅ 记录 {i+1} 修复后插入成功")

                        except Exception as retry_error:
                            error_count += 1
                            print(f"❌ 记录 {i+1} 修复后仍然失败: {retry_error}")

                            # 记录详细错误信息
                            if hasattr(instance, 'test_name'):
                                print(f"   失败记录信息: test_name={getattr(instance, 'test_name', 'N/A')}")
                    else:
                        # 非时间格式错误：记录错误并跳过
                        error_count += 1
                        print(f"❌ 记录 {i+1} 插入失败 (非时间格式错误): {single_error}")

                        if hasattr(instance, 'test_name'):
                            print(f"   失败记录信息: test_name={getattr(instance, 'test_name', 'N/A')}")
        else:
            # 非时间格式错误：记录错误
            error_count = len(test_result_instances)
            print(f"❌ 批量插入失败 (非时间格式错误): {bulk_error}")
            if row_info is not None:
                print(f"   相关行信息: {row_info}")

    # 记录最终统计信息
    print(f"📊 数据库插入统计:")
    print(f"   ✅ 成功插入: {success_count} 条")
    print(f"   ❌ 插入失败: {error_count} 条")
    print(f"   🔧 时间字段修复: {time_fixed_count} 条")

    return success_count, error_count, time_fixed_count


def close_db_connection():
    # 关闭数据库连接
    db_erp.dispose()
    print("Database connection closed")


def getBase64(data):
    if isinstance(data, str):
        # 如果传入的是文件路径
        with open(data, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode()
    elif isinstance(data, bytes):
        # 如果传入的是字节流
        encoded_string = base64.b64encode(data).decode()
    else:
        raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")
    return encoded_string


def getOcrResult(url, payload, headers):
    """通过 API 获取 OCR 结果"""
    response = requests.post(url, headers=headers, data=payload, verify=False)
    return response.text


def getKeyWords(ocrResult):
    """提取 OCR 结果中的文本内容"""
    text = ""
    try:
        ocr_dict = ast.literal_eval(ocrResult)
        text_list = []
        for result in ocr_dict["result"]["words_block_list"]:
            text_list.append(result["words"])
            text += result["words"] + "\n"
        print(text_list)
    except Exception as e:
        print(f"❌ OCR结果解析失败: {e}")
    return text.strip()


def ocr_main(imgpath):
    """执行图片 OCR 识别并返回识别到的文本内容"""
    try:
        # 读取图片数据
        if isinstance(imgpath, str):
            with open(imgpath, "rb") as image_file:
                image_bytes = image_file.read()
        elif isinstance(imgpath, bytes):
            image_bytes = imgpath
        else:
            raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")
        
        # 使用统一的OCR服务，启用角度矫正（对应原来的detect_direction=True）
        from common.clients.ocr_service import process_ocr
        result = process_ocr(image_bytes, use_correction=True)
        
        # 直接使用统一OCR服务返回的markdown_text作为完整文本
        text = result.get("markdown_text", "")
        
        print(f"📝 提取的文本长度: {len(text)}")
        return text.strip()
        
    except Exception as e:
        print(f"❌ 图片 {imgpath} OCR识别失败: {e}")
        return ""


# 调用新的test_result_format处理 OCR 结果转换为 JSON
def request_llm_api1(ocr_text, task_info=None, ocr_blocks=None):
    """使用新的test_result_format处理OCR结果，转换为标准的JSON格式"""
    print("🚀 开始调用【test_result_format_ae_ocr PocketFlow】转换 OCR 结果为 JSON...")

    try:
        # 🎯 调用新的处理流程，传递任务信息字典和OCR坐标信息
        test_items = process_medical_ocr(ocr_text, task_info, ocr_blocks)

        # 转换为原有的JSON格式
        test_results = []
        for item in test_items:
            item_dict = item.to_dict()
            # 过滤掉 TestResult 模型不支持的字段
            filtered_dict = {k: v for k, v in item_dict.items() if k != 'test_text'}
            test_results.append(filtered_dict)

        ocr_json = {"test_results": test_results}

        # 构造返回值以保持与原有接口兼容
        # inputText = ocr_text
        # outputText = {"choices": [{"message": {"content": json.dumps(ocr_json, ensure_ascii=False)}}]}
        # think = "无think"
        # generated_tokens = {"prompt_tokens": len(ocr_text), "completion_tokens": len(str(ocr_json))}

        # print(f"📄 test_result_format_ocr 处理后的 JSON OCR 结果:\n{json.dumps(ocr_json, ensure_ascii=False, indent=2)}")

        # return ocr_json, inputText, outputText, think, generated_tokens
        return ocr_json

    except Exception as e:
        print(f"❌ test_result_format处理失败: {e}")
        # 回退到原有的处理方式
        # return request_llm_api1_fallback(ocr_text)


# def request_llm_api1_fallback(ocr_text):
#     """原有的LLM处理方式作为回退"""
#     url = 'https://192.168.230.105:30334/v1/infer/46c6c41b-ae45-4362-92f9-152e6cc67975/v1/chat/completions'
#     headers = {
#         "Content-Type": "application/json",
#         "Auth-Username": "mauser",
#         "Auth-Password": "Prs@123456"
#     }
#     example_json = """
#     {
#         "test_results": [
#         {
#         "test_code": "检查代码",
#         "test_name": "检查名称",
#         "test_value": "检查结果值",
#         "test_flag": "检查结果值标志",//0：正常，1：偏高，2：偏低；不可为空，必须为 0，1，2 三个当中的一个
#         "test_type": "检查结果值类型",//必须为 数值，定性 两个当中的一个
#         "test_unit": "检查单位",
#         "reference_value": "参考范围",
#         "reference_range_min": "参考范围最小值",
#         "reference_range_max": "参考范围最大值",
#         "collect_time": "采集时间",//时间格式需要转换为 yyyy-mm-dd hh:mm:ss
#         "report_time": "报告时间",//时间格式需要转换为 yyyy-mm-dd hh:mm:ss
#         },
#         // 若有多个测试结果，按照上述格式继续添加对象，每个对象对应一个测试结果；每个对象里面的字段是可以为空的。
#         ]
#     }
#     """
#     body = {
#         "model": "DeepSeek-R1-Distill-Qwen-32B",
#         "messages": [{"role": "user",
#                       "content": f"请参考以下输出例子结构，将 OCR 识别结果转为标准的 JSON 格式。例子结构如下：{example_json}。OCR 识别结果为：{ocr_text}。不要返回除了JSON格式的字符串之外任何其他额外其他的内容。"}],
#         "max_tokens": 8192,
#         "temperature": 0.0,
#         "top_p": 0.95,
#         "stream": False
#     }

#     print("🚀 回退到原有的【Deepseek-distil-Qwen32B】大模型转换 OCR 结果为 JSON...")
#     response = requests.post(url, json=body, headers=headers, verify=False)
#     result = response.json()
#     inputText = ocr_text
#     outputText = result
#     think = result["choices"][0]["message"]["content"]
#     start_index = think.find("<think>") + len("<think>")
#     end_index = think.find("</think>")
#     think = think[start_index:end_index]
#     generated_tokens = result.get('usage', {})

#     content = result.get("choices", [{}])[0].get("message", {}).get("content", "{}")
#     cleaned_content = clean_llm_response(content)

#     print(f"📄 处理后的 JSON OCR 结果:\n{cleaned_content}")
#     pattern = r'```json(.*?)```'
#     match = re.search(pattern, cleaned_content, re.DOTALL)
#     cleaned_content = {"test_results": []}
#     if match:
#         cleaned_content = match.group(1)
#     cleaned_content = json.loads(cleaned_content)
#     return cleaned_content, inputText, outputText, think, generated_tokens


# 清理大模型返回的内容，去除 <think> 标签内的内容
def clean_llm_response(content):
    """清理大模型返回的内容"""
    # 使用正则去除 <think> 标签及其内容
    return re.sub(r"<think>.*?</think>", "", content, flags=re.DOTALL).strip()


def process_images_in_directory(images_directory):
    """遍历指定文件夹中的所有图片文件并进行 OCR 识别"""
    ocr_texts = []
    print("🔍 开始进行 OCR 识别...")

    for filename in sorted(os.listdir(images_directory)):
        image_path = os.path.join(images_directory, filename)

        if image_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
            ocr_result = ocr_main(image_path)
            char_count = len(ocr_result)
            print(f"📄 图片 {filename} 识别完成，字符数: {char_count}")

            if ocr_result:
                ocr_texts.append(f"## {filename}\n{ocr_result}\n")

    print("✅ OCR 识别全部完成！")
    return "\n".join(ocr_texts)


def main(images_directory):
    """主函数：获取 OCR 识别并将结果转换为 JSON"""
    ocr_text = process_images_in_directory(images_directory)
    if ocr_text:
        ocr_json = request_llm_api1(ocr_text)
        print("OCR 转换为 JSON 后的结果：")
        print(ocr_json)
    else:
        print("⚠️ 没有获取到 OCR 识别结果")


def hw_ocr_general_text(
    image_path=None,
    image_url=None,
    detect_direction=True,
    quick_mode=False,
    character_mode=False,
    language="zh",
    single_orientation_mode=True,
    pdf_page_number=1
):
    """
    调用华为 OCR API 进行文字识别。
    :param project_id: 项目 ID
    :param token: 认证 Token
    :param image_path: 本地图片路径（与 image_url 二选一）
    :param image_url: 远程图片 URL（与 image_path 二选一）
    :param detect_direction: 是否校正图片倾斜角度
    :param quick_mode: 是否开启快速模式
    :param character_mode: 是否开启单字符模式
    :param language: 语言选择（默认为中英文）
    :param single_orientation_mode: 是否开启单朝向模式
    :param pdf_page_number: 指定 PDF 识别的页码
    :param endpoint: OCR API 终端节点
    :return: 识别结果
    """
    try:
        if image_path:
            # 处理图片路径或字节数据
            if isinstance(image_path, str):
                with open(image_path, "rb") as file:
                    image_bytes = file.read()
            else:
                image_bytes = image_path
        elif image_url:
            # 处理远程图片URL
            response = requests.get(image_url)
            response.raise_for_status()
            image_bytes = response.content
        else:
            raise ValueError("image_path 或 image_url 其中之一必须提供")

        # 使用统一OCR服务，映射detect_direction参数到use_correction
        use_correction = detect_direction  # 映射参数
        from common.clients.ocr_service import process_ocr
        result = process_ocr(image_bytes, use_correction=use_correction)
        
        # 返回原始OCR结果格式，保持向后兼容
        return result["ocr_result"]
        
    except Exception as e:
        print(f"❌ OCR服务调用失败: {e}")
        raise


def format_ocr_text(result, image_path):
    """处理 OCR 识别结果，返回格式化的文本"""
    def rotate_point(point, Cx, Cy, theta):
        """计算旋转后的坐标"""
        x, y = point

        # 平移到中心
        x_prime = x - Cx
        y_prime = y - Cy

        # 应用旋转公式
        x_double_prime = x_prime * math.cos(theta) + y_prime * math.sin(theta)
        y_double_prime = -x_prime * math.sin(theta) + y_prime * math.cos(theta)

        # 平移回原坐标系
        x_rot = x_double_prime + Cx
        y_rot = y_double_prime + Cy

        return [x_rot, y_rot]

    # 提取带位置信息的文本块
    text_blocks = []
    if "result" in result and "words_block_list" in result["result"]:
        ocr_result = result["result"]
        direction = ocr_result["direction"]

        if direction:
            print(f"direction: {direction}")
            # image = Image.open(image_path)
            image_file = io.BytesIO(image_path)
            # 用 Image.open 打开文件对象
            image = Image.open(image_file)
            # 获取图片宽高
            W, H = image.size
            Cx, Cy = W / 2, H / 2  # 中心点

            # 顺时针旋转角度（弧度制）
            theta = math.radians(direction)

            for box in ocr_result["words_block_list"]:
                location = []
                for point in box['location']:
                    # 旋转单个点
                    rotated_point = rotate_point(point, Cx, Cy, theta)
                    location.append(rotated_point)
                box['location'] = location

        # 处理文本块
        for block in result["result"]["words_block_list"]:
            if "words" in block and "location" in block:
                text = block["words"]
                location = block["location"]

                # 计算文本块中心点坐标
                center_x = sum(point[0] for point in location) / 4
                center_y = sum(point[1] for point in location) / 4

                # 计算高度 (使用左上角和左下角的y坐标差)
                # 这里我们使用中心点高度作为参考
                min_y = min([p[1] for p in location])
                max_y = max([p[1] for p in location])
                height = max_y - min_y

                text_blocks.append({
                    "text": text,
                    "center_y": center_y,
                    "center_x": center_x,
                    "height": height if height > 0 else 1  # 确保高度为正
                })

    if not text_blocks:
        return ""

    # 基于文本块高度分析确定行高容差
    heights = [block["height"] for block in text_blocks]

    if not heights:
        return ""

    # avg_height = sum(heights) / len(heights)

    # 使用中位数可能比平均值更稳健
    heights.sort()
    median_height = heights[len(heights) // 2]

    # 根据中位数计算行高容差
    line_height_tolerance = median_height * 0.7  # 可以根据实际情况调整系数

    # 使用确定的line_height_tolerance分组文本块
    rows = {}

    for block in text_blocks:
        # 查找最近的行
        found_row = False
        for row_y in rows.keys():
            if abs(block["center_y"] - row_y) < line_height_tolerance:
                rows[row_y].append(block)
                found_row = True
                break

        # 如果没有找到匹配的行，创建新行
        if not found_row:
            rows[block["center_y"]] = [block]

    # 对每一行按x坐标排序，然后将行按y坐标排序
    formatted_text = ""
    for row_y in sorted(rows.keys()):
        row_blocks = sorted(rows[row_y], key=lambda b: b["center_x"])
        line_text = " ".join([block["text"] for block in row_blocks])
        formatted_text += line_text + "\n"

    return formatted_text.strip()


def process_row(task_id):
    print('开始处理下面的row了！！！')
    task = AeTrackerTask.objects.filter(id=task_id, category=AeTrackerTask.OCR_EXTRACTION, delete_flag=0).first()
    if not task:
        print(f"没有发现任务")
        return
    
    # 🎯 创建任务信息字典，避免参数层层传递
    task_info = {
        'task_id': task_id,
        'create_user': task.create_user,
        'create_name': task.create_name,
        'business_id': task.patient_id
    }
    subject_id = task.subject_id
    subject_item_id = task.subject_item_id
    queryset = SubjectItem.objects.select_related(
        'subject_visit').filter(delete_flag=0, subject_item_id=subject_item_id).first()
    if queryset:
        data = [{
            'subject_visit__visit_date': queryset.subject_visit.visit_date if queryset.subject_visit else None,
            'item_type': queryset.item_type,
            'item_id': queryset.item_id
        }]
    else:
        data = []
    print(data)

    sql = f"""
        SELECT
        subject_medical_info.id,
        ocr_text_mask as ocr_text,
        ocr_box,
        subject_medical_file.original_filename
            FROM 
        subject_medical_info
        JOIN 
        subject_medical_file ON subject_medical_info.file_id = subject_medical_file.id
        WHERE 
        subject_medical_info.delete_flag = 0 
        AND subject_medical_info.ocr_time IS NOT NULL 
        AND subject_medical_info.subject_id = '{subject_id}'
        AND subject_medical_info.subject_item_id = '{subject_item_id}' AND subject_medical_info.ocr_text_mask IS NOT NULL
    """
    df = sql_to_df('default', sql)


    task.status = 'IN_PROGRESS'
    task.save()

    print(task_id)
    try:
        for index, row in df.iterrows():
            try:
                ocr_json = {"test_results": []}
                original_filename = row['original_filename']
                # 从 MinIO 中获取对象
                # 读取对象内容到内存中
                both_conditions_false = True

                SUPPORTED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.webp', '.pdf', '.docx', '.doc'}
                if any(original_filename.lower().endswith(ext) for ext in SUPPORTED_EXTENSIONS):
                    print('图片|pdf|word！！！')
                    ocr_text = row['ocr_text']
                    ocr_box = row['ocr_box'] if pd.notna(row.get('ocr_box')) else None
                    # result = hw_ocr_general_text(image_path=file_bytesX)
                    # ocr_text = format_ocr_text(result, image_path=file_bytesX)
                    if ocr_text:
                        start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        # 🎯 使用方案5：传递任务信息字典，LLM调用会自动记录日志
                        ocr_json = request_llm_api1(ocr_text, task_info, ocr_box)
                        # ocr_json, inputText, outputText, think, generated_tokens = request_llm_api1(ocr_text, task_info)
                        # result = {}
                        # result['task_id'] = task_id
                        # result['create_user'] = task.create_user
                        # result['create_name'] = task.create_name
                        # result['category'] = 'TEST_REPORT_STRUCTURE'
                        # result['model_name'] = 'qwen3_32b'
                        # result['start_time'] = start_time
                        # result['end_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        # result['input_text'] = inputText
                        # result['think_text'] = think
                        # result['output_text'] = outputText
                        # result['prompt_tokens'] = generated_tokens['prompt_tokens']
                        # result['completion_tokens'] = generated_tokens['completion_tokens']
                        # result['business_id'] = task.patient_id
                        # ModelInvocationLog.objects.create(**result)
                        print("OCR 转换为 JSON 后的结果：")
                        print(ocr_json)
                    else:
                        print("⚠️ 没有获取到 OCR 识别结果")
                        continue

                    both_conditions_false = False
                if both_conditions_false:
                    print(row)
                    print('文件类型不属于处理范畴！！！')
                    continue

                ae_tracker_task_item = AeTrackerTask.objects.filter(delete_flag=0).get(
                    subject_item_id=subject_item_id,
                    subject_id=subject_id, id=task_id)
                llm_results = ocr_json
                max_seq_value = TestResult.objects.filter(
                    subject_id=subject_id,
                    subject_item_id=subject_item_id,
                    delete_flag=0
                ).values_list('seq', flat=True).order_by('-seq').first() or 0
                test_result_instances = []
                page_collect_times = {}
                # 首先为每个page_num确定collect_time
                for result in llm_results["test_results"]:
                    page_num = result.get('page_num', 0)  # 默认为0页
                    if page_num not in page_collect_times:
                        # 为该页码计算collect_time
                        page_collect_times[page_num] = collect_time_choice(result['collect_time'], data)


                for i, result in enumerate(llm_results["test_results"]):
                    # result['report_time'] = '2024-09-14 09:38'

                    print("*" * 50)
                    print(result)
                    print("*" * 50)

                    # test_text = result['test_text']
                    # # if test_text and any(char in test_text for char in ['↑', '↓', '阴性', '阳性', '+', '-']):
                    # if test_text is None:
                    #     result['abnormal_symbol'] = None
                    # elif '↑' in test_text:
                    #     result['abnormal_symbol'] = '↑'
                    # elif '↓' in test_text:
                    #     result['abnormal_symbol'] = '↓'
                    # elif '+' in test_text:
                    #     result['abnormal_symbol'] = '+'
                    # elif '阳性' in test_text:
                    #     result['abnormal_symbol'] = '阳性'
                    # elif '-' in test_text:
                    #     result['abnormal_symbol'] = '-'
                    # elif '阴性' in test_text:
                    #     result['abnormal_symbol'] = '阴性'
                    # else:
                    #     result['abnormal_symbol'] = None  # 不包含任何字符，设为None

                    result['seq'] = max_seq_value + i + 1
                    page_num = result.get('page_num', 0)
                    try:
                        result['collect_time'] = page_collect_times[page_num]
                    except:
                        pass
                    if result['report_time'] == '':
                        result['report_time'] = None
                    if result['test_flag'] == '':
                        result['test_flag'] = None
                    result['abnormal_flag'] = 0
                    result['subject_medical_info_id'] = row['id']
                    result['project'] = ae_tracker_task_item.project
                    result['project_site'] = ae_tracker_task_item.project_site
                    result['subject_id'] = subject_id
                    result['patient'] = ae_tracker_task_item.patient
                    result['subject_item_id'] = subject_item_id
                    result['subject_epoch'] = ae_tracker_task_item.subject_epoch
                    result['subject_visit'] = ae_tracker_task_item.subject_visit
                    test_result_instances.append(TestResult(**result))
                # 智能异常处理机制：优先尝试批量插入，失败时降级到单条插入
                success_count, error_count, time_fixed_count = smart_database_insert(test_result_instances, row)
                print(f"数据库插入结果: 成功 {success_count} 条，错误 {error_count} 条，时间字段修正 {time_fixed_count} 条")

            except Exception as e:
                print(e)
                print('文件处理错误！！！')
                print(row)

        with transaction.atomic():
            # for instance in created_instances:
            #     instance.seq = float(instance.id)
            # TestResult.objects.bulk_update(created_instances, ['seq'])

            params = {
                'subject_id': subject_id,
                'subject_item_id': subject_item_id,
                'ae_ai_current_step': 2
            }
            sql = text(
                f"""update subject_item_info set ae_ai_current_step=:ae_ai_current_step where subject_id=:subject_id and subject_item_id=:subject_item_id and ae_ai_current_step <= 2""")
            # print(sql)q
            with db_erp.begin() as conn:
                conn.execute(sql, params)
            conn.close()

            params = {
                'which_need_update_id': task_id,
                'mask_status': 'COMPLETED',
                'end_time': datetime.now()
            }
            sql = text(
                f"""update ae_tracker_task set status=:mask_status,end_time=:end_time where id=:which_need_update_id""")
            # print(sql)q
            with db_erp.begin() as conn:
                conn.execute(sql, params)
            conn.close()
            print(task_id)
            return True
    except:
        with db_erp.begin() as conn:
            # 执行第一个更新操作
            params = {
                'which_need_update_id': task_id,
                'mask_status': 'ERROR',
                'end_time': datetime.now()
            }
            sql = text("""
                UPDATE ae_tracker_task 
                SET status=:mask_status, end_time=:end_time 
                WHERE id=:which_need_update_id
            """)
            conn.execute(sql, params)

            # 执行第二个更新操作
            params = {
                'subject_id': subject_id,
                'subject_item_id': subject_item_id,
                'ae_ai_current_step': 0
            }
            sql = text("""
                UPDATE subject_item_info 
                SET ae_ai_current_step=:ae_ai_current_step 
                WHERE subject_id=:subject_id 
                AND subject_item_id=:subject_item_id 
                AND ae_ai_current_step <= 2
            """)
            conn.execute(sql, params)
        conn.close()
        print(task_id)
        return False


# def main():
#     sql = f"""
#                 select
#                 id,subject_id,subject_item_id
#                 from ae_tracker_task
#                 where delete_flag = 0 and (status = 'TODO' or status = 'IN_PROGRESS') and category = 'OCR_EXTRACTION'
#             """
#     df_task = pd.read_sql_query(sql, db_erp)
#
#     with ThreadPoolExecutor(max_workers=4) as executor:
#         futures = [executor.submit(process_row, row) for _, row in df_task.iterrows()]
#         for future in futures:
#             future.result()


def main():
    parser = argparse.ArgumentParser(description="AE-OCR任务")
    parser.add_argument('--task_id', type=int, required=True, help="task_id")
    args = parser.parse_args()
    process_row(args.task_id)


if __name__ == "__main__":
    main()
    close_db_connection()

    # image_path = r'C:\Users\<USER>\Desktop\检验单2.webp'
    # with open(image_path, 'rb') as file:
    #     # 读取文件内容到字节流
    #     file_bytesX = file.read()


    # result = hw_ocr_general_text(image_path=file_bytesX)
    # print(result)
    # ocr_text = format_ocr_text(result, image_path=file_bytesX)
    # print(ocr_text)

    # ocr_text = ocr_main(file_bytesX)
    # print(ocr_text)
    #
    # if ocr_text:
    #     ocr_json = request_llm_api1(ocr_text)
    #     print(ocr_json)

