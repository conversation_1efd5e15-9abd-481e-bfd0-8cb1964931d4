import os
import logging
from datetime import timedel<PERSON>, datetime

from minio import <PERSON>o
from django.db import transaction
from django.db.models import Q
from django.http import FileResponse
from django.conf import settings
from django.shortcuts import render
from django.db.models import Subquery, OuterRef
from rest_framework.viewsets import GenericViewSet
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.fields import <PERSON>r<PERSON><PERSON>
from rest_framework import filters, viewsets
from rest_framework.mixins import ListModelMixin, CreateModelMixin, UpdateModelMixin, RetrieveModelMixin
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.filters import OrderingFilter
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.renderers import BaseRenderer
from rest_framework.exceptions import ValidationError, NotFound, APIException
from rest_framework.parsers import <PERSON>PartParser, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import APIException

from django_filters.rest_framework import DjangoFilterBackend

from rest_pandas.views import PandasViewBase
from rest_pandas import PandasExcelRenderer
from openpyxl.styles import Font, Border
from openpyxl.styles import Alignment

from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema, OpenApiParameter

from common.auth import ERPSysJWTAuthentication
from common.utils import calculate_file_hash
from common.minio_client import get_minio_client
from common.pagination import StandardResultsSetPagination

from apps.medical_collection.models import MedicalCollectionTask
from . import serializers
from . import models
from . import filters
from apps.subject.models import SubjectItem
from apps.subject_medical.models import SubjectMedicalInfo
from apps.subject_medical.serializers import SubjectMedicalInfoSerializer
from apps.medical_collection.models import MedicalCollectionTask
from apps.medical_collection.serializers import MedicalCollectionTaskSerializer
from apps.subject.models import SubjectItem, Subject
from apps.system.models import OperationLog
import re
from common.tools import trigger_dag, collect_time_choice

from common.def_tools import post_tracker, make_ae_tracker_flag
import io
import requests
from django.http import HttpResponse
from django.db import connections
import zipfile


logger = logging.getLogger('app')


class BaseAPIView(APIView):
    authentication_classes = [ERPSysJWTAuthentication]
    permission_classes = [IsAuthenticated]


class BaseListViewSet(BaseAPIView, GenericViewSet, ListModelMixin):
    filter_backends = (DjangoFilterBackend, OrderingFilter)


class MedicalInfoNotExistsError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "请先上传文件！！！"
    default_code = "error"


class MedicalInfoPicNotExistsError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "请先上传文件！！！"
    default_code = "error"


class TaskExistsStatusTODO(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "先前打码，待办中！！！"
    default_code = "error"


class TaskExistsStatusIN_PROGRESS(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "先前任务流程还未完全结束,正在处理中！！！"
    default_code = "error"


class No_Ocr_Text(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "OCR基础文本数据暂未输出，请稍后再试！！！"
    default_code = "error"


class TaskExistsStatusCOMPLETED(APIException):
    status_code = status.HTTP_200_OK
    default_detail = "已生成任务,任务已完成！！！"
    default_code = "success"


class TaskExistsStatusCANCELLED(APIException):
    status_code = status.HTTP_200_OK
    default_detail = "已生成任务,任务取消！！！"
    default_code = "success"


class NonNumericInputException(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "输入值无法判断！！！"
    default_code = "success"


class NoChosenItemType(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "请先选择操作项类型！！！"
    default_code = "error"


class NoAeMeds(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "请先配置用药！！！"
    default_code = "error"


class OcrTaskExistsStatusIN_PROGRESS(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "当前OCR任务正在进行中，请等候！！！"
    default_code = "error"


class PicMaskRequests(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "此接口已停用！！！"
    default_code = "error"


class NoFileError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "请先上传操作项文件！！！"
    default_code = "error"


class EditHighLowRequests(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "数据不符合箭头判断标准！！！"
    default_code = "error"


class OcrResultListViewSet(BaseListViewSet):
    queryset = models.TestResult.objects.filter(delete_flag=0).select_related('subject_visit', 'subject',
                                                                              'subject_item')
    serializer_class = serializers.OcrResultListSerializer
    filterset_class = filters.OcrResultListFilter

    # ordering_fields = '-id'
    ordering = ('seq',)

    @extend_schema(
        summary='获取化验单OCR结果',
        tags=['访视详情'],
        # request=serializers.OcrResultListRequestSerializer,
        # parameters=[
        #     OpenApiParameter(name='subject_id', type=str, location=OpenApiParameter.QUERY, description='受试者 ID', required=True),
        #     # OpenApiParameter(name='subject_epoch_id', type=str, location=OpenApiParameter.QUERY,
        #     #                  description='受试者阶段 ID', required=True),
        #     # OpenApiParameter(name='subject_visit_id', type=str, location=OpenApiParameter.QUERY,
        #     #                  description='受试者访视 ID', required=True),
        #     OpenApiParameter(name='subject_item_id', type=str, location=OpenApiParameter.QUERY, description='受试者项目 ID', required=True),
        # ],
        responses=serializers.OcrResultListSerializer(many=True)
    )
    # if not ProjectMaterialFile.objects.filter(
    #         material_info__project_id=project_id, material_info__category=t_c, delete_flag=0
    # ).exists():
    #     raise CRFNotExistsError()

    def list(self, request, format=None):
        query_params = request.query_params

        # 取出特定参数
        subject_id = query_params.get('subject_id')
        # subject_epoch_id = query_params.get('subject_epoch_id')
        # subject_visit_id = query_params.get('subject_visit_id')
        subject_item_id = query_params.get('subject_item_id')
        # 打印取出的参数，用于调试
        # print(f"subject_id: {subject_id}, subject_epoch_id: {subject_epoch_id}, subject_visit_id: {subject_visit_id}, subject_item_id: {subject_item_id}")
        task = models.AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[models.AeTrackerTask.IN_PROGRESS],
            delete_flag=0,
            # 直接指定 category 的可选值
            category__in=[
                models.AeTrackerTask.OCR_EXTRACTION,
                models.AeTrackerTask.OCR_EXTRACTION_TEXT,
            ]
        ).first()
        if task:
            return Response([])

        res = super().list(request, format)

        return res


class OcrResultAddDeleteViewSet(BaseListViewSet, UpdateModelMixin):
    queryset = models.TestResult.objects.filter(delete_flag=0)
    serializer_class = serializers.OcrResultListSerializer
    filterset_class = filters.OcrResultListFilter

    http_method_names = ['patch', 'delete', 'post']

    @extend_schema(
        summary='新增化验单OCR结果',
        tags=['访视详情'],
        request=serializers.OcrResultAddSerializer,
        responses=serializers.OcrResultListSerializer
    )
    def create(self, request, format=None):
        subject_id = request.data.get('subject_id')
        subject_item_id = request.data.get('subject_item_id')
        data = request.data
        if data.get('seq') is not None:
            temp = request.data.get('seq')
            data['seq'] = float(data.get('seq')) + 0.01
            test_result = models.TestResult.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id,
                                                           delete_flag=0, seq=temp).first()
            if test_result is None:
                queryset = SubjectItem.objects.select_related(
                    'subject_visit').filter(delete_flag=0, subject_item_id=subject_item_id).first()
                if queryset:
                    data_ot_visit = [{
                        'subject_visit__visit_date': queryset.subject_visit.visit_date if queryset.subject_visit else None,
                        'item_type': queryset.item_type,
                        'item_id': queryset.item_id
                    }]
                else:
                    data_ot_visit = []
                print(data_ot_visit)
                data['collect_time'] = collect_time_choice(None, data_ot_visit)
                data['report_time'] = None
            else:
                data['collect_time'] = test_result.collect_time
                data['report_time'] = test_result.report_time
        serializer = serializers.OcrResultAddSerializer(data=data)
        if not serializer.is_valid():
            raise ValidationError(serializer.errors)
        subject_item = SubjectItem.objects.get(
            subject_item_id=subject_item_id,
            subject_id=subject_id, delete_flag=0)
        try:
            subject = Subject.objects.get(subject_id=subject_id, delete_flag=0)
        except Subject.DoesNotExist:
            raise NotFound({'subject_id': ['受试者不存在']})
        subject_visit = subject_item.subject_visit
        # project = subject.project
        # project_site = subject.project_site

        result = data
        if subject_item.item_type != '1':
            result['test_flag'] = '1'
        else:
            try:
                if str(data.get('test_value')) == str(data.get('reference_value')):
                    data['test_flag'] = '0'
                # else:
                pattern = r'^(?:[1-9]\d*|0)(?:\.\d+)?$'
                if re.match(pattern, str(data.get('test_value'))):
                    # raise NonNumericInputException
                    reference_value = data.get('reference_value')
                    if reference_value and '-' in reference_value:
                        reference_range_min = reference_value.split("-")[0]
                        reference_range_max = reference_value.split("-")[1]
                        if float(data.get('test_value')) >= float(reference_range_min) and float(
                                data.get('test_value')) <= float(reference_range_max):
                            data['test_flag'] = '0'
                        if float(data.get('test_value')) > float(reference_range_max):
                            data['test_flag'] = '1'
                        if float(data.get('test_value')) < float(reference_range_min):
                            data['test_flag'] = '2'
            except:
                pass
        result['abnormal_flag'] = 0
        result['subject_medical_info_id'] = 1
        result['project'] = subject_item.project
        result['project_site'] = subject_item.project_site
        # result['subject_id'] = subject_id
        result['patient'] = subject.patient
        # result['subject_item_id'] = subject_item_id
        result['subject_epoch'] = subject_visit.subject_epoch
        result['subject_visit'] = subject_visit
        TestResult = models.TestResult.objects.create(**result)
        serializer = serializers.OcrResultListSerializer(TestResult)
        return Response(serializer.data)

    @extend_schema(
        summary='删除化验单OCR结果',
        tags=['访视详情'],
        # request=serializers.OcrResultAddSerializer,
        # responses=serializers.OcrResultListSerializer
    )
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete_flag = 1
        # subject_item_id = instance.subject_item_id
        # subject_id = instance.subject_id
        # task = models.AeTrackerTask.objects.filter(
        #     subject_id=subject_id,
        #     subject_item_id=subject_item_id,
        #     status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
        #     delete_flag=0
        # ).order_by('-create_time').first()
        # if task:
        #     raise TaskExistsStatusIN_PROGRESS()

        instance.update_user = request.sys_user.username
        instance.update_name = request.sys_user.realname
        instance.save()

        return Response(status=status.HTTP_204_NO_CONTENT)

    @extend_schema(
        summary='单条更新化验单',
        tags=['访视详情'],
        request=serializers.UpdateAeLevelSerializer,
        responses=serializers.OcrResultSerializer
    )
    def partial_update(self, request, *args, **kwargs):  # 病史  单条信息编辑  ； 病史更新异常项提取名字
        # data = request.data.copy()
        # data['update_user'] = request.sys_user.username
        # data['update_name'] = request.sys_user.realname
        # test_result = self.get_object()
        # partial = kwargs.pop('partial', True)
        # serializer = serializers.OcrResultSerializer(test_result, data=data, partial=partial)
        # if not serializer.is_valid():
        #     return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        # self.perform_update(serializer)
        # serializer = serializers.OcrResultSerializer(test_result)
        # return Response(serializer.data)
        try:
            data = request.data.copy()
            data['update_user'] = request.sys_user.username
            data['update_name'] = request.sys_user.realname
            data['ae_edit_flag'] = 1

            test_result = self.get_object()
            print(test_result.id)
            a = models.TestResult.objects.filter(
                id=test_result.id,
            ).first()
            if data.get('ae_grade') is not None and int(data.get('ae_grade')) > 0:  # or data.get('ae_name')
                data['abnormal_flag'] = 1
            if data.get('ae_grade') is not None and int(data.get('ae_grade')) == 0:  # or data.get('ae_name')
                data['abnormal_flag'] = 0
            if a.ae_ai_result_list[0]['ae_grade'] is not None and a.ae_ai_result_list[0]['ae_grade'] != '' and int(
                    a.ae_ai_result_list[0]['ae_grade']) == int(
                    data.get('ae_grade')):
                data['ae_edit_flag'] = 0
                # operationlog = OperationLog.objects.filter(
                #     target_id=test_result.id,
                #     target_type=OperationLog.EDIT_AE_LOG,
                #     delete_flag=0
                # ).first()
                # if operationlog:
                #     data['ae_edit_flag'] = 1


            else:
                if data.get('ae_grade') is None and a.ae_ai_result_list[0]['ae_grade'] is None:
                    data['ae_edit_flag'] = 0
                if data.get('ae_grade') is not None:
                    ae_grade = int(data.get('ae_grade'))
                    ae_grade_list = {
                        None: "未判定",
                        0: "正常",
                        1: "1级",
                        2: "2级",
                        3: "3级",
                        4: "4级",
                        5: "5级",
                        6: "NCS"
                    }
                    # 根据 ae_grade 的值从字典中获取对应的结果
                    ae_grade_result = ae_grade_list.get(ae_grade, "未知")
                    with transaction.atomic():
                        opt_log = {
                            'target_type': OperationLog.EDIT_AE_LOG,
                            'operate_content': '{}修改{}等级为{}'.format(
                                request.sys_user.realname + ' ' + request.sys_user.username,
                                data.get('test_name'), ae_grade_result),
                            # 'version': 'v' + str(formatted_time),
                            'target_id': test_result.id,
                            'create_user': request.sys_user.username,
                            'create_name': request.sys_user.realname
                        }
                        OperationLog.objects.create(**opt_log)

            try:
                if data['medical_history_flag'] is None:

                    latest_result = models.TestResult.objects.filter(
                        subject_id=a.subject_id,
                        test_name=data['test_name'],
                        medical_history_flag__isnull=False
                    ).order_by('-create_time').first()
                    data['medical_history_flag'] = latest_result.medical_history_flag
                    if int(data['medical_history_flag']) == 1:
                        data['ae_name'] = '病史'

                if data['medical_history_flag'] != '' and int(data['medical_history_flag']) == 1:
                    data['ae_name'] = '病史'
            except:
                pass

            partial = kwargs.pop('partial', True)
            serializer = serializers.OcrResultSerializer(test_result, data=data, partial=partial)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            self.perform_update(serializer)
            serializer = serializers.OcrResultSerializer(test_result)

            subject_item = SubjectItem.objects.filter(subject_item_id=a.subject_item_id,
                                                      subject_id=a.subject_id).first()

            test_results = models.TestResult.objects.filter(subject_id=a.subject_id,
                                                            subject_item__label=subject_item.label,
                                                            subject_item__ae_ai_current_step=6, delete_flag=0)
            # test_results = models.TestResult.objects.filter(subject_id=a.subject_id, delete_flag=0)
            # 用于存储分组信息
            make_ae_tracker_flage = make_ae_tracker_flag(test_results, subject_item)
            push_tracker = post_tracker(a.project_id, a.project_site_id, a.subject_id, a.subject_item_id, request.sys_user.username, request.sys_user.realname)
            return Response(serializer.data)
        except:
            print('病史单条编辑except')
            data = request.data.copy()
            data['update_user'] = request.sys_user.username
            data['update_name'] = request.sys_user.realname
            test_result = self.get_object()
            partial = kwargs.pop('partial', True)
            serializer = serializers.OcrResultSerializer(test_result, data=data, partial=partial)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            self.perform_update(serializer)
            serializer = serializers.OcrResultSerializer(test_result)
            return Response(serializer.data)


class MedsUpdateViewSet(BaseListViewSet, UpdateModelMixin):
    queryset = models.TestResult.objects.filter(delete_flag=0)
    serializer_class = serializers.OcrResultListSerializer
    filterset_class = filters.OcrResultListFilter

    http_method_names = ['patch', 'delete', 'post']

    @extend_schema(
        summary='单条用药更新检验+病史',
        tags=['访视详情'],
        request=serializers.AiMedicationMeasuresListSerializers,
        responses=serializers.OcrResultSerializer
    )
    def partial_update(self, request, *args, **kwargs):  # 单条用药更新检验+病史
        try:
            data_post = request.data.copy()
            data = {}
            data['ae_medication_measures_list'] = data_post
            data['update_user'] = request.sys_user.username
            data['update_name'] = request.sys_user.realname
            # data['ae_edit_flag'] = 1
            drug_type = data_post.get('drug_type')
            if int(drug_type) == 1 or int(drug_type) == 2:
                if data_post['drugs'][0]['reason'] is not None and data_post['drugs'][0]['reason'] != '':
                    data['ae_meds'] = data_post['drugs'][0]['reason']
                elif data_post['drugs'][0]['reason'] is not None and data_post['drugs'][0]['reason'] == '':
                    data['ae_meds'] = '方案未提及'
                    if int(drug_type) == 2:
                        data['ae_meds'] = "\n".join(
                            item["measure_name"] for item in data_post['drugs'][0]['measure_name_list'] if
                            data_post['drugs'][0]['id'] == item['id'])


            elif int(drug_type) == 3 or int(drug_type) == 5:
                if int(data_post['drugs'][0]['no_adjustment_needed']) == 1:
                    data['ae_meds'] = data_post['drugs'][0]['reason']
                else:
                    data['ae_meds'] = "\n".join(item["measure_name"] for item in data_post['drugs'][0]['measure_name_list'] if data_post['drugs'][0]['id'] == item['id'])
                    if int(drug_type) == 5:
                        if '级' in data['ae_meds']:
                            data['ae_meds'] = data['ae_meds']
                        elif '级' not in data['ae_meds']:
                            data['ae_meds'] = data['ae_meds']
            elif int(drug_type) == 4 or int(drug_type) == 6:
                list_ae_meds = []
                for item in data_post['drugs']:
                    if int(item['no_adjustment_needed']) == 1:
                        ae_meds = f"{item['drug_name']}：{item['reason']}"
                        list_ae_meds.append(ae_meds)
                    else:
                        ae_meds = "\n".join(f"{item['drug_name']}：{item1['measure_name']}"
                                            for item1 in item['measure_name_list'] if
                                            int(item1.get('id')) == int(item.get('id')))
                        list_ae_meds.append(ae_meds)
                data['ae_meds'] = "\n".join(item for item in list_ae_meds)
                if int(drug_type) == 6:
                    # 筛选出包含'级'和不包含'级'的项目
                    with_grade = [item for item in list_ae_meds if '级' in item]
                    without_grade = [item for item in list_ae_meds if '级' not in item]

                    # 重新组织输出格式
                    ae_meds_parts = []
                    if with_grade:

                        ae_meds_parts.extend(with_grade)
                    if without_grade:
                        if with_grade:
                            ae_meds_parts.append('')  # 空行分隔

                        ae_meds_parts.extend(without_grade)

                    data['ae_meds'] = '\n'.join(ae_meds_parts)
            test_result = self.get_object()
            print(test_result.id)
            a = models.TestResult.objects.filter(
                id=test_result.id,
            ).first()
            # 标准化 a.ae_meds
            temp_ae_meds_1 = None if a.ae_meds is None or str(a.ae_meds).strip() == '' else a.ae_meds
            # 标准化 data['ae_meds']，使用 get() 避免 KeyError
            temp_ae_meds_2 = None if data['ae_meds'] is None or str(
                data['ae_meds']).strip() == '' else data['ae_meds']
            if temp_ae_meds_1 != temp_ae_meds_2:
                data['meds_edit_flag'] = 1
                with transaction.atomic():
                    opt_log = {
                        'target_type': OperationLog.EDIT_AE_MEDICATION_MEASURES,
                        'operate_content': '{}修改{}为{}'.format(
                            request.sys_user.realname + ' ' + request.sys_user.username,
                            a.ae_meds, data['ae_meds']),
                        # 'version': 'v' + str(formatted_time),
                        'target_id': test_result.id,
                        'create_user': request.sys_user.username,
                        'create_name': request.sys_user.realname
                    }
                    OperationLog.objects.create(**opt_log)
            partial = kwargs.pop('partial', True)
            serializer = serializers.OcrResultSerializer(test_result, data=data, partial=partial)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            self.perform_update(serializer)
            serializer = serializers.OcrResultSerializer(test_result)
            return Response(serializer.data)
        except:
            print('用药单条编辑except')
            data = request.data.copy()
            data['update_user'] = request.sys_user.username
            data['update_name'] = request.sys_user.realname
            test_result = self.get_object()
            partial = kwargs.pop('partial', True)
            serializer = serializers.OcrResultSerializer(test_result, data=data, partial=partial)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            self.perform_update(serializer)
            serializer = serializers.OcrResultSerializer(test_result)
            return Response(serializer.data)


class OcrResultTextViewSet(BaseListViewSet):
    queryset = models.TestOcrResult.objects.filter(delete_flag=0)
    serializer_class = serializers.OcrResultTextSerializer
    filterset_class = filters.OcrResultTextFilter

    # ordering_fields = '-id'
    # ordering = ('id',)
    @extend_schema(
        summary='获取非检验项目OCR文本结果',
        tags=['访视详情'],
        responses=serializers.OcrResultTextSerializer  # (many=True)
    )
    def list(self, request, format=None):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            data = serializer.data
            if all(all(len(str(val)) == 0 for val in item.values()) for item in data):
                return Response({})
            return self.get_paginated_response(data)

        serializer = self.get_serializer(queryset.first(), many=False)
        data = serializer.data
        if all(len(str(val)) == 0 for val in data.values()):
            return Response({})
        return Response(data)


class OcrResultTextUpdateViewSet(BaseAPIView, GenericViewSet, UpdateModelMixin):
    queryset = models.TestOcrResult.objects.all()
    serializer_class = serializers.OcrResultTextSerializer
    http_method_names = ['post']

    @extend_schema(
        summary='更新OCR-TEXT结果',
        tags=['访视详情'],
        request=serializers.OcrResultTextSerializer,  # (many=True),
        responses=serializers.OcrResultTextSerializer,  # (many=True)
    )
    @action(url_path='ocr-update', detail=False, methods=['post'])
    def ocr_update(self, request, format=None):
        data = request.data
        # 检查请求数据是否为字典
        if not isinstance(data, dict):
            return Response({"detail": "请求数据必须是一个字典"}, status=status.HTTP_400_BAD_REQUEST)
        # 将单个字典数据转换为列表形式处理
        data_list = [data]
        if len(data_list) < 1:
            # 这里的 TaskExistsStatusIN_PROGRESS 异常需要确保在合适的地方定义
            raise TaskExistsStatusIN_PROGRESS()
        updated_instance = None
        fields_to_update = ['ocr_text', 'update_time']  # 这里替换为你实际要更新的字段名
        with transaction.atomic():
            for data in data_list:
                # 检查数据是否包含 id
                data['update_time'] = datetime.now()
                instance_id = data.get('id')
                if not instance_id:
                    return Response({"detail": "数据必须包含 'id' 字段"}, status=status.HTTP_400_BAD_REQUEST)
                try:
                    # 根据 id 获取要更新的实例
                    instance = models.TestOcrResult.objects.get(id=instance_id)
                    filtered_data = {key: value for key, value in data.items() if key in fields_to_update}
                    # 创建序列化器并验证数据
                    serializer = self.get_serializer(instance, data=filtered_data, partial=True)
                    if serializer.is_valid():
                        # 保存更新后的实例
                        updated_instance = serializer.save()
                    else:
                        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                except models.TestOcrResult.DoesNotExist:
                    return Response({"detail": f"未找到 id 为 {instance_id} 的记录"}, status=status.HTTP_404_NOT_FOUND)

        # 序列化更新后的实例列表
        updated_serializer = self.get_serializer(updated_instance)
        # subject_item_id = data_list[0].get('subject_item_id')
        # subject_id = data_list[0].get('subject_id')
        # words = 'OCR识别结果'
        # material_file = {
        #     'target_type': OperationLog.CONFIRM_OCR_LOG,
        #     'operate_content': '{}确认{}完成'.format(request.sys_user.realname + ' ' + request.sys_user.username, words),
        #     # 'version': 'v' + str(formatted_time),
        #     'target_id': subject_item_id,
        #     'create_user': request.sys_user.username,
        #     'create_name': request.sys_user.realname
        # }
        # task_info = OperationLog.objects.create(**material_file)
        return Response(updated_serializer.data, status=status.HTTP_200_OK)


class OcrResultBatchUpdateViewSet(BaseAPIView, GenericViewSet, UpdateModelMixin):
    queryset = models.TestResult.objects.all()
    serializer_class = serializers.OcrResultListSerializer
    http_method_names = ['patch', 'post']

    @extend_schema(
        summary='批量更新化验单OCR结果',
        tags=['访视详情'],
        request=serializers.OcrResultListSerializer(many=True),
        # parameters =[
        #     OpenApiParameter
        #         (
        #         name='step',
        #         type=str,
        #         location=OpenApiParameter.QUERY,
        #         description='用于指定判定logger脚印步骤',
        #     ),
        # ],
        responses=serializers.OcrResultListSerializer(many=True)
    )
    @action(url_path='batch-update', detail=False, methods=['post'])
    def batch_update(self, request, format=None):  # ae数值  全更新
        data_list = request.data
        # step = request.query_params.get('step')
        # import json
        # if len(data_list) < 1:
        #     raise TaskExistsStatusIN_PROGRESS()
        # # data_list = json.loads(data_list)
        # # print(data_list)
        # import codecs
        # from rest_framework import status, serializers
        # data_str = codecs.decode(request.body, 'utf-8-sig')
        # data_list = serializers.json.loads(data_str)
        # # data_str = request.body.decode('utf-8-sig')
        # # from rest_framework import status, serializers
        # # data_list = serializers.json.loads(data_str)
        updated_instances = []
        fields_to_update = ['test_code', 'test_name', 'test_value', 'test_unit', 'reference_value',
                            'test_flag', 'abnormal_symbol']  # 这里替换为你实际要更新的字段名
        # 检查请求数据是否为列表
        if not isinstance(data_list, list):
            return Response({"detail": "请求数据必须是一个列表"}, status=status.HTTP_400_BAD_REQUEST)
        with transaction.atomic():
            # update_time
            for data in data_list:
                # 检查每条数据是否包含 id
                instance_id = data.get('id')
                if not instance_id:
                    subject_item_id = data_list[0].get('subject_item_id')
                    words = 'OCR识别结果'
                    material_file = {
                        'target_type': OperationLog.CONFIRM_OCR_LOG,
                        'operate_content': '{}确认{}完成'.format(
                            request.sys_user.realname + ' ' + request.sys_user.username, words),
                        # 'version': 'v' + str(formatted_time),
                        'target_id': subject_item_id,
                        'create_user': request.sys_user.username,
                        'create_name': request.sys_user.realname
                    }
                    task_info = OperationLog.objects.create(**material_file)
                    return Response({"detail": "已确认OCR结果"}, status=status.HTTP_200_OK)

                try:
                    # 根据 id 获取要更新的实例
                    instance = models.TestResult.objects.get(id=instance_id)
                    filtered_data = {key: value for key, value in data.items() if key in fields_to_update}

                    # 这里可以对数据进行修改，例如将某个字段的值统一添加前缀
                    # 示例：假设 TestResult 模型有一个名为 'field_name' 的字段
                    # if 'field_name' in data:
                    #     data['field_name'] = "Updated_" + str(data['field_name'])

                    # 创建序列化器并验证数据
                    serializer = self.get_serializer(instance, data=filtered_data, partial=True)
                    if serializer.is_valid():
                        # 保存更新后的实例
                        updated_instance = serializer.save()
                        updated_instances.append(updated_instance)
                    else:
                        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

                except models.TestResult.DoesNotExist:
                    return Response({"detail": f"未找到 id 为 {instance_id} 的记录"}, status=status.HTTP_404_NOT_FOUND)

        # 序列化更新后的实例列表
        updated_serializer = self.get_serializer(updated_instances, many=True)

        subject_item_id = data_list[0].get('subject_item_id')
        # subject_id = data_list[0].get('subject_id')
        # a = SubjectItem.objects.filter(subject_item_id=subject_item_id, subject_id=subject_id).first()
        # if int(step) == 2:
        words = 'OCR识别结果'
        material_file = {
            'target_type': OperationLog.CONFIRM_OCR_LOG,
            'operate_content': '{}确认{}完成'.format(request.sys_user.realname + ' ' + request.sys_user.username, words),
            # 'version': 'v' + str(formatted_time),
            'target_id': subject_item_id,
            'create_user': request.sys_user.username,
            'create_name': request.sys_user.realname
        }
        task_info = OperationLog.objects.create(**material_file)
        return Response(updated_serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        summary='单条更新化验单AE结果2',
        tags=['访视详情'],
        request=serializers.UpdateAeLevelSerializer,
        responses=serializers.OcrResultSerializer
    )
    def partial_update(self, request, *args, **kwargs):  # ae数值  更新ae 等级
        data = request.data.copy()
        data['update_user'] = request.sys_user.username
        data['update_name'] = request.sys_user.realname
        data['ae_edit_flag'] = 1

        test_result = self.get_object()
        print(test_result.id)
        a = models.TestResult.objects.filter(
            id=test_result.id,
        ).first()
        if data.get('ae_grade') is not None and int(data.get('ae_grade')) > 0:  # or data.get('ae_name')
            data['abnormal_flag'] = 1
        if data.get('ae_grade') is not None and int(data.get('ae_grade')) == 0:  # or data.get('ae_name')
            data['abnormal_flag'] = 0
        if a.ae_ai_result_list[0]['ae_grade'] is not None and a.ae_ai_result_list[0]['ae_grade'] != '' and int(a.ae_ai_result_list[0]['ae_grade']) == int(
                data.get('ae_grade')):
            data['ae_edit_flag'] = 0
            # operationlog = OperationLog.objects.filter(
            #     target_id=test_result.id,
            #     target_type=OperationLog.EDIT_AE_LOG,
            #     delete_flag=0
            # ).first()
            # if operationlog:
            #     data['ae_edit_flag'] = 1

        else:
            if data.get('ae_grade') is None and a.ae_ai_result_list[0]['ae_grade'] is None:
                data['ae_edit_flag'] = 0
            if data.get('ae_grade') is not None:
                ae_grade = int(data.get('ae_grade'))
                ae_grade_list = {
                    None: "未判定",
                    0: "正常",
                    1: "1级",
                    2: "2级",
                    3: "3级",
                    4: "4级",
                    5: "5级",
                    6: "NCS"
                }
                # 根据 ae_grade 的值从字典中获取对应的结果
                ae_grade_result = ae_grade_list.get(ae_grade, "未知")
                with transaction.atomic():
                    opt_log = {
                        'target_type': OperationLog.EDIT_AE_LOG,
                        'operate_content': '{}修改{}等级为{}'.format(
                            request.sys_user.realname + ' ' + request.sys_user.username,
                            data.get('test_name'), ae_grade_result),
                        # 'version': 'v' + str(formatted_time),
                        'target_id': test_result.id,
                        'create_user': request.sys_user.username,
                        'create_name': request.sys_user.realname
                    }
                    OperationLog.objects.create(**opt_log)

        try:
            if data['medical_history_flag'] is None:

                latest_result = models.TestResult.objects.filter(
                    subject_id=a.subject_id,
                    test_name=data['test_name'],
                    medical_history_flag__isnull=False
                ).order_by('-create_time').first()
                data['medical_history_flag'] = latest_result.medical_history_flag
                if int(data['medical_history_flag']) == 1:
                    data['ae_name'] = '病史'

            if data['medical_history_flag'] != '' and int(data['medical_history_flag']) == 1:
                data['ae_name'] = '病史'
        except:
            pass

        partial = kwargs.pop('partial', True)
        serializer = serializers.OcrResultSerializer(test_result, data=data, partial=partial)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        self.perform_update(serializer)
        serializer = serializers.OcrResultSerializer(test_result)

        subject_item = SubjectItem.objects.filter(subject_item_id=a.subject_item_id, subject_id=a.subject_id).first()

        test_results = models.TestResult.objects.filter(subject_id=a.subject_id, subject_item__label=subject_item.label,
                                                        subject_item__ae_ai_current_step=6, delete_flag=0)
        # test_results = models.TestResult.objects.filter(subject_id=a.subject_id, delete_flag=0)
        make_ae_tracker_flage = make_ae_tracker_flag(test_results, subject_item)
        push_tracker = post_tracker(a.project_id, a.project_site_id, a.subject_id, a.subject_item_id, request.sys_user.username, request.sys_user.realname)
        return Response(serializer.data)

    @extend_schema(
        summary='单条OCR编辑偏高偏低',
        tags=['访视详情'],
        request=serializers.UpdateOCRHighLowRequestSerializer,
        responses=serializers.UpdateOCRHighLowResponseSerializer
    )
    @action(url_path='high-low', detail=False, methods=['post'])
    def high_low(self, request, format=None):
        data = request.data.copy()
        test_value = data.get('test_value')
        data['test_flag'] = '0'
        try:
            if str(test_value) == str(data.get('reference_value')):
                data['test_flag'] = '0'
            # else:
            pattern = r'^(?:[1-9]\d*|0)(?:\.\d+)?$'
            if re.match(pattern, str(test_value)):
                # raise NonNumericInputException
                reference_value = data.get('reference_value')
                if reference_value and '-' in reference_value:
                    reference_range_min = reference_value.split("-")[0]
                    reference_range_max = reference_value.split("-")[1]
                    if float(test_value) >= float(reference_range_min) and float(test_value) <= float(
                            reference_range_max):
                        data['test_flag'] = '0'
                    if float(test_value) > float(reference_range_max):
                        data['test_flag'] = '1'
                    if float(test_value) < float(reference_range_min):
                        data['test_flag'] = '2'

            # 实例化序列化器并传入数据
            serializer = serializers.UpdateOCRHighLowResponseSerializer(data=data)
            # 验证数据
            if serializer.is_valid():
                # 返回序列化后的数据
                return Response(serializer.data)
            else:
                # 序列化器验证失败，返回错误信息
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            raise EditHighLowRequests()
            # return Response({"error": '数据不符合箭头调用标准'}, status=status.HTTP_200_OK)

        # except Exception as e:
        #     # 处理其他异常
        #     return Response({"error": f"An unexpected error occurred: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)

    # @extend_schema(
    #     summary='单条更新化验单AE结果',
    #     tags=['访视详情'],
    #     request=serializers.UpdateAeLevelSerializer,
    #     responses=serializers.OcrResultListSerializer
    # )
    # @action(url_path='single-update', detail=False, methods=['post'])
    # def single_update(self, request, format=None):
    #     data = request.data
    #     # 检查请求数据是否包含 id
    #     instance_id = data.get('id')
    #     if not instance_id:
    #         return Response({"detail": "请求数据必须包含 'id' 字段"}, status=status.HTTP_400_BAD_REQUEST)
    #
    #     try:
    #         # 根据 id 获取要更新的实例
    #         instance = models.TestResult.objects.get(id=instance_id)
    #
    #         # 创建序列化器并验证数据
    #         serializer = self.get_serializer(instance, data=data, partial=True)
    #         if serializer.is_valid():
    #             # 保存更新后的实例
    #             updated_instance = serializer.save()
    #         else:
    #             return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    #
    #     except models.TestResult.DoesNotExist:
    #         return Response({"detail": f"未找到 id 为 {instance_id} 的记录"}, status=status.HTTP_404_NOT_FOUND)
    #
    #     # 序列化更新后的实例
    #     updated_serializer = self.get_serializer(updated_instance)
    #
    #
    #     material_file = {
    #         'target_type': 'confirm_ae_level',
    #         'operate_content': '操作内容：{}修改{}等级为{}'.format(request.sys_user.realname + ' ' + request.sys_user.username, data.get('test_name'), data.get('ae_grade')),
    #         # 'version': 'v' + str(formatted_time),
    #         'target_id': instance_id,
    #         'create_user': request.sys_user.username,
    #         'create_name': request.sys_user.realname
    #     }
    #     task_info = OperationLog.objects.create(**material_file)
    #     return Response(updated_serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        summary='确认AE结果，点击下一步生成脚印',
        tags=['访视详情'],
        # request=serializers.ConfirmAiCtcaeSerializer,
        request=serializers.ConfirmAiCtcaeSerializer,
        responses=serializers.OcrResultListSerializer(many=True)
    )
    @action(url_path='confirm-ai-ctcae', detail=False, methods=['post'])
    def confirm_ai_ctcae(self, request, format=None):

        subject_item_id = request.data.get('subject_item_id')
        subject_id = request.data.get('subject_id')

        task = models.AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        task_ae = models.AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[models.AeTrackerTask.COMPLETED],
            delete_flag=0
        ).filter(Q(category=models.AeTrackerTask.AE_RECOGNITION)).order_by('-create_time').first()

        if task or not task_ae:
            raise TaskExistsStatusIN_PROGRESS()

        a = SubjectItem.objects.filter(subject_item_id=subject_item_id, subject_id=subject_id).first()
        words = 'AI判定CTCAE等级'
        material_file = {
            'target_type': OperationLog.CONFIRM_AE_LOG,
            'operate_content': '{}确认{}完成'.format(request.sys_user.realname + ' ' + request.sys_user.username,
                                                 words),
            # 'version': 'v' + str(formatted_time),
            'target_id': subject_item_id,
            'create_user': request.sys_user.username,
            'create_name': request.sys_user.realname
        }
        task_info = OperationLog.objects.create(**material_file)
        a.ae_ai_current_step = 4
        a.save()
        serializer = serializers.OperationLogListSerializer(task_info)
        return Response(serializer.data)

    @extend_schema(
        summary='确认AE用药结果，点击下一步生成脚印',
        tags=['访视详情'],
        # request=serializers.ConfirmAiCtcaeSerializer,
        request=serializers.ConfirmAiCtcaeSerializer,
        responses=serializers.OcrResultListSerializer(many=True)
    )
    @action(url_path='confirm-ai-meds', detail=False, methods=['post'])
    def confirm_ai_meds(self, request, format=None):

        subject_item_id = request.data.get('subject_item_id')
        subject_id = request.data.get('subject_id')

        task = models.AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        task_ae = models.AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[models.AeTrackerTask.COMPLETED],
            delete_flag=0
        ).filter(Q(category=models.AeTrackerTask.AE_MEDICATION_MEASURES)).order_by('-create_time').first()

        if task or not task_ae:
            raise TaskExistsStatusIN_PROGRESS()

        a = SubjectItem.objects.filter(subject_item_id=subject_item_id, subject_id=subject_id).first()
        words = '方案建议用药措施'
        material_file = {
            'target_type': OperationLog.CONFIRM_AE_MEDICATION_MEASURES,
            'operate_content': '{}确认{}完成'.format(request.sys_user.realname + ' ' + request.sys_user.username,
                                                 words),
            # 'version': 'v' + str(formatted_time),
            'target_id': subject_item_id,
            'create_user': request.sys_user.username,
            'create_name': request.sys_user.realname
        }
        task_info = OperationLog.objects.create(**material_file)
        a.ae_ai_current_step = 5
        a.save()
        serializer = serializers.OperationLogListSerializer(task_info)
        return Response(serializer.data)

    @extend_schema(
        summary='生成AEtracker表，点击下一步确认生成脚印',
        tags=['访视详情'],
        # request=serializers.ConfirmAiCtcaeSerializer,
        request=serializers.ConfirmAiCtcaeSerializer,
        responses=serializers.OcrResultListSerializer(many=True)
    )
    @action(url_path='create-ae-tracker', detail=False, methods=['post'])
    def create_ae_tracker(self, request, format=None):
        subject_item_id = request.data.get('subject_item_id')
        subject_id = request.data.get('subject_id')

        task = models.AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        task_ae = models.AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[models.AeTrackerTask.COMPLETED],
            delete_flag=0
        ).filter(Q(category=models.AeTrackerTask.AE_RECOGNITION)).order_by('-create_time').first()

        if task or not task_ae:
            raise TaskExistsStatusIN_PROGRESS()

        a = SubjectItem.objects.filter(subject_item_id=subject_item_id, subject_id=subject_id).first()
        words = '生成AEtracker表'
        material_file = {
            'target_type': OperationLog.CREATE_AE_TRACKER_LOG,
            'operate_content': '{}确认{}完成'.format(request.sys_user.realname + ' ' + request.sys_user.username,
                                                 words),
            # 'version': 'v' + str(formatted_time),
            'target_id': subject_item_id,
            'create_user': request.sys_user.username,
            'create_name': request.sys_user.realname
        }
        task_info = OperationLog.objects.create(**material_file)
        a.ae_ai_current_step = 6
        a.save()
        serializer = serializers.OperationLogListSerializer(task_info)

        # models.TestResult.objects.filter(subject_item_id=subject_item_id, delete_flag=0).update(ae_tracker_flag=1)

        test_results = models.TestResult.objects.filter(subject_id=subject_id, subject_item__label=a.label,
                                                        subject_item__ae_ai_current_step=6, delete_flag=0)
        make_ae_tracker_flage = make_ae_tracker_flag(test_results, a)


        push_tracker = post_tracker(a.project_id, a.project_site_id, a.subject_id, a.subject_item_id, request.sys_user.username, request.sys_user.realname)

        return Response(serializer.data)


class AeTrackerTasksView(BaseAPIView, GenericViewSet, CreateModelMixin, RetrieveModelMixin):
    # parser_classes = (MultiPartParser, )  # 支持文件上传
    queryset = models.AeTrackerTask.objects.filter(delete_flag=0)
    serializer_class = serializers.AeTrackerTaskSerializer
    filterset_class = filters.ProjectMaterialInfoFilter

    # def get_serializer_class(self):
    #     if self.action == 'list':
    #         return serializers.ProjectMaterialInfoSerializer
    #     elif self.action == 'create':
    #         return serializers.ProjectMaterialFileSerializer
    #     return super().get_serializer_class()

    # @extend_schema(
    #     summary='获取最新的项目素材信息',
    #     tags=['项目'],
    #     parameters=[ProjectMaterialLatestRequestSerializer],
    #     responses=serializers.ProjectMaterialInfoSerializer
    # )
    # @action(detail=False, methods=['get'])
    # def latest(self, request, project_id, format=None):
    #     category = request.query_params.get('category')
    #     queryset = self.queryset
    #     if category:
    #         queryset = queryset.filter(project_id=project_id, category=category)
    #     latest_info = queryset.order_by('-create_time').first()
    #     if not latest_info:
    #         raise NotFound("还没有上传素材文件")
    #     serializer = self.get_serializer(latest_info)
    #     return Response(serializer.data)

    @extend_schema(
        summary='访视详情打码、ocr、AE任务',
        tags=['访视详情'],
        request=serializers.AeTrackerTaskCreatRequestSerializer,
        # parameters=[serializers.AeTrackerTaskCreatRequestSerializer],
        responses=serializers.AeTrackerTaskSerializer
    )
    def create(self, request, format=None):
        # file = request.FILES.get('file')
        category = request.data.get('category')
        subject_item_id = request.data.get('subject_item_id')
        subject_id = request.data.get('subject_id')
        a = SubjectItem.objects.filter(subject_item_id=subject_item_id, subject_id=subject_id, delete_flag=0).first()
        if a.item_type is None:
            raise NoChosenItemType()
        if a.item_type == '1':
            # if not SubjectMedicalInfo.objects.filter(
            #         subject_id=subject_id,
            #         subject_item_id=subject_item_id,
            #         delete_flag=0,
            #         file__object_name__isnull=False
            # ).exclude(file__object_name__endswith='.pdf').exists():
            #     raise MedicalInfoPicNotExistsError()
            if not SubjectMedicalInfo.objects.filter(
                    subject_id=subject_id, subject_item_id=subject_item_id, delete_flag=0
            ).exists():
                raise MedicalInfoNotExistsError()
        else:
            if not SubjectMedicalInfo.objects.filter(
                    subject_id=subject_id, subject_item_id=subject_item_id, delete_flag=0
            ).exists():
                raise MedicalInfoNotExistsError()

        if category == models.AeTrackerTask.PIC_MASK:
            # a.ae_ai_current_step = 1
            # a.save()
            # task = models.AeTrackerTask.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
            #     delete_flag=0
            # ).order_by('-create_time').first()
            # if task:
            raise PicMaskRequests()

            # 软删除 AeTrackerTask 模型的相关对象
            models.AeTrackerTask.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).update(delete_flag=1)

            test_result_ids = list(
                models.TestResult.objects.filter(
                    subject_id=subject_id,
                    subject_item_id=subject_item_id,
                    delete_flag=0
                ).values_list("id", flat=True)
            )
            models.TestResult.objects.filter(id__in=test_result_ids).update(delete_flag=1)
            OperationLog.objects.filter(
                target_id__in=test_result_ids
            ).update(delete_flag=1)
            OperationLog.objects.filter(
                target_id=subject_item_id,
                delete_flag=0
            ).update(delete_flag=1)

            models.TestOcrResult.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).update(delete_flag=1)

            # SubjectMedicalInfo.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     delete_flag=0
            # ).update(file_masked_id=None, ocr_time=None)
            # 软删除 OperationLog 模型的相关对象
            # a = SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).first()
            SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
                ae_ai_current_step=0, ae_ai_task_id=0)
            # return Response({"message": "数据清空及更新操作成功"}, status=status.HTTP_200_OK)

            # task_pic = models.AeTrackerTask.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
            #     delete_flag=0
            # ).order_by('-create_time').first()

            ai_step = SubjectItem.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                ae_ai_current_step=0,
                delete_flag=0
            ).order_by('-create_time').first()

            # models.TestResult.objects.filter(subject_item_id=subject_item_id, delete_flag=0).update(ae_tracker_flag=1)

            test_results = models.TestResult.objects.filter(subject_id=subject_id, subject_item__label=a.label,
                                                            subject_item__ae_ai_current_step=6, delete_flag=0)
            # make_ae_tracker_flage = make_ae_tracker_flag(test_results, a)

            if not ai_step:  # task or
                raise TaskExistsStatusIN_PROGRESS()

        if category == models.AeTrackerTask.OCR_EXTRACTION:

            ocr_file = SubjectMedicalInfo.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).order_by('-create_time').first()
            if not ocr_file:
                raise NoFileError()

            task = models.AeTrackerTask.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
                delete_flag=0
            ).filter(~Q(category=models.AeTrackerTask.PIC_MASK)).order_by('-create_time').first()
            if task:
                raise TaskExistsStatusIN_PROGRESS()

            ocr_task = SubjectMedicalInfo.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                ocr_status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
                delete_flag=0
            ).order_by('-create_time').first()
            if ocr_task:
                raise OcrTaskExistsStatusIN_PROGRESS()



            models.AeTrackerTask.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).update(delete_flag=1)

            test_result_ids = list(
                models.TestResult.objects.filter(
                    subject_id=subject_id,
                    subject_item_id=subject_item_id,
                    delete_flag=0
                ).values_list("id", flat=True)
            )
            models.TestResult.objects.filter(id__in=test_result_ids).update(delete_flag=1)
            OperationLog.objects.filter(
                target_id__in=test_result_ids
            ).update(delete_flag=1)
            OperationLog.objects.filter(
                target_id=subject_item_id,
                delete_flag=0
            ).update(delete_flag=1)

            models.TestOcrResult.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).update(delete_flag=1)

            # SubjectMedicalInfo.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     delete_flag=0
            # ).update(file_masked_id=None, ocr_time=None)
            # 软删除 OperationLog 模型的相关对象
            # a = SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).first()
            SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
                ae_ai_current_step=1, ae_ai_task_id=0)
            # return Response({"message": "数据清空及更新操作成功"}, status=status.HTTP_200_OK)

            # task_pic = models.AeTrackerTask.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
            #     delete_flag=0
            # ).order_by('-create_time').first()

            ai_step = SubjectItem.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                ae_ai_current_step=1,
                delete_flag=0
            ).order_by('-create_time').first()

            # models.TestResult.objects.filter(subject_item_id=subject_item_id, delete_flag=0).update(ae_tracker_flag=1)

            test_results = models.TestResult.objects.filter(subject_id=subject_id, subject_item__label=a.label,
                                                            subject_item__ae_ai_current_step=6, delete_flag=0)
            make_ae_tracker_flage = make_ae_tracker_flag(test_results, a)
            push_tracker = post_tracker(a.project_id, a.project_site_id, a.subject_id, a.subject_item_id, request.sys_user.username, request.sys_user.realname)
            if task or not ai_step:  # task or
                raise TaskExistsStatusIN_PROGRESS()

        if category == models.AeTrackerTask.OCR_EXTRACTION_TEXT:
            ocr_file = SubjectMedicalInfo.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).order_by('-create_time').first()
            if not ocr_file:
                raise NoFileError()
            # test_ocr_result = models.TestOcrResult.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     delete_flag=0
            # ).first()
            task = models.AeTrackerTask.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
                delete_flag=0
            ).order_by('-create_time').first()

            if task:  # or not ai_step
                raise TaskExistsStatusIN_PROGRESS()
            ocr_task = SubjectMedicalInfo.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                ocr_status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
                delete_flag=0
            ).order_by('-create_time').first()
            if ocr_task:
                raise OcrTaskExistsStatusIN_PROGRESS()

            models.AeTrackerTask.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).update(delete_flag=1)

            test_result_ids = list(
                models.TestResult.objects.filter(
                    subject_id=subject_id,
                    subject_item_id=subject_item_id,
                    delete_flag=0
                ).values_list("id", flat=True)
            )
            models.TestResult.objects.filter(id__in=test_result_ids).update(delete_flag=1)
            OperationLog.objects.filter(
                target_id__in=test_result_ids
            ).update(delete_flag=1)
            OperationLog.objects.filter(
                target_id=subject_item_id,
                delete_flag=0
            ).update(delete_flag=1)

            models.TestOcrResult.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).update(delete_flag=1)

            SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
                ae_ai_current_step=1, ae_ai_task_id=0)

            ai_step = SubjectItem.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                ae_ai_current_step=1,
                delete_flag=0
            ).order_by('-create_time').first()

            # models.TestResult.objects.filter(subject_item_id=subject_item_id, delete_flag=0).update(ae_tracker_flag=1)

            test_results = models.TestResult.objects.filter(subject_id=subject_id, subject_item__label=a.label,
                                                            subject_item__ae_ai_current_step=6, delete_flag=0)
            make_ae_tracker_flage = make_ae_tracker_flag(test_results, a)
            push_tracker = post_tracker(a.project_id, a.project_site_id, a.subject_id, a.subject_item_id, request.sys_user.username, request.sys_user.realname)
            if task or not ai_step:  # task or
                raise TaskExistsStatusIN_PROGRESS()

        if category == models.AeTrackerTask.AE_RECOGNITION:
            task = models.AeTrackerTask.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
                delete_flag=0
            ).order_by('-create_time').first()
            if task:
                raise TaskExistsStatusIN_PROGRESS()

            test_result_ids = list(
                models.TestResult.objects.filter(
                    subject_id=subject_id,
                    subject_item_id=subject_item_id,
                    delete_flag=0
                ).values_list("id", flat=True)
            )
            models.TestResult.objects.filter(id__in=test_result_ids).update(ae_name=None, ae_grade=None, ae_desc=None, ae_edit_flag=None, ae_ai_result_list=None,
                     ae_ai_result_flag=None, medical_history_flag=None, ae_tracker_flag=0, ae_meds=None, meds_edit_flag=None)

            ## models.TestResult.objects.filter(subject_item_id=subject_item_id, delete_flag=0).update(ae_tracker_flag=1)

            test_results = models.TestResult.objects.filter(subject_id=subject_id, subject_item__label=a.label,
                                                            subject_item__ae_ai_current_step=6, delete_flag=0)
            make_ae_tracker_flage = make_ae_tracker_flag(test_results, a)
            push_tracker = post_tracker(a.project_id, a.project_site_id, a.subject_id, a.subject_item_id, request.sys_user.username, request.sys_user.realname)
            # queryset = models.TestResult.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     delete_flag=0
            # )
            # unique_test_names = queryset.values_list('test_name', flat=True).distinct()
            # for test_name in unique_test_names:
            #     models.TestResult.objects.filter(
            #         subject_id=subject_id,
            #         test_name=test_name,
            #         delete_flag=0
            #     ).update(ae_tracker_flag=0)

            # 软删除 AeTrackerTask 模型的相关对象
            # models.AeTrackerTask.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     delete_flag=0
            # ).update(delete_flag=1)

            # SubjectMedicalInfo.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     delete_flag=0
            # ).update(file_masked_id=None, ocr_time=None)
            # 软删除 OperationLog 模型的相关对象
            # a = SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).first()
            OperationLog.objects.filter(
                target_id=subject_item_id,
                delete_flag=0
                # target_type__ne=OperationLog.CONFIRM_OCR_LOG
            ).exclude(target_type=OperationLog.CONFIRM_OCR_LOG).update(delete_flag=1)
            OperationLog.objects.filter(
                target_id__in=test_result_ids
            ).update(delete_flag=1)

            SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
                ae_ai_current_step=2, ae_ai_task_id=0)

            # task = models.AeTrackerTask.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
            #     delete_flag=0
            # ).order_by('-create_time').first()
            if a.item_type == '1':
                task_ocr = models.AeTrackerTask.objects.filter(
                    subject_id=subject_id,
                    subject_item_id=subject_item_id,
                    category=models.AeTrackerTask.OCR_EXTRACTION,
                    delete_flag=0
                ).order_by('-create_time').first()
            else:
                task_ocr = models.AeTrackerTask.objects.filter(
                    subject_id=subject_id,
                    subject_item_id=subject_item_id,
                    category=models.AeTrackerTask.OCR_EXTRACTION_TEXT,
                    delete_flag=0
                ).order_by('-create_time').first()

            # task_ae = models.AeTrackerTask.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     status__in=[models.AeTrackerTask.COMPLETED],
            #     delete_flag=0
            # ).filter(Q(category=models.AeTrackerTask.OCR_EXTRACTION)).order_by('-create_time').first()

            # ai_step = SubjectItem.objects.filter(
            #     subject_id=subject_id,
            #     subject_item_id=subject_item_id,
            #     ae_ai_current_step=2,
            #     delete_flag=0
            # ).order_by('-create_time').first()

            # if not (task_ocr and ai_step):
            if task_ocr.status != models.AeTrackerTask.COMPLETED or task:
                raise TaskExistsStatusIN_PROGRESS()

        if category == models.AeTrackerTask.AE_MEDICATION_MEASURES:
            # project_id = a.project_id
            # query = """
            #                 SELECT project_id
            #                 FROM project_plan_ae_drug
            #                 WHERE project_id = %s
            #                 AND delete_flag = 0
            #             """
            # # 构建查询参数
            # params = [project_id]
            # # 使用正确的数据库连接和游标
            # with connections['default'].cursor() as cursor:  # 如果使用默认数据库
            #     cursor.execute(query, params)
            #     columns = [col[0] for col in cursor.description]
            #     suggested_medication_list = [
            #         dict(zip(columns, row))
            #         for row in cursor.fetchall()
            #     ]
            # if not suggested_medication_list:
            #     a.ae_ai_current_step = 3
            #     a.save()
            #     raise NoAeMeds()
            task = models.AeTrackerTask.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
                delete_flag=0
            ).order_by('-create_time').first()
            if task:
                raise TaskExistsStatusIN_PROGRESS()

            OperationLog.objects.filter(
                target_id=subject_item_id,
                delete_flag=0,
                target_type__in=[OperationLog.CONFIRM_AE_MEDICATION_MEASURES, OperationLog.CREATE_AE_TRACKER_LOG]
            ).update(delete_flag=1)


            SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
                ae_ai_current_step=4, ae_ai_task_id=0)


            task_ae = models.AeTrackerTask.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                category=models.AeTrackerTask.AE_RECOGNITION,
                delete_flag=0
            ).order_by('-create_time').first()

            if task_ae.status != models.AeTrackerTask.COMPLETED or task:
                raise TaskExistsStatusIN_PROGRESS()

        subject_item = SubjectItem.objects.filter(delete_flag=0).get(subject_item_id=subject_item_id,
                                                                     subject_id=subject_id)
        # 数据入库
        if category != models.AeTrackerTask.PIC_MASK:
            with transaction.atomic():
                start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                task_info = {
                    'name': str(category) + '-' + str(subject_id) + str(subject_item_id) + 'task',
                    'category': category,
                    'subject_id': subject_id,
                    'subject_item_id': subject_item_id,
                    'create_user': request.sys_user.username,
                    'create_name': request.sys_user.realname,
                    'start_time': start_time,
                    'patient': subject_item.subject.patient,
                    'project': subject_item.project,
                    'project_site': subject_item.project_site,
                    'subject_epoch': subject_item.subject_visit.subject_epoch,
                    'subject_visit': subject_item.subject_visit
                }
                task = models.AeTrackerTask.objects.create(**task_info)
                subject_item.ae_ai_task_id = task.id
                subject_item.save()
                serializer = serializers.AeTrackerTaskSerializer(task)
                # if not serializer.is_valid():
                #     raise APIException(serializer.errors)
                if category == models.AeTrackerTask.AE_RECOGNITION:
                    a.ae_ai_current_step = 3
                    a.save()
        if category == models.AeTrackerTask.PIC_MASK:
            # try:
            #     trigger_dag('subject_medical_file_mask', conf={'task_id': task.id})
            # except Exception as e:
            #     logger.error(e)
            #     task.status = 'ERROR'
            #     task.save()
            pass
        if category == models.AeTrackerTask.OCR_EXTRACTION:
            if int(a.item_type) == 1:
                try:
                    trigger_dag('test_result_format', conf={'task_id': task.id})
                except Exception as e:
                    logger.error(e)
                    task.status = 'ERROR'
                    task.save()
            else:
                print('item_type!=1的 不做ocr提取了, 已被替代')
                # try:
                #     trigger_dag('test_result_format_ocr', conf={'task_id': task.id})
                # except Exception as e:
                #     logger.error(e)
                #     task.status = 'ERROR'
                #     task.save()
            # trigger_dag('test_result_format', conf={'task_id': task.id})
        if category == models.AeTrackerTask.OCR_EXTRACTION_TEXT:
            try:
                trigger_dag('test_result_format_ocr_text', conf={'task_id': task.id})
            except Exception as e:
                logger.error(e)
                task.status = 'ERROR'
                task.save()
        if category == models.AeTrackerTask.AE_RECOGNITION:
            # if int(a.item_type) == 1:
            #     trigger_dag('ae_grade_recognition', conf={'task_id': task.id})
            # else:
            #     trigger_dag('ae_grade_recognition_ocr', conf={'task_id': task.id})
            try:
                trigger_dag('ae_grade_recognition', conf={'task_id': task.id})
            except Exception as e:
                logger.error(e)
                task.status = 'ERROR'
                task.save()
        if category == models.AeTrackerTask.AE_MEDICATION_MEASURES:
            try:
                trigger_dag('ae_medication_measures', conf={'task_id': task.id})
            except Exception as e:
                logger.error(e)
                task.status = 'ERROR'
                task.save()
        if category == models.AeTrackerTask.PIC_MASK:
            return Response([])
        return Response(serializer.data)

    @extend_schema(summary='获取定时任务状态', tags=['访视详情'], deprecated=True)
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(self, request, *args, **kwargs)

    # @extend_schema(
    #     summary='查询特定条件的任务数据',
    #     tags=['访视详情'],
    #     responses=serializers.AeTrackerTaskSerializer(many=True)
    # )
    # @extend_schema(
    #     summary='查询是否存在任务',
    #     tags=['访视详情'],
    #     parameters=[serializers.AeTrackerTaskListRequestSerializer],
    #     # parameters=[serializers.AeTrackerTaskCreatRequestSerializer],
    #     responses=serializers.AeTrackerTaskSerializer
    # )
    # @action(url_path='get-specific-tasks', detail=False, methods=['get'])
    # def get_specific_tasks(self, request, format=None):
    #     subject_id = request.query_params.get('subject_id')
    #     subject_item_id = request.query_params.get('subject_item_id')
    #     print(subject_id, subject_item_id)
    #     queryset = self.queryset.filter(
    #         subject_id=subject_id,
    #         subject_item_id=subject_item_id,
    #         status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
    #         delete_flag=0
    #     )
    #     serializer = serializers.AeTrackerTaskSerializer(queryset, many=True)
    #     print(serializer.data)
    #     return Response(serializer.data)
    @extend_schema(
        summary='查询AE+病历归集特定任务状态',
        tags=['访视详情'],
        # parameters=[serializers.AeTrackerTaskListRequestSerializer],
        parameters=[serializers.AeTrackerTaskCreatRequestSerializer],
        responses=serializers.AeTrackerTaskSerializer
    )
    @action(url_path='get-specific-tasks', detail=False, methods=['get'])
    def get_specific_tasks(self, request, format=None):
        subject_id = request.query_params.get('subject_id')
        subject_item_id = request.query_params.get('subject_item_id')
        subject_visit_id = request.query_params.get('subject_visit_id')
        category = request.query_params.get('category')
        print(subject_id, subject_item_id)
        if category != MedicalCollectionTask.CRF and category != MedicalCollectionTask.MEDICAL_RECORD:
            queryset = self.queryset.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                category=category,
                # status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
                delete_flag=0
            ).order_by('-create_time').first()
            serializer = serializers.AeTrackerTaskSerializer(queryset)
            print(serializer.data)
            return Response(serializer.data)
        else:
            queryset = MedicalCollectionTask.objects.filter(
                subject_id=subject_id,
                subject_visit_id=subject_visit_id,
                category=category,
                # status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
                delete_flag=0
            ).order_by('-create_time').first()
            serializer = MedicalCollectionTaskSerializer(queryset)
            print(serializer.data)
            return Response(serializer.data)


class LoggerViewSet(BaseAPIView, GenericViewSet):
    queryset = OperationLog.objects.filter(delete_flag=0)

    @extend_schema(
        summary='步骤日志查询',
        tags=['访视详情'],
        parameters=[serializers.AeTrackerOperationLogListRequestSerializer],
        # parameters=[serializers.AeTrackerTaskCreatRequestSerializer],
        responses=serializers.OperationLogListSerializer(many=True)
    )
    @action(detail=False, methods=['get'])
    def get_logger(self, request, format=None):
        subject_id = request.query_params.get('subject_id')
        subject_item_id = request.query_params.get('subject_item_id')
        id = request.query_params.get('id')
        step = request.query_params.get('step')
        if step:
            if int(step) == 2:
                target_type = 'confirm_ocr_results'
            if int(step) == 3:
                target_type = 'confirm_ae_results'
            if int(step) == 4:
                target_type = 'confirm_ae_tracker'
            a = SubjectItem.objects.filter(subject_item_id=subject_item_id, subject_id=subject_id).first()
            print(subject_id, subject_item_id)
            queryset = self.queryset.filter(
                target_id=a.id,
                target_type=target_type
            ).order_by('-create_time')  # .first()
            print(queryset)
            serializer = serializers.OperationLogListSerializer(queryset, many=True)
            print(serializer.data)
            return Response(serializer.data)
        else:
            queryset = self.queryset.filter(
                target_id=id,
                target_type='confirm_ae_level'
            ).order_by('-create_time')
            print(queryset)
            serializer = serializers.OperationLogListSerializer(queryset, many=True)
            print(serializer.data)
            return Response(serializer.data)


class OperationLogViewSet(BaseListViewSet, ListModelMixin):
    queryset = OperationLog.objects.filter(delete_flag=0)
    serializer_class = serializers.OperationLogListSerializer
    filterset_class = filters.OperationLogFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'

    # ordering = ['create_time']

    @extend_schema(
        summary='操作日志',
        tags=['访视详情'],
        # request=serializers.ProjectMaterialLatestRequestSerializer,
        responses=serializers.OperationLogListSerializer
    )
    def list(self, request, format=None):
        target_type = request.query_params.get('target_type')
        target_id = request.query_params.get('target_id')
        if target_type == 'EDIT_AE_LOG':
            if target_id is not None:
                self.queryset = self.queryset.filter(
                    target_id=target_id,
                    target_type=target_type,
                    delete_flag=0
                ).order_by('-create_time')
        else:
            if target_id is not None:
                # subject_item = SubjectItem.objects.filter(subject_item_id=target_id).first()
                # if subject_item:
                self.queryset = self.queryset.filter(
                    target_id=target_id,
                    target_type=target_type,
                    delete_flag=0
                ).order_by('-create_time')
            else:
                # 若 SubjectItem 不存在，返回空查询集
                self.queryset = self.queryset.none()

        # return super().list(request, format)
        serializer = serializers.OperationLogListSerializer(self.queryset, many=True)
        return Response(serializer.data)


class DeleteItemDataViewSet(BaseAPIView, GenericViewSet):
    queryset = models.TestResult.objects.all()
    serializer_class = serializers.OcrResultListSerializer

    @extend_schema(
        summary='清空生成数据，从头开始',
        tags=['访视详情'],
        # request=serializers.ConfirmAiCtcaeSerializer,
        request=serializers.ConfirmAiCtcaeSerializer,
        responses=serializers.OcrResultListSerializer(many=True)
    )
    @action(url_path='clear-data', detail=False, methods=['post'])
    def clear_data(self, request, format=None):
        # try:
        subject_item_id = request.data.get('subject_item_id')
        subject_id = request.data.get('subject_id')
        task = models.AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        if task:
            raise TaskExistsStatusIN_PROGRESS()

        test_result_ids = list(
            models.TestResult.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).values_list("id", flat=True)
        )
        models.TestResult.objects.filter(id__in=test_result_ids).update(delete_flag=1)
        models.TestOcrResult.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        # 软删除 AeTrackerTask 模型的相关对象
        models.AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        # SubjectMedicalInfo.objects.filter(
        #     subject_id=subject_id,
        #     subject_item_id=subject_item_id,
        #     delete_flag=0
        # ).update(file_masked_id=None, ocr_time=None)
        # 软删除 OperationLog 模型的相关对象
        # a = SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).first()
        OperationLog.objects.filter(
            target_id__in=test_result_ids
        ).update(delete_flag=1)
        OperationLog.objects.filter(
            target_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)
        SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(ae_ai_current_step=0,
                                                                                                  ae_ai_task_id=0)
        return Response({"message": "数据清空及更新操作成功"}, status=status.HTTP_200_OK)
        # except Exception as e:
        #     return Response({"message": f"操作失败: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BackStepTwoViewSet(BaseAPIView, GenericViewSet):
    queryset = models.TestResult.objects.all()
    serializer_class = serializers.OcrResultListSerializer

    @extend_schema(
        summary='修改结构化-AE等级-用药结果',
        tags=['访视详情'],
        # request=serializers.ConfirmAiCtcaeSerializer,
        request=serializers.AeBackUpSerializer,
        responses=serializers.OcrResultListSerializer(many=True)
    )
    @action(url_path='back-step', detail=False, methods=['post'])
    def back(self, request, format=None):
        # try:
        subject_item_id = request.data.get('subject_item_id')
        subject_id = request.data.get('subject_id')
        category = request.data.get('category')
        task = models.AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[models.AeTrackerTask.IN_PROGRESS, models.AeTrackerTask.TODO],
            delete_flag=0
        ).first()
        if task:
            raise TaskExistsStatusIN_PROGRESS()

        subject_item = SubjectItem.objects.get(subject_id=subject_id, subject_item_id=subject_item_id)
        if category == models.AeTrackerTask.OCR_EXTRACTION and int(subject_item.ae_ai_current_step) >= 2:
            # 如果 ae_ai_current_step 大于等于 2，则更新为 2
            SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
                ae_ai_current_step=2)
            test_result_ids = list(
                models.TestResult.objects.filter(
                    subject_id=subject_id,
                    subject_item_id=subject_item_id,
                    delete_flag=0
                ).values_list("id", flat=True)
            )
            models.TestResult.objects.filter(id__in=test_result_ids).update(ae_name=None, ae_grade=None, ae_desc=None,
                                                                            ae_edit_flag=None, ae_ai_result_list=None,
                                                                            ae_ai_result_flag=None,
                                                                            medical_history_flag=None,
                                                                            ae_tracker_flag=0, ae_meds=None,
                                                                            meds_edit_flag=None)
            test_results = models.TestResult.objects.filter(subject_id=subject_id,
                                                            subject_item__label=subject_item.label,
                                                            subject_item__ae_ai_current_step=6, delete_flag=0)
            make_ae_tracker_flage = make_ae_tracker_flag(test_results, subject_item)
            push_tracker = post_tracker(subject_item.project_id, subject_item.project_site_id, subject_item.subject_id, subject_item.subject_item_id,
                                        request.sys_user.username, request.sys_user.realname)
            return Response({"message": "回到编辑OCR识别结果成功！"}, status=status.HTTP_200_OK)
        elif category == models.AeTrackerTask.AE_RECOGNITION and int(subject_item.ae_ai_current_step) >= 3:
            # 如果 ae_ai_current_step 大于等于 3，则更新为 3
            SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
                ae_ai_current_step=3)
            test_result_ids = list(
                models.TestResult.objects.filter(
                    subject_id=subject_id,
                    subject_item_id=subject_item_id,
                    delete_flag=0
                ).values_list("id", flat=True)
            )
            models.TestResult.objects.filter(id__in=test_result_ids).update(ae_tracker_flag=0, ae_meds=None, meds_edit_flag=None)
            test_results = models.TestResult.objects.filter(subject_id=subject_id,
                                                            subject_item__label=subject_item.label,
                                                            subject_item__ae_ai_current_step=6, delete_flag=0)
            # make_ae_tracker_flage = make_ae_tracker_flag(test_results, subject_item)
            push_tracker = post_tracker(subject_item.project_id, subject_item.project_site_id, subject_item.subject_id,
                                        subject_item.subject_item_id,
                                        request.sys_user.username, request.sys_user.realname)
            return Response({"message": "回到AI判定CTCAE编辑页成功！"}, status=status.HTTP_200_OK)
        elif category == models.AeTrackerTask.AE_MEDICATION_MEASURES and int(subject_item.ae_ai_current_step) >= 4:
            # 如果 ae_ai_current_step 大于等于 3，则更新为 3
            SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
                ae_ai_current_step=4)
            test_result_ids = list(
                models.TestResult.objects.filter(
                    subject_id=subject_id,
                    subject_item_id=subject_item_id,
                    delete_flag=0
                ).values_list("id", flat=True)
            )
            models.TestResult.objects.filter(id__in=test_result_ids).update(ae_tracker_flag=0)
            test_results = models.TestResult.objects.filter(subject_id=subject_id,
                                                            subject_item__label=subject_item.label,
                                                            subject_item__ae_ai_current_step=6, delete_flag=0)
            # make_ae_tracker_flage = make_ae_tracker_flag(test_results, subject_item)
            push_tracker = post_tracker(subject_item.project_id, subject_item.project_site_id, subject_item.subject_id,
                                        subject_item.subject_item_id,
                                        request.sys_user.username, request.sys_user.realname)

            return Response({"message": "回到方案建议用药编辑页成功！"}, status=status.HTTP_200_OK)
        else:
            # 否则返回暂无数据无法编辑的错误信息
            return Response({"message": "当前步骤已更新，请重新操作！"}, status=status.HTTP_400_BAD_REQUEST)


class download_and_zip_view(BaseAPIView):
    @extend_schema(
        summary='下载打码文件zip',
        tags=['访视详情'],
        # request=serializers.ConfirmAiCtcaeSerializer,
        request=SubjectMedicalInfoSerializer(many=True),
        responses=SubjectMedicalInfoSerializer(many=True)
    )
    def post(self, request, format=None):
        data = request.data.copy()
        urls = [item["file_masked"]["url"] for item in data]
        buffer = io.BytesIO()
        with zipfile.ZipFile(buffer, 'w') as zipf:
            for i, url in enumerate(urls):
                try:
                    response = requests.get(url, timeout=10)
                    if response.status_code == 200:
                        file_name = data[i]["file_masked"]["original_filename"]
                        # file_extension = response.headers.get('content-type').split('/')[-1]
                        # file_name = f"file_{i}.{file_extension}"
                        zipf.writestr(file_name, response.content)
                    else:
                        print(f"下载失败: {url}，状态码: {response.status_code}")
                except requests.RequestException as e:
                    print(f"请求出错: {url}，错误信息: {e}")
        buffer.seek(0)
        response = HttpResponse(buffer, content_type='application/zip')
        response['Content-Disposition'] = 'attachment; filename=downloaded_files.zip'
        return response
