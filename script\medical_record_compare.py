import os
import re
import uuid
import shutil
import requests
import tempfile
import hashlib
import datetime
import logging
from docx import Document
from docx.shared import RGBColor, Pt
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
from django.db import connection
from datetime import datetime
from apps.system.models import ModelInvocationLog
from common.minio_client import download_file_from_minio, upload_file_to_minio,get_minio_client
from apps.medical_collection.models import MedicalCollectionFile

# 初始化日志
logger = logging.getLogger(__name__)

def activate_conn(connection):
    try:
        connection.connect()
        if not connection.is_usable():
            connection.close()
            connection.connect()
    except:
        pass
    return connection
def get_rerank_score(query, texts):
    """
    调用 Rerank API 获取相似度分数
    """
    url = 'http://192.168.230.3:8081/rerank'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer cHKajrZMzxw5tgQAeOjSEtgZUWr4AB4P'
    }
    data = {
        "query": query,
        "texts": texts,
        "raw_scores": False,
        "return_text": False,
        "truncate": True,
        "truncation_direction": "Right"
    }
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        return response.json()[0]['score']
    except Exception as e:
        logger.error(f"Rerank request failed: {e}")
        return 0.0
def parse_data_from_docx(file_path):
    """
    解析 .docx 文件，提取 {key}：【value】 格式数据
    """
    if not os.path.exists(file_path):
        logger.error(f"File not found: {file_path}")
        return {}

    try:
        doc = Document(file_path)
        full_text = "\n".join([para.text for para in doc.paragraphs])

        regex_block = re.compile(r'(\{([^}]+)\}：)([\s\S]*?)(?=(?:\s*\{[^}]+\}：)|\Z)')
        data_dict = {}

        for match_block in regex_block.finditer(full_text):
            key = match_block.group(2).strip()
            block_content = match_block.group(3).strip()
            values = re.findall(r'【([^】]*)】', block_content, re.DOTALL)
            if values:
                data_dict[key] = "\n".join([v.strip() for v in values])
            else:
                data_dict[key] = ""
        return data_dict
    except Exception as e:
        logger.error(f"Error parsing {file_path}: {e}")
        return {}
    

def create_highlighted_docx(output_path, base_file_path, comparison_scores_dict):
    """
    创建高亮差异的 docx 文件
    """
    try:
        source_doc = Document(base_file_path)
        new_doc = Document()

        full_text = "\n".join([para.text for para in source_doc.paragraphs])
        full_block_pattern = re.compile(r'(\{([^}]+)\}：)([\s\S]*?)(?=(?:\s*\{[^}]+\}：)|\Z)')
        char_to_block = {}
        block_positions = []

        for match in full_block_pattern.finditer(full_text):
            key = match.group(2).strip()
            start, end = match.start(), match.end()
            block_positions.append((start, end, key))

        for start, end, key in block_positions:
            for i in range(start, end):
                char_to_block[i] = (start, end, key)

        processed_positions = set()

        for para in source_doc.paragraphs:
            new_para = new_doc.add_paragraph(style=para.style)
            new_para.paragraph_format.alignment = para.paragraph_format.alignment

            rPr = new_para.style._element.get_or_add_rPr()
            rFonts = OxmlElement('w:rFonts')
            rFonts.set(qn('w:eastAsia'), '宋体')
            rPr.append(rFonts)
            new_para.style.font.name = '宋体'
            new_para.style.font.size = Pt(11)

            para_start_idx = full_text.find(para.text)
            if para_start_idx == -1:
                run = new_para.add_run(para.text)
                run.font.name = '宋体'
                run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                run.font.size = Pt(11)
                continue

            current_idx_in_para = 0
            current_idx_in_full = para_start_idx

            while current_idx_in_para < len(para.text):
                if current_idx_in_full in char_to_block:
                    block_start, block_end, key = char_to_block[current_idx_in_full]

                    if block_start in processed_positions:
                        current_idx_in_para += 1
                        current_idx_in_full += 1
                        continue

                    for i in range(block_start, block_end):
                        processed_positions.add(i)

                    block_text = full_text[block_start:block_end]
                    key_end_idx_in_block = block_text.find("：") + 1
                    key_part = block_text[:key_end_idx_in_block]
                    value_part = block_text[key_end_idx_in_block:]

                    result = comparison_scores_dict.get(key)
                    should_highlight = result and result['Rerank Score'] < 0.7

                    run_key = new_para.add_run(key)
                    run_key.font.name = '宋体'
                    run_key._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                    run_key.font.size = Pt(11)
                    if should_highlight:
                        run_key.bold = True
                        run_key.font.color.rgb = RGBColor(0, 255, 0)
                        rPr = run_key._element.get_or_add_rPr()
                        # shd = OxmlElement('w:shd')
                        # shd.set(qn('w:val'), 'clear')
                        # shd.set(qn('w:color'), 'auto')
                        # shd.set(qn('w:fill'), '00FF00')
                        # rPr.append(shd)

                    run_colon = new_para.add_run("：")
                    run_colon.font.name = '宋体'
                    run_colon._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                    run_colon.font.size = Pt(11)

                    value_part_clean = re.sub(r'【([^】]*)】', r'\1', value_part)

                    lines = value_part_clean.strip().split('\n')
                    for line in lines:
                        if line.strip():
                            run_line = new_para.add_run(line.strip() + '\n')
                            run_line.font.name = '宋体'
                            run_line._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                            run_line.font.size = Pt(11)
                            if should_highlight:
                                run_line.bold = True
                                run_line.font.color.rgb = RGBColor(255, 0, 0)

                    current_idx_in_para += (block_end - block_start) - (current_idx_in_full - para_start_idx)
                    current_idx_in_full = block_end
                else:
                    plain_text = para.text[current_idx_in_para]
                    run = new_para.add_run(plain_text)
                    run.font.name = '宋体'
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                    run.font.size = Pt(11)

                    current_idx_in_para += 1
                    current_idx_in_full += 1

        new_doc.save(output_path)
        logger.info(f"✅ 已生成高亮文件：{output_path}")
    except Exception as e:
        logger.error(f"生成高亮文件失败 {output_path}: {e}")

def generate_highlighted_docs(file_qwen, file_deepseek, output_qwen, output_deepseek):
    """
    对比两个 docx 文件，生成高亮新文件
    """
    try:
        # 1. 解析两个文件
        data_qwen_dict = parse_data_from_docx(file_qwen)
        data_deepseek_dict = parse_data_from_docx(file_deepseek)

        if not data_qwen_dict or not data_deepseek_dict:
            logger.error("无法解析文件内容")
            return None, None

        # 2. 对比并获取 rerank 分数
        comparison_scores_dict = {}
        all_keys = sorted(set(data_qwen_dict.keys()) | set(data_deepseek_dict.keys()))

        for key in all_keys:
            val_qwen = data_qwen_dict.get(key, "")
            val_deepseek = data_deepseek_dict.get(key, "")

            score = 1.0
            if val_qwen and val_deepseek:
                score = get_rerank_score(val_qwen, [val_deepseek])
            elif val_qwen != val_deepseek:
                score = 0.0

            comparison_scores_dict[key] = {
                'Key': key,
                'Qwen Value': val_qwen,
                'DeepSeek Value': val_deepseek,
                'Rerank Score': score
            }

        # 3. 创建高亮文件
        create_highlighted_docx(output_qwen, file_qwen, comparison_scores_dict)
        create_highlighted_docx(output_deepseek, file_deepseek, comparison_scores_dict)

        return output_qwen, output_deepseek

    except Exception as e:
        logger.error(f"生成高亮文件失败：{e}")
        return None, None


def process_and_update(task_id):
    """
    主函数：下载 qwen 和 ds 的文件，处理后生成新文件，覆盖数据库记录
    """
    try:
        start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        # 获取两个模型的记录
        activate_conn(connection)
        records = MedicalCollectionFile.objects.filter(
            task_id=task_id,
            model_name__in=['qwen3', 'DeepSeek']
        ).order_by('-create_time')
        qwen_record = records.filter(model_name='qwen3').first()
        ds_record = records.filter(model_name='DeepSeek').first()

        if not qwen_record or not ds_record:
            raise ValueError(f"Task {task_id} 缺少 qwen 或 DeepSeek 的文件记录")

        # ✅ 创建当前目录下的随机文件夹
        current_dir = os.path.dirname(os.path.abspath(__file__))
        temp_dir = os.path.join(current_dir, uuid.uuid4().hex)

        os.makedirs(temp_dir, exist_ok=True)
        logger.info(f"✅ 创建临时文件夹：{temp_dir}")

        try:
            # 下载原始文件
            qwen_path = download_file(qwen_record, temp_dir)
            ds_path = download_file(ds_record, temp_dir)

            # 自定义处理逻辑，返回两个新文件路径
            new_qwen_path, new_ds_path = custom_compare(qwen_path, ds_path, temp_dir)

            # 覆盖数据库记录
            update_record(qwen_record, new_qwen_path)
            update_record(ds_record, new_ds_path)

        finally:
            # ✅ 清理当前目录下的临时文件夹
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                logger.info(f"✅ 临时文件夹 {temp_dir} 已删除")
        # 在处理完成后添加日志记录
        end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_comparison_result(task_id, qwen_record, ds_record,start_time, end_time)
    except Exception as e:
        # 记录错误日志
        end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_comparison_result(task_id, qwen_record, ds_record,start_time, end_time, error=str(e))
        raise

def log_comparison_result(task_id, qwen_record, ds_record,start_time, end_time, error=None):
    """
    记录病历小结对比结果到日志表
    """
    try:
        from django.db import connection
        activate_conn(connection)
        
        log_data = {
            'task_id': task_id,
            'category': 'MEDICAL_RECORD_COMPARISON',
            'model_name': 'comparison_tool',
            'start_time': start_time,
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'input_text': f"对比任务ID: {task_id}",
            'output_text': error if error else "对比完成",
            'prompt_tokens': 0,
            'completion_tokens': 0,
            'business_id': qwen_record.task.patient_id if qwen_record and hasattr(qwen_record, 'task') else None,
            'create_user': qwen_record.task.create_user if qwen_record and hasattr(qwen_record, 'task') else None,
            'create_name': qwen_record.task.create_name if qwen_record and hasattr(qwen_record, 'task') else None,
        }
        
        ModelInvocationLog.objects.create(**log_data)
        logger.info(f"✅ 病历小结对比日志记录完成，任务ID: {task_id}")
        
    except Exception as e:
        logger.error(f"❌ 记录病历小结对比日志失败: {e}")


def download_file(record, download_dir):
    """
    从 MinIO 下载文件到本地
    """
    local_path = os.path.join(download_dir, record.original_filename)
    client = get_minio_client()
    # ✅ 改为按位置传参
    download_file_from_minio(
        client,
        record.bucket_name,
        record.object_name,
        local_path
    )

    logger.info(f"✅ 文件 {record.original_filename} 下载成功")
    return local_path


def custom_compare(qwen_path, ds_path, output_dir):
    """
    使用你自己的对比逻辑生成两个高亮的新文件
    """
    output_qwen = os.path.join(output_dir, f"{uuid.uuid4()}_qwen3_highlighted.docx")
    output_ds = os.path.join(output_dir, f"{uuid.uuid4()}_DeepSeek_highlighted.docx")  # ✅ 修复点

    result_qwen, result_ds = generate_highlighted_docs(
        file_qwen=qwen_path,
        file_deepseek=ds_path,
        output_qwen=output_qwen,
        output_deepseek=output_ds
    )

    if not result_qwen or not result_ds:
        raise Exception("生成高亮文件失败")

    return result_qwen, result_ds


def update_record(record, new_file_path):
    """
    上传新文件并更新数据库记录
    """
    # 上传新文件到 MinIO
    _, ext = os.path.splitext(new_file_path)
    object_name = f"{uuid.uuid4().hex}{ext}"
    bucket_name = record.bucket_name
    client = get_minio_client()  # 获取 MinIO 客户端
    upload_file_to_minio(client, new_file_path, bucket_name, object_name)

    # 计算哈希
    with open(new_file_path, 'rb') as f:
        content = f.read()
    file_hash = hashlib.sha256(content).hexdigest()

    # 获取文件信息
    file_stat = os.stat(new_file_path)
    now = datetime.now()
    new_filename = f"modified_{record.original_filename}"

    # 更新数据库记录
    with connection.cursor() as cursor:
        sql = """
        UPDATE medical_collection_file SET
            original_filename = %s,
            object_name = %s,
            content_type = %s,
            size = %s,
            hash = %s,
            data_version = data_version + 1,
            update_time = %s
        WHERE id = %s
        """
        cursor.execute(sql, [
            new_filename,
            object_name,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # docx 类型
            file_stat.st_size,
            file_hash,
            now,
            record.id
        ])
        connection.commit()
        logger.info(f"✅ 记录 ID={record.id} 已更新为新文件 {new_filename}")


if __name__ == "__main__":
    task_id = 123  # 示例 task_id，你可以通过命令行传入
    process_and_update(task_id)